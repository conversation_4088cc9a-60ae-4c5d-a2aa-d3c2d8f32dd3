<p class="commentaireetape">V<PERSON> pouvez &agrave; pr&eacute;sent&nbsp;valider votre abonnement ou :</p>

<p class="commentaireetape"><u>Ajouter un abonnement</u><strong> </strong>en cliquant dans le menu de gauche<strong>&nbsp;</strong>sur <strong>"Abonnement (s) &nbsp;Suppl&eacute;mentaire (s)".</strong></p>

<p class="commentaireetape"><u>Modifier, ajouter ou supprimer un spectacle</u><strong> </strong>en&nbsp;cliquant en bas de la page sur&nbsp; <strong>"Etape pr&eacute;c&eacute;dente" </strong></p>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-13']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    </script>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-2']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    &lt;/script&gt;</body></html></body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html></script>