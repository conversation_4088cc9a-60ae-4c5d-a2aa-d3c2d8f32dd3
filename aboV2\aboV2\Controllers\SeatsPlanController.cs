﻿using aboV2.App_Code;
using aboV2.App_Code.crypto;
using aboV2.Models.Enums;
using aboV2.wcf_Themis;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;
using System.Web.Mvc;
using utilitaires2010;
using WebTracing2010;
using ws_bll;
using ws_bll.WT;
using ws_DTO.objets_liaisons;

namespace aboV2.Controllers
{


    /// <summary>
    /// objet de base : ligne de panier_entree ou panier_entree_abo
    /// </summary>
    [DataContract]
    [SerializableAttribute()]
    [KnownTypeAttribute(typeof(ws_DTO.SeatUnitSalesEntity))]
    [KnownTypeAttribute(typeof(ws_DTO.SeatAboEntity))]
    public class mySeatEntity
    {

        /// <summary>
        /// entree_id
        /// </summary>
        [DataMember]
        public int Seat_id { get; set; }

        /// <summary>
        /// rang
        /// </summary>
        [DataMember]
        public string Rank { get; set; }

        /// <summary>
        /// siege (du rang)
        /// </summary>
        [DataMember]
        public string Seat { get; set; }

        /// <summary>
        /// categorie_id
        /// </summary>

        [DataMember]
        public int Categ_id { get; set; }


        [DataMember]
        public string Categ_name { get; set; }

        /// <summary>
        /// type de siege "F", "SD", "SG" = fauteuil, strapontin gauche, strapontin droit
        /// </summary>
        [DataMember]
        public string type_siege { get; set; }


        /// <summary>
        /// denomination du siege (fauteuil, strapontin, chaise, banc, debout,  etc)
        /// </summary>

        [DataMember]
        public string Denomination_name { get; set; }

        /// <summary>
        /// denomination id du siege
        /// </summary>
        [DataMember]
        public int denom_id { get; set; }

        /// <summary>
        /// orientation du siege (nord N, sud S, etc)
        /// </summary>

        [DataMember]
        public string orientation { get; set; }

        [DataMember]
        public int Floor_id { get; set; }
        [DataMember]
        public string Floor_name { get; set; }

        [DataMember]
        public int Zone_id { get; set; }
        [DataMember]
        public string Zone_name { get; set; }

        [DataMember]
        public int Section_id { get; set; }
        [DataMember]

        public string Section_name { get; set; }

        /// <summary>
        /// coordonnée X abscisse
        /// </summary>
        [DataMember]
        public int Posx { get; set; }
        /// <summary>
        /// coordonnée Y ordonnée
        /// </summary>
        [DataMember]
        public int Posy { get; set; }

        /// <summary>
        /// decalage pixel en abscisse par rapport à la grille 
        /// </summary>
        [DataMember]
        public int Decalx { get; set; }

        /// <summary>
        /// decalage pixel en ordonnée par rapport à la grille 
        /// </summary>
        [DataMember]
        public int Decaly { get; set; }


        [DataMember]
        public int Reserve_id { get; set; }
        [DataMember]
        public string Reserve_name { get; set; }

        /// <summary>
        /// libre =1, non libre =0
        /// </summary>
        [DataMember]
        public int Free { get; set; }

        /// <summary>
        /// siege flagé par moi 
        /// </summary>
        [DataMember]
        public string isMine { get; set; }

        /// <summary>
        /// placement libre ou non
        /// </summary>
        [DataMember]
        public bool isPlacementLibre { get; set; }


        [DataMember]
        public int bordureTop { get; set; }
        /// <summary>
        /// bordure right 0 ou 1
        /// </summary>
        [DataMember]
        public int bordureRight { get; set; }
        /// <summary>
        /// bordure bottom 0 ou 1
        /// </summary>
        [DataMember]
        public int bordureBottom { get; set; }
        /// <summary>
        /// bordure left 0 ou 1
        /// </summary>
        [DataMember]
        public int bordureLeft { get; set; }
        /// <summary>
        /// couleur de la bordure 0 = noir, 1 = rouge
        /// </summary>
        [DataMember]
        public int bordureColor { get; set; }


        /// <summary>
        /// hash calculé à partir du seatId
        /// </summary>
        [DataMember]
        public string hashKey { get; set; }

        public static explicit operator mySeatEntity(ws_DTO.SeatEntity v)
        {

            mySeatEntity myS = new mySeatEntity();
            myS.Seat_id = v.Seat_id;
            myS.Categ_id = v.Categ_id;
            myS.Categ_name = v.Categ_name;
            myS.Denomination_name = v.Denomination_name;
            myS.denom_id = v.denom_id;
            myS.Floor_id = v.Floor_id;
            myS.Floor_name = v.Floor_name;
            myS.Free = v.Free;
            myS.hashKey = v.hashKey;
            myS.isMine = v.isMine;
            myS.isPlacementLibre = v.isPlacementLibre;
            myS.orientation = v.orientation;
            myS.Posx = v.Posx;
            myS.Posy = v.Posy;
            myS.Rank = v.Rank;
            myS.Seat = v.Seat;
            myS.Reserve_id = v.Reserve_id;

            myS.Section_id = v.Section_id;
            myS.Section_name = v.Section_name;

            myS.Zone_id = v.Zone_id;
            myS.Zone_name = v.Zone_name;
            myS.type_siege = v.type_siege;

            myS.bordureBottom = v.bordureBottom;
            myS.bordureTop = v.bordureTop;
            myS.bordureLeft = v.bordureLeft;
            myS.bordureRight = v.bordureRight;
            myS.bordureColor = v.bordureColor;

            myS.Decalx = v.Decalx;
            myS.Decaly = v.Decaly;


            return myS;
            //   throw new NotImplementedException();
        }
    }

    public class SeatsPlanController : BaseController
    {
        private string thisSalt = "Ab0V2";
        // GET: SeatsPlan
        public ActionResult partIndex()
        {
            try
            {
                int structureId = int.Parse(RouteData.Values["structureId"].ToString());
                int identityId = int.Parse(RouteData.Values["identiteId"].ToString());

                int sessionId = int.Parse(RouteData.Values["sessionId"].ToString());
                ViewBag.sessionId = sessionId;

                int eventId = int.Parse(RouteData.Values["eventId"].ToString());
                ViewBag.eventId = eventId;

                int categId = int.Parse(RouteData.Values["categId"].ToString());
                ViewBag.categId = categId;

                string typeHorsAbo = RouteData.Values["typeHorsAbo"].ToString();
                ViewBag.typeHorsAbo = typeHorsAbo;
                string strListGestionPlaceId = RouteData.Values["lstGestionPlaceId"].ToString();
                ViewBag.lstGestionPlaceId = strListGestionPlaceId;

                int zoneId = int.Parse(RouteData.Values["zoneId"].ToString());
                ViewBag.zoneId = zoneId;
                int floorId = int.Parse(RouteData.Values["floorId"].ToString());
                ViewBag.floorId = floorId;
                int sectionId = int.Parse(RouteData.Values["sectionId"].ToString());
                ViewBag.sectionId = sectionId;

                ViewBag.aboIdx = 0; //int.Parse(RouteData.Values["aboidx"].ToString());
                int nbSeats = int.Parse(RouteData.Values["nbSeats"].ToString());
                ViewBag.nbSeats = nbSeats;

                int sessionIdOrigine = int.Parse(RouteData.Values["sessionIdOrigine"].ToString());
                ViewBag.sessionIdOrigine = sessionIdOrigine;

                string langCode = RouteData.Values["langCode"].ToString();
                var iGestionPlacesId = strListGestionPlaceId?.Split(',')?.Select(Int32.Parse)?.ToList();
                var sessionsForChange = SeanceManager.GetSeancesCommunesAbo(structureId, eventId, iGestionPlacesId, nbSeats);
                List<int> formulesId = GestionPlaceManager.GetFormulesIdByGestionPlacesId(structureId, iGestionPlacesId);

                List<SessionForChangeSelect> sessionsForChangeForSelect = new List<SessionForChangeSelect>();

                foreach (var sess in sessionsForChange)
                {
                    var sessForChange = new SessionForChangeSelect()
                    {
                        SessionId = sess.SessionId,
                        SessionStartDate = sess.SessionStartDate,
                        UrlOfSession = $"/seatsPlan/partIndex/{structureId}/0/{eventId}/{sess.SessionId}/{categId}/{langCode}/{typeHorsAbo}/{strListGestionPlaceId}/{zoneId}/{floorId}/{sectionId}/{nbSeats}/{sessionIdOrigine}"
                    };

                    sessionsForChangeForSelect.Add(sessForChange);
                }

                ViewBag.SessionForChangeSelect = sessionsForChangeForSelect;
                ViewBag.FormulesId = formulesId;

                var infosOfsessionOrigin = SeanceManager.GetById(structureId, sessionIdOrigine);

                if (infosOfsessionOrigin != null)
                    ViewBag.SessionStartDateOrigine = infosOfsessionOrigin.SessionStartDate;


            }
            catch (Exception ex)
            {
            }
            return PartialView("~/Views/SeatsPlan/vSeatsPlan.cshtml");


        }
        private string getHash(string tohash, string salt)
        {
            Sha1 sha1 = new Sha1(tohash + salt);
            return sha1.getSha1();
        }

        [Throttle(Name = "Load seat Plan", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 1000)]
        public JsonResult Load(int structureId, int identiteId, int eventId, int sessionId, int categId, string langCode, string typeHorsAbo, List<int> lstGestionPlaceId, int zoneId, int floorId, int sectionId)
        {

            logger.Debug("SeatPlan Load " + identiteId + " " + eventId + " " + sessionId + " " + categId + " " + langCode + " " + typeHorsAbo + " " + string.Join(".", lstGestionPlaceId) + "," + zoneId + "," + floorId + "," + sectionId);
            GestionTrace.WriteLog(structureId, "SeatPlan Load(" + structureId + "," + identiteId + "," + eventId + "," + sessionId + "," + categId + "," + langCode + "," + zoneId + "," + floorId + "," + sectionId + ")");

            wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
            List<ws_DTO.SeatEntity> listSeats = wcfThemis.LoadSeats(structureId, eventId, sessionId, identiteId, "", langCode).ToList();

            List<ws_DTO.ReserveEntity> listReserves = new List<ws_DTO.ReserveEntity>();
            
           // if (typeHorsAbo == "HA")
            //{
                listReserves = wcfThemis.GetReserveList(structureId, lstGestionPlaceId.ToArray()).ToList();
            //}
            //else
            //{
            //    listReserves = wcfThemis.GetListeReserveForFormula(structureId, "", 0, 0, eventId, sessionId).ToList();
            //}


            logger.Debug("SeatPlan Load " + listReserves.Count);

            List<int> listReservesId = listReserves.Select(r => r.reserve_id).ToList();
            /* foreach (ws_DTO.ReserveEntity res in listReserves)
             {
                 listReservesId.Add(res.reserve_id);
             }*/

            List<mySeatEntity> listSeatsEpurees = new List<mySeatEntity>();

            foreach (ws_DTO.SeatEntity seat in listSeats)
            {
                //if (seat.Free == 1)
                if (seat.Categ_id != categId || !listReservesId.Contains(seat.Reserve_id))
                {
                    seat.Free = 0;
                }
                else
                {
                    seat.hashKey = getHash(seat.Seat_id + "/" + sessionId, thisSalt);
                }
                bool addThisSeat = true;
                if (sectionId != 0 && seat.Section_id != sectionId)
                {
                    addThisSeat = false;
                }
                if (floorId != 0 && seat.Floor_id != floorId)
                {
                    addThisSeat = false;
                }
                if (zoneId != 0 && seat.Zone_id != zoneId)
                {
                    addThisSeat = false;
                }
                if (addThisSeat)
                {
                    mySeatEntity myS = (mySeatEntity)seat;
                    listSeatsEpurees.Add(myS);
                }
            }

            //string startingWithA = JsonConvert.SerializeObject(listSeats, Formatting.Indented,
            //new JsonSerializerSettings { ContractResolver = new DynamicContractResolver('A') });


            return Json(listSeatsEpurees, JsonRequestBehavior.AllowGet);
            // return startingWithA;

            // return PartialView("~/Views/SeatsPlan/vSeatsPlan.cshtml");
        }



        [Throttle(Name = "Load seat Plan Text", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 1000)]
        public JsonResult LoadTexts(int structureId, int identiteId, int eventId, int sessionId, int zoneId, int floorId, int sectionId)
        {

            mlogger log = new mlogger();

            GestionTrace.WriteLog(structureId, "SeatPlan LoadTexts(" + structureId + "," + identiteId + "," + eventId + ", " + sessionId + ", " + zoneId + ", " + floorId + ", " + sectionId + ")");
            log.LogMsg(structureId, LogLevel.INFO, "SeatPlan LoadTexts(" + structureId + "," + identiteId + "," + eventId + "," + sessionId + "," + zoneId + "," + floorId + ", " + sectionId + ") ");

            List<ws_DTO.TextEntity> listSeats = new List<ws_DTO.TextEntity>();
            string cacheName = "LoadTexts" + structureId + "." + identiteId + "_" + sessionId + "_" + zoneId + "_" + floorId + "_" + sectionId;

            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                listSeats = wcfThemis.LoadSeatsTexts(structureId, eventId, sessionId, zoneId, floorId, sectionId).ToList();
                System.Web.HttpContext.Current.Cache.Insert(cacheName, listSeats, null, DateTime.Now.AddSeconds(60 * 60), TimeSpan.Zero);

                GestionTrace.WriteLog(structureId, "SeatPlan listSeats " + listSeats.Count);
                log.LogMsg(structureId, LogLevel.INFO, "SeatPlan listSeats " + listSeats.Count);

            }
            else
            {
                listSeats = (List<ws_DTO.TextEntity>)System.Web.HttpContext.Current.Cache[cacheName];

                GestionTrace.WriteLog(structureId, "SeatPlan listSeats cache " + listSeats.Count);
                log.LogMsg(structureId, LogLevel.INFO, "SeatPlan listSeats cache " + listSeats.Count);
            }

            return Json(listSeats, JsonRequestBehavior.AllowGet);
            // return PartialView("~/Views/SeatsPlan/vSeatsPlan.cshtml");
        }


        [Throttle(Name = "validationReFlag seat Plan", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        public JsonResult validationReFlag(int structureId, int identiteId, int eventId, int sessionId, int sessionIdOrigine, List<SeatEntity> newSeats, List<SeatEntity> oldSeats)
        {
            mlogger log = new mlogger();

            GestionTrace.WriteLog(structureId, "validationReFlag start(" + structureId + "," + identiteId + "," + eventId + "," + sessionId + "," + sessionIdOrigine);
            log.LogMsg(structureId, LogLevel.INFO, "validationReFlag start(" + structureId + "," + identiteId + "," + eventId + "," + sessionId + "," + sessionIdOrigine);

            List<SeatEntity> seatReturn = new List<SeatEntity>();
            if (System.Web.HttpContext.Current.Session["currIdUser"] != null)
            {
                int userId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());

                List<int> listToUnflag = new List<int>();
                List<int> listToReflag = new List<int>();

                #region controle des hashKey

                GestionTrace.WriteLog(structureId, "validationReFlag control hashKey");
                log.LogMsg(structureId, LogLevel.INFO, "validationReFlag control hashKey");

                foreach (SeatEntity s in newSeats)
                {
                    if (s.hashKey != getHash(s.Seat_id + "/" + sessionId, thisSalt))
                    {
                        var result = new
                        {
                            Code = (int)ErrorsCode.HashNewSeatsNotValid,
                            StrCode = Initialisations.GetDescription(ErrorsCode.HashNewSeatsNotValid),
                            Comment = "Hash new seats not valid"
                        };
                        return Json(result, JsonRequestBehavior.AllowGet);
                    }
                }
                foreach (SeatEntity s in oldSeats)
                {
                    if (s.hashKey != getHash(s.Seat_id + "/" + sessionIdOrigine, thisSalt))
                    {

                        var result = new
                        {
                            Code = (int)ErrorsCode.HashOldSeatsNotValid,
                            StrCode = Initialisations.GetDescription(ErrorsCode.HashOldSeatsNotValid),
                            Comment = "Hash old seats not valid"
                        };
                        return Json(result, JsonRequestBehavior.AllowGet);
                    }
                }
                #endregion

                foreach (SeatEntity s in oldSeats)
                {
                    if (newSeats.Where(x => x.Seat_id == s.Seat_id).ToList().Count() == 0)
                    {
                        listToUnflag.Add(s.Seat_id);
                    }
                }
                foreach (SeatEntity s in newSeats)
                {
                    listToReflag.Add(s.Seat_id);
                }

                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();

                List<ws_DTO.SeatEntity> listReflag = wcfThemis.ReFlagSeatsManuel_tempo(structureId, eventId, sessionId, listToReflag.ToArray(), userId).ToList();

                //si la liste à reflagguée est la même que le retour de la liste qui vient d'être flaguée (même nombre)
                if (listReflag.Count == listToReflag.Count)
                {
                    List<ws_DTO.SeatEntity> listUnflaged = wcfThemis.UnFlagSeatsManuel_tempo(structureId, eventId, sessionIdOrigine, listToUnflag.ToArray(), userId).ToList();
                    if (listUnflaged.Count == listToUnflag.Count)
                    {
                        foreach (ws_DTO.SeatEntity dtost in listReflag)
                        {
                            SeatEntity st = new SeatEntity();
                            st.Seat_id = dtost.Seat_id;

                            seatReturn.Add(st);
                        }
                    }
                    else
                    {
                        GestionTrace.WriteLog(structureId, "validationReFlag start(" + structureId + "," + identiteId + "," + eventId + "," + sessionId + "," + sessionIdOrigine);
                        log.LogMsg(structureId, LogLevel.INFO, "validationReFlag start(" + structureId + "," + identiteId + "," + eventId + "," + sessionId + "," + sessionIdOrigine);

                    }

                }
                else
                {


                    GestionTrace.WriteLog(structureId, "nombre de places passé en paramètre est différent du nombre flaggué ");
                    log.LogMsg(structureId, LogLevel.INFO, "validationReFlag start(" + structureId + "," + identiteId + "," + eventId + "," + sessionId + "," + sessionIdOrigine);

                    var result = new
                    {
                        Code = (int)ErrorsCode.CantReflagSeats,
                        StrCode = Initialisations.GetDescription(ErrorsCode.CantReflagSeats),
                        Comment = "Seats not reflag"
                    };
                    return Json(result, JsonRequestBehavior.AllowGet);

                }

            }

            return Json(seatReturn, JsonRequestBehavior.AllowGet);
        }

        [Throttle(Name = "FlagTemp seat Plan", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        /// <summary>
        /// Flag sur click image
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="seatId"></param>
        /// <param name="hashEntree"></param>
        /// <returns></returns>       
        public JsonResult FlagTemp(int structureId, int identiteId, int eventId, int sessionId, int seatId, string hashEntree)
        {
            if (System.Web.HttpContext.Current.Session["currIdUser"] != null)
            {
                int userId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                ws_DTO.SeatEntity seatReturn = new ws_DTO.SeatEntity();
                string calcKey = getHash(seatId + "/" + sessionId, thisSalt);

                if (calcKey == hashEntree)
                {
                    try
                    {
                        ws_DTO.SeatEntity seatFlag = wcfThemis.FlagManuel_tempo(structureId, eventId, sessionId, seatId, userId);
                        //List<SeatEntity> listSeats = wcfThemis.LoadSeats(structureId, eventId, sessionId, identiteId, "", langCode).ToList();
                        if (seatFlag != null && seatFlag.Seat_id == seatId)
                        {
                            seatReturn = seatFlag;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Error("error SeatsPlan - FlagTemp : " + ex.Message + " " + ex.StackTrace);
                        GestionTrace.WriteLogError(structureId, "error SeatsPlan - FlagTemp : " + ex.Message);
                        throw new Exception(ex.Message);
                    }


                }
                return Json(seatReturn, JsonRequestBehavior.AllowGet);
            }
            else
            {

                var result = new
                {
                    Code = (int)ErrorsCode.NotInitialized,
                    StrCode = Initialisations.GetDescription(ErrorsCode.NotInitialized),
                    Comment = "Not initialized"
                };
                return Json(result, JsonRequestBehavior.AllowGet);

                // Exception ex = new Exception("FlagTemp:SessionLost");
                // throw ex;
            }
            // return PartialView("~/Views/SeatsPlan/vSeatsPlan.cshtml");
        }


        [Throttle(Name = "UnFlagTemp seat Plan", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        /// <summary>
        /// unFlag sur click image
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="seatId"></param>
        /// <param name="hashEntree"></param>
        /// <returns></returns>
        public JsonResult UnFlagTemp(int structureId, int identiteId, int eventId, int sessionId, int seatId, string hashEntree)
        {
            if (System.Web.HttpContext.Current.Session["currIdUser"] != null)
            {
                int userId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                ws_DTO.SeatEntity seatReturn = new ws_DTO.SeatEntity();
                string calcKey = getHash(seatId + "/" + sessionId, thisSalt);

                if (calcKey == hashEntree)
                {
                    ws_DTO.SeatEntity seatFlag = wcfThemis.UnFlagManuel_tempo(structureId, eventId, sessionId, seatId, userId);
                    //List<SeatEntity> listSeats = wcfThemis.LoadSeats(structureId, eventId, sessionId, identiteId, "", langCode).ToList();
                    if (seatFlag != null && seatFlag.Seat_id == seatId)
                    {
                        seatReturn = seatFlag;
                    }

                }
                return Json(seatReturn, JsonRequestBehavior.AllowGet);
            }
            else
            {
                var result = new
                {
                    Code = (int)ErrorsCode.NotInitialized,
                    StrCode = Initialisations.GetDescription(ErrorsCode.NotInitialized),
                    Comment = "Not initialized"
                };
                return Json(result, JsonRequestBehavior.AllowGet);
                //Exception ex = new Exception("UnFlagTemp:SessionLost");
                //throw ex;
            }
            // return PartialView("~/Views/SeatsPlan/vSeatsPlan.cshtml");
        }



        public JsonResult UnFlagAndUnFlagTemp(int structureId, int identiteId, List<customEvent> lstSessionsToUnFlagged)
        {

            mlogger log = new mlogger();

            GestionTrace.WriteLog(structureId, "UnFlagAndUnFlagTemp start " + identiteId);
            log.LogMsg(structureId, LogLevel.INFO, "UnFlagAndUnFlagTemp start " + identiteId);


            if (System.Web.HttpContext.Current.Session["currIdUser"] != null)
            {
                int userId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());

                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                ws_DTO.SeatEntity seatReturn = new ws_DTO.SeatEntity();

                foreach (customEvent itemsession in lstSessionsToUnFlagged)
                {
                    foreach (customFlaggedSeat itemSeat in itemsession.listSeats)
                    {
                        string calcKey = getHash(itemSeat.seatid + "/" + itemsession.sessionid, thisSalt);

                        if (calcKey == itemSeat.hashKey)
                        {
                            // ws_DTO.SeatEntity seatFlag = wcfThemis.UnFlagManuel_tempo(structureId, itemsession.eventid, itemsession.sessionid, itemSeat.seatid, userId);
                            ws_DTO.SeatEntity seatFlag = wcfThemis.UnFlagSeatsManuel(structureId, itemsession.eventid, itemsession.sessionid, itemSeat.seatid, userId);
                            if (seatFlag != null && seatFlag.Seat_id == itemSeat.seatid)
                            {
                                seatReturn = seatFlag;
                            }

                            GestionTrace.WriteLog(structureId, "UnFlagAndUnFlagTemp  " + seatReturn);
                            log.LogMsg(structureId, LogLevel.INFO, "UnFlagAndUnFlagTemp  " + seatReturn);
                        }
                        GestionTrace.WriteLogError(structureId, "UnFlagAndUnFlagTemp  " + calcKey + " != " + itemSeat.hashKey);
                        log.LogMsg(structureId, LogLevel.ERROR, "UnFlagAndUnFlagTemp  " + calcKey + " != " + itemSeat.hashKey);

                    }

                }

                string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];

                bool isInvalidBasket = BasketsManager.SetInvalide(userId, identiteId, typeRun, structureId);

                if (!isInvalidBasket)
                {
                    GestionTrace.WriteLogError(structureId, "UnFlagAndUnFlagTemp  " + isInvalidBasket);
                    log.LogMsg(structureId, LogLevel.ERROR, "UnFlagAndUnFlagTemp  " + isInvalidBasket);
                }

                /*
                if (calcKey == hashEntree)
                {
                    ws_DTO.SeatEntity seatFlag = wcfThemis.UnFlagManuel_tempo(structureId, eventId, sessionId, seatId, userId);
                    //List<SeatEntity> listSeats = wcfThemis.LoadSeats(structureId, eventId, sessionId, identiteId, "", langCode).ToList();
                    if (seatFlag != null && seatFlag.Seat_id == seatId)
                    {
                        seatReturn = seatFlag;
                    }

                }*/
                return Json(seatReturn, JsonRequestBehavior.AllowGet);
            }
            else
            {

                GestionTrace.WriteLog(structureId, "UnFlagAndUnFlagTemp  SessionLost");
                log.LogMsg(structureId, LogLevel.ERROR, "UnFlagAndUnFlagTemp  SessionLost");

                var result = new
                {
                    Code = (int)ErrorsCode.NotInitialized,
                    StrCode = Initialisations.GetDescription(ErrorsCode.NotInitialized),
                    Comment = "Not initialized"
                };
                return Json(result, JsonRequestBehavior.AllowGet);
                //Exception ex = new Exception("UnFlagAndUnFlagTemp:SessionLost");
                //throw ex;
            }
            // return PartialView("~/Views/SeatsPlan/vSeatsPlan.cshtml");
        }





        public JsonResult UnFlagListSeats(int structureId, int identiteId, List<customEvent> lstCustomSessionAndSeat)
        {
            mlogger log = new mlogger();

            GestionTrace.WriteLog(structureId, "UnFlagListSeats start " + identiteId);
            log.LogMsg(structureId, LogLevel.INFO, "UnFlagListSeats start " + identiteId);


            string strReturn = "KO";

            if (System.Web.HttpContext.Current.Session["currIdUser"] != null)
            {
                try
                {

                    int userId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());
                    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();

                    foreach (customEvent itemsession in lstCustomSessionAndSeat)
                    {
                        List<int> lstseatid = itemsession.listSeats.Select(s => s.seatid).ToList();
                        List<ws_DTO.SeatEntity> listUnflaged = wcfThemis.UnFlagSeatsManuel_tempo(structureId, itemsession.eventid, itemsession.sessionid, lstseatid.ToArray(), userId).ToList();

                        if (listUnflaged != null)
                        {
                            strReturn = "OK";

                        }
                        GestionTrace.WriteLog(structureId, "UnFlagSeatsManuel_tempo " + strReturn);
                        log.LogMsg(structureId, LogLevel.INFO, "UnFlagSeatsManuel_tempo " + strReturn);
                    }

                    // return Json(new { panier = thisCurrentBasket, customBasket = custBasket }, JsonRequestBehavior.AllowGet);
                    return Json(strReturn, JsonRequestBehavior.AllowGet);

                }
                catch (Exception ex)
                {
                    logger.Error("error SeatsPlan - UnFlagListSeats : " + ex.Message + " " + ex.StackTrace);
                    GestionTrace.WriteLogError(structureId, "error UnFlagListSeats - UnFlagSeatsManuel_tempo : " + ex.Message);
                    throw new Exception(ex.Message);
                }


            }
            else
            {
                Exception ex = new Exception("UnFlagListSeats:SessionLost");

                GestionTrace.WriteLog(structureId, "UnFlagListSeats  SessionLost");
                log.LogMsg(structureId, LogLevel.INFO, "UnFlagListSeats  SessionLost ");
                throw ex;
            }


        }



    }
    public class DynamicContractResolver : DefaultContractResolver
    {
        private readonly char _startingWithChar;

        public DynamicContractResolver(char startingWithChar)
        {
            _startingWithChar = startingWithChar;
        }

        protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
        {
            IList<JsonProperty> properties = base.CreateProperties(type, memberSerialization);

            // only serializer properties that start with the specified character
            properties =
                properties.Where(p => p.PropertyName.StartsWith(_startingWithChar.ToString())).ToList();

            return properties;
        }
    }
}