﻿<?xml version="1.0" encoding="UTF-8" ?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
<xsl:variable name="unique-listTypeTarifId" select="//type_tarif_id[not(.=following::type_tarif_id)]" />

<xsl:template match="/">
<div class="table">
	<table class="table table-condensed" id="TableGrilleTarifs" width="100%">
		<xsl:for-each select="SESSION">
			<xsl:variable name="nodeSession" select="."/> 
			<tr class="GrilleTarifDateSeance">
				<td id="GrilleTarifNameVide"></td>
				<th colspan="10">
					Séance du <xsl:value-of select="seance_date_deb"/>
				</th>
			</tr>
			<tr id="GrilleTarifName">
				<td id="GrilleTarifNameVide"></td>
				<xsl:for-each select="$unique-listTypeTarifId">
					<xsl:variable name="vPosTarifId" select="."/>
					<td class="GrilleTarifName">
						<xsl:variable name="vPos" select="."/>
						<xsl:value-of select="$nodeSession/ZONE/FLOOR/SECTION/CATEGORY/PRICELIST[type_tarif_id=$vPosTarifId]/type_tarif_nom"/>
					</td>
				</xsl:for-each>	
			</tr>
		
			
				
				<xsl:for-each select="$nodeSession/ZONE/FLOOR/SECTION/CATEGORY/categorie_id">
					<xsl:variable name="vPosCatId" select="."/>
					<tr>
						<td class="GtCategName">
							<xsl:value-of select="../categ_nom"/><span class="seatimage legend">c<xsl:value-of select="$vPosCatId"/></span>

						</td>
						
						<!--td class="GtCategName">
							<xsl:value-of select="../categ_nom"/>
						</td-->
						<xsl:for-each select="$unique-listTypeTarifId">
						<td class="colonneGrilleTarifCategName">
							<xsl:variable name="vPosTarifId" select="."/>
							<xsl:for-each select="$nodeSession/ZONE/FLOOR/SECTION/CATEGORY[categorie_id=$vPosCatId]/PRICELIST[type_tarif_id=$vPosTarifId]/montant">				
								<xsl:sort select="montant" data-type="number"/>
								<xsl:if test="position() = last()">
									<xsl:variable name="vMontant" select="."/>
									<xsl:variable name="vTaxe" select="../taxe"/>
									<div align="center"><xsl:value-of select="($vMontant + $vTaxe) div 100"/> €</div>
								</xsl:if>
						
							</xsl:for-each>
						</td>			
						</xsl:for-each>

					
					</tr>
				</xsl:for-each>
		</xsl:for-each>
    </table>
</div>
</xsl:template>

</xsl:stylesheet>