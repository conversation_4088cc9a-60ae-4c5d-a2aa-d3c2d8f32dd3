﻿using aboV2.wcf_Themis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ws_DTO;

namespace aboV2.App_Code
{

    public class customBasket
    {
        /// <summary>
        /// list des formules prises
        /// </summary>
        public List<customFormulas> listFormulas { get; set; }


        /// <summary>
        /// list des events Hors Abo
        /// </summary>
        public List<customEventHA> listEventsHA { get; set; }

        public ws_DTO.ProductFraisEnvois mobtention { get; set; }

        public List<ws_DTO.ProductFraisDossier> proddossier { get; set; }

        public List<ws_DTO.ProductEntity> prodQuestionnaire { get; set; }


        public List<CustomErrorsFlags> messageError { get; set; }

        //si le panier est en création true sinon panier récupérer de la bdd
        public bool IsCreateBasket { get; set; }

        public string StrDateCreateBasket { get; set; }

    }

    public class customFormulas
    {
        public string formulaName { get; set; }

        public int formulaid { get; set; }

        /// <summary>
        /// nbr de spectacles max sur la formule
        /// </summary>
        public int nbmanifmax { get; set; }


        /// <summary>
        /// nbr de spectacles min sur la formule
        /// </summary>
        public int nbmanifmin { get; set; }


        public List<customTarif> listTarifs { get; set; }
    }

    /// <summary>
    /// choix places hors abo
    /// </summary>

    public class customEventHA
    {
        //public int categoryid { get; set; }
        public int sessionid { get; set; }
        public int eventid { get; set; }
        public int aboidx { get; set; }
        public string eventName { get; set; }
        public string sessionDescription { get; set; }

        public int categid { get; set; }

        public string categoryName { get; set; }

        public string lieuName { get; set; }


        public int zoneid { get; set; }
        public int floorid { get; set; }
        public int sectionid { get; set; }

        public string floorName { get; set; }

        public string sectionName { get; set; }

        /// <summary>
        /// list des gp id demandés
        /// </summary>
        public List<customTarifHA> listTarifs { get; set; }

        public string type { get; set; }

        public string typeName { get; set; }
        public string SessionStartDate { get; set; }
    }
    public class customTarifHA
    {
        public int nb { get; set; }
        public int gpid { get; set; }

        public int categoryid { get; set; }

        public int priceId { get; set; }


        //public int priceid { get; set; }

        public int vtsId { get; set; }

        public string pricename { get; set; }

        public int unitTTCamount { get; set; }

        public string hashKey { get; set; }

        public bool isChoixPlaceSurPlan { get; set; }
        public bool isPlacementLibre { get; set; }
        public bool isVoirPlacement { get; set; }

        public List<customFlaggedSeat> listSeats { get; set; }
    }

    /// <summary>
    /// objet demande Hors Abo recu en parametre
    /// </summary>
    public class customChoiceHA_recu
    {
        public List<customEventHA> listEventsHA { get; set; }
    }


    public class customTarif
    {
        public int nb { get; set; }

        public string tarifName { get; set; }

        public int tarifid { get; set; }

        public List<customAbo> listAbos { get; set; }

        public string hashKey { get; set; }
    }
    public class customAbo
    {
        public int formulaid { get; set; }

        public int idx { get; set; }

        public int tarifid { get; set; }

        /// <summary>
        /// identite reliée à l'abo
        /// </summary>
        public int identiteId { get; set; }

        /// <summary>
        /// hash Key de l'identité reliée à l'abo
       //// </summary>
        public string hk { get; set; }


        public List<customEvent> listEvents { get; set; }

        public List<ws_DTO.ProductPur> listmandatoryproducts { get; set; }

    }

    public class customEvent
    {
        public int aboidx { get; set; }
        public int eventid { get; set; }
        public int sessionid { get; set; }

        public int constraintId { get; set; }

        public int categid { get; set; }

        public string lieuName { get; set; }

        public string eventName { get; set; }
        public string sessionDescription { get; set; }
        public string categoryName { get; set; }

        public int unitTTCamount { get; set; }

        public int ischoixplacesurplan { get; set; }
        public int isplacementlibre { get; set; }
        public int isvoirplacement { get; set; }

        public int zoneId { get; set; }

        public string zoneName { get; set; }

        public int floorId { get; set; }
        public string floorName { get; set; }
        public int sectionId { get; set; }
        public string sectionName { get; set; }

        public string SessionStartDate { get; set; }

        public int GestionPlaceId { get; set; }

        public string TypePrise { get; set; }
        public string TypePriseName { get; set; }
        public List<customFlaggedSeat> listSeats { get; set; }

    }
    public class customFlaggedSeat
    {
        public int seatid { get; set; }
        public string rank { get; set; }
        public string seat { get; set; }

        public string hashKey { get; set; }
    }

    public class FormulaWithEvents
    {
        public int formulaId { get; set; }

        public int tarifId { get; set; }

        public List<ws_DTO.EventEntityOfFormula> listEvents { get; set; }
    }

    public class FormulaWithConstraints
    {
        public int formulaId { get; set; }

        public List<ws_DTO.FormulaContraintesGroupeOpenEntity> listConstraints { get; set; }
    }


    public class CustomErrorsFlags
    {

        public string EventName { get; set; }
        public string SessionDescription { get; set; }
        public string CategoryName { get; set; }
        public int EventId { get; set; }
        public int SessionId { get; set; }
        public int CategoryId { get; set; }

    }
}