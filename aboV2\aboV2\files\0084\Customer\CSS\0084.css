#tblOrderList2 ul {
    list-style: none;
}

#tblOrderList2 ul li span {
    margin: 0 30px 0 30px;
}

#captchaContainer {
    margin: 0 auto;
    width: 304px;
}

.panel-primary {
    border-color: #007F8B;
}

.panel-primary>.panel-heading {
    background-color: #007F8B;
    border-color: #007F8B;
}

.top-navigation .navbar-brand {
    background: #007F8B;
    color: #fff;
    padding: 15px 25px;
}

.btn-success {
    background-color: #007F8B;
    border-color: #007F8B;
    color: #fff;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success,
.btn-success:active:focus,
.btn-success:active:hover,
.btn-success.active:hover,
.btn-success.active:focus {
    background-color: #F5406D;
    border-color: #F5406D;
    color: #fff;
}

.top-navigation .nav>li a:hover,
.top-navigation .nav>li a:focus {
    background: #fff;
    color: #007F8B;
}

#ctPlaceHold_WctrlLoginConnect1_LnkBtnPassForget {
    background-color: transparent;
    border: none;
    color: #000000;
    display: inline;
    text-decoration: underline;
}

.nav-pills>li.active>a,
.nav-pills>li.active>a:focus,
.nav-pills>li.active>a:hover {
    color: #fff;
    background-color: #007F8B;
}