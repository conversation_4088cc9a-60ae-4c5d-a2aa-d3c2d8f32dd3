﻿$(document).ready(function () {

    if (sessionStorage.getItem("structureIdCurrent") != null)
        structureIdCurrent = sessionStorage.getItem("structureIdCurrent");

    LoadDataTable();

});


function LoadDataTable() {



    var table = $('#formulasTable').DataTable({
      //  "processing": true,
       // "serverSide": true,
        "bDestroy": true,
        //  "bJQueryUI": true,
        "order": [[1, 'asc']],
        "ajax": {
            'type': 'POST',
            'url': '/formulas/Load',
            "dataSrc": '',
            'data': {
                structureId: structureIdCurrent,
                identiteId: 0,
                langCode: 'fr'
                // etc..
            },
        },
        "iDisplayLength": 15,
        "columns": [
            {
                "class": 'details-control',
                "orderable": false,
                "data": null,
                "defaultContent": ''
            },
          
            { "data": "formulaName" },
            { "data": "formulaCode" },
            { "data": "formulaId" },
          /*  { "data": "StrDateCreationDossier" },
            { "data": "StrDateOperationDossier" },
            { "data": "DossierStatus.NomDossierStatus" },
            { "data": "TaskDossier.NomTaskDossier" },
            {
                "data": "IdDossier",
                render: function (data, type, row) {
                    return "<label class='label label-success'>" + row.DossierStatus.NomDossierStatus + "</label><div class='progress progress-xs' data-progressbar-value='" + row.DossierStatus.PercentDossierStatus + "'><div class='progress-bar'></div></div>";
                }
            },

            {
                "data": "IsValidDossier",
                render: function (data, type, row) {

                    if (row.IsValidDossier)
                        return '<i class="fa fa-circle txt-color-darken font-xs"></i> <span> Oui</span>';
                    else
                        return '<i class="fa fa-circle txt-color-danger font-xs"></i> <span Non </span>';
                }
            },



            { "data": "IdDossier" },
            { "data": "IdDossier" },*/
        ]
    });


    // Add event listener for opening and closing details
    $('#formulasTable tbody').on('click', 'td.details-control', function () {
        var tr = $(this).closest('tr');
        var row = table.row(tr);

        if (row.child.isShown()) {
            // This row is already open - close it
            row.child.hide();
            tr.removeClass('shown');
        } else {
            // Open this row
            row.child(format(row.data())).show();
            tr.addClass('shown');
        }
    });
}

function format(d) {
    // `d` is the original data object for the row

    var htmlDetailTable = '<table cellpadding="5" cellspacing="0" border="0" class="table table-hover table-condensed">';

    htmlDetailTable += '<tr><td><ul>';

    $.each(d.listPrices, function (indx, item) {
       // htmlDetailTable += '<li><input type="number" data_priceId="' + item.PriceId + '"/> ' + item.Price_name + ' ' + item.PriceId + '</li>';

        
        htmlDetailTable += '<input class="touchspin1" type="text" value="" name="demo1">';
    });

    htmlDetailTable += '</ul></td></tr>';


    htmlDetailTable += '</table>';


   /* return '<table cellpadding="5" cellspacing="0" border="0" class="table table-hover table-condensed">' +
        '<tr>' +
        '<td style="width:100px">Project Title:</td>' +
        '<td>' + d.name + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td>Deadline:</td>' +
        '<td>' + d.ends + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td>Extra info:</td>' +
        '<td>And any further details here (images etc)...</td>' +
        '</tr>' +
        '<tr>' +
        '<td>Comments:</td>' +
        '<td>' + d.comments + '</td>' +
        '</tr>' +
        '<tr>' +
        '<td>Action:</td>' +
        '<td>' + d.action + '</td>' +
        '</tr>' +
        '</table>';*/


    return htmlDetailTable;
}






/*
function fnFormatDetails(oTable, nTr) {
    var aData = oTable.row(nTr).data();

   // var aData = oTable.fnGetData(nTr);
    var sOut = '<table bgcolor="yellow" cellpadding="8" border="0" style="padding-left:50px;">';
    sOut += '<tr><td>BSN:</td><td>' + aData['Details']['BSN'] + '</td></tr>';
    sOut += '<tr><td>Station:</td><td>' + aData['Details']['StationName'] + '</td></tr>';
    sOut += '<tr><td>Project:</td><td>' + aData['Details']['ProjectName'] + '</td></tr>';
    sOut += '</table>';

    return sOut;
}




function LoadDataTable() {
    var table = $('#formulasTable').DataTable({
        "processing": true,
        
        "bDestroy": true,
        "bJQueryUI": true
    });

    // Add event listener for opening and closing details
    $('#formulasTable').on('click', 'td.details-control', function () {
        //var tr = $(this).closest('tr');
        var nTr = $(this).closest('tr');
        var row = table.row(nTr);

        if (row.child.isShown()) {
            // This row is already open - close it
            //row.child.hide();
            table.fnClose(nTr);
            nTr.removeClass('shown');
        } else {
            // Open this row
            table.fnOpen(nTr, fnFormatDetails(table, nTr), 'details');
            //row.child(format(tr.data('child-value'))).show();
            nTr.addClass('shown');
        }
    });

}

function format(value) {
    return '<div>Hidden Value: ' + value + '</div>';
}*/