﻿using log4net;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Caching;
using System.Web.Configuration;

namespace aboV2.App_Code
{
    public static class Initialisations
    {

        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static string GetKeyAppSettings(string key)
        {

            if (ConfigurationManager.AppSettings[key] == null)
            {
                logger.Error("key " + key + " not exists in Web.config ");
                throw new Exception("key " + key + " not exists in Web.config");
            }
            else
                return ConfigurationManager.AppSettings[key];
        }

        public static bool IsValidEmail(string inputEmail)
        {
            string strRegex = @"^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}" +
                  @"\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" +
                  @".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$";
            Regex re = new Regex(strRegex);
            if (re.IsMatch(inputEmail))
                return (true);
            else
                return (false);
        }


        public static List<string> GetListFilePathCss(string path, int structureId, int eventId, int profilAcheteurId, string langCode)
        {
            return GetListFilePath(path, structureId, eventId, profilAcheteurId, langCode, false);
        }

        public static List<string> GetListFilePathBanner(string path, string logoName, string extensionName, int structureId, int eventId, int profilAcheteurId, string lang, bool useDefaultIfNotExists)
        {
            //logoName, lstExtensionsLogo,
            List<string> lstPaths = new List<string>();
            //int iIdStructure = int.Parse(structureId);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + logoName + "." + eventId + lang + ".pa" + profilAcheteurId + extensionName);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + logoName + "." + eventId + lang + extensionName);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + logoName + ".pa" + profilAcheteurId + lang + extensionName);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + logoName + ".pa" + profilAcheteurId + extensionName);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + logoName + lang + extensionName);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + logoName + extensionName);
            if (useDefaultIfNotExists)
            {
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + logoName + lang + extensionName);
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + logoName + extensionName);
            }

            return lstPaths;
        }


        public static List<string> GetListFilePathJs(string path, int structureId, int eventId, int profilAcheteurId, string langCode)
        {
            List<string> lstPaths = new List<string>();
            string tmPhysicalPathOfJs = path.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));
            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + "." + eventId + "." + langCode + ".pa" + profilAcheteurId + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + "." + eventId + "." + langCode + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + ".pa" + profilAcheteurId + "." + langCode + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + ".pa" + profilAcheteurId + "." + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + "." + langCode + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + Path.GetExtension(path));

            tmPhysicalPathOfJs = path.Replace("[idstructureSur4zeros]\\", "");

            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + "." + langCode + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(tmPhysicalPathOfJs) + "\\" + Path.GetFileNameWithoutExtension(tmPhysicalPathOfJs) + Path.GetExtension(path));

            if (path.Contains(".mobl.js"))
            {
                List<string> lstPathsSansMob = new List<string>();
                string pathSM = path.Replace(".mobl.js", ".js");

                lstPathsSansMob = GetListFilePathJs(pathSM, structureId, eventId, profilAcheteurId, langCode);
                lstPaths.AddRange(lstPathsSansMob);
                //lstPathsSansMob.AddRange(lstPaths);
            }
            return lstPaths;
        }



        public static List<string> GetListFilePathXml(string path, int structureId, int eventId, int profilAcheteurId, string langCode, bool isDefault)
        {
            return GetListFilePath(path, structureId, eventId, profilAcheteurId, langCode, isDefault);
        }


        public static List<string> GetListFilePath(string path, int structureId, int eventId, int profilAcheteurId, string lang, bool useDefaultIfNotExists)
        {
            List<string> lstPaths = new List<string>();
            /*

            //int iIdStructure = int.Parse(structureId);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + "." + eventId + lang + ".pa" + profilAcheteurId + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + "." + eventId + lang + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + ".pa" + profilAcheteurId + lang + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + ".pa" + profilAcheteurId + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + lang + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + Path.GetExtension(path));
            if (useDefaultIfNotExists)
            {
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + Path.GetFileNameWithoutExtension(path) + lang + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + Path.GetFileNameWithoutExtension(path) + Path.GetExtension(path));
            }

            */
            if (useDefaultIfNotExists)
            {
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + Path.GetFileNameWithoutExtension(path) + lang + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + Path.GetFileNameWithoutExtension(path) + Path.GetExtension(path));
            }else
            {
                //int iIdStructure = int.Parse(structureId);
                lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + "." + eventId + lang + ".pa" + profilAcheteurId + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + "." + eventId + lang + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + ".pa" + profilAcheteurId + lang + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + ".pa" + profilAcheteurId + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + lang + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + Path.GetExtension(path));
            }
            return lstPaths;
        }


        //retourne la liste avec les chemin de default si useDefaultIfNotExists est à true
        public static List<string> GetListFileWithDefaultPath(string path, int structureId, int eventId, int profilAcheteurId, string lang, bool useDefaultIfNotExists)
        {
            List<string> lstPaths = new List<string>();
            

            //int iIdStructure = int.Parse(structureId);
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + "." + eventId + lang + ".pa" + profilAcheteurId + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + "." + eventId + lang + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + ".pa" + profilAcheteurId + lang + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + ".pa" + profilAcheteurId + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + lang + Path.GetExtension(path));
            lstPaths.Add(Path.GetDirectoryName(path) + "\\" + Path.GetFileNameWithoutExtension(path) + Path.GetExtension(path));
            if (useDefaultIfNotExists)
            {
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + Path.GetFileNameWithoutExtension(path) + lang + Path.GetExtension(path));
                lstPaths.Add(Path.GetDirectoryName(path).Replace(String.Format("{0,4:0000}", structureId), "DEFAULT") + "\\" + Path.GetFileNameWithoutExtension(path) + Path.GetExtension(path));
            }
           
            return lstPaths;
        }

        public static string GetTemplate(string fileName)
        {

            string templateLine = "";
            if (System.IO.File.Exists(fileName))
            {
                System.IO.StreamReader streamRead =
                 new System.IO.StreamReader(fileName, System.Text.Encoding.UTF8);
                System.Text.Encoding encoding = streamRead.CurrentEncoding;
                templateLine = streamRead.ReadToEnd();
                streamRead.Dispose();
            }
            return templateLine;
        }

    }
}