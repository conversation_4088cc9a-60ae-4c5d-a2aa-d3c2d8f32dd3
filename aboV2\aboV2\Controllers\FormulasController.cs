﻿using aboV2.Models;
using aboV2.wcf_Themis;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using aboV2.App_Code;
using log4net;
using utilitaires2010;
using WebTracing2010;
using ws_DTO;

namespace aboV2.Controllers
{
    public class FormulasController : BaseController
    {
        private static readonly ILog logger4net = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);




        [Throttle(Name = "Load formulas", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        public JsonResult LoadGroupFormulas(int structureId, int identiteId, string langCode)
        {
            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "LoadGroupFormulas start ('" + structureId + " " + langCode + ")");
            

            GestionTrace.WriteLog(structureId, "LoadGroupFormulas start ('" + structureId + " " + langCode);
            List<ws_DTO.GroupeFormulaEntity> listGroupFormulas = new List<ws_DTO.GroupeFormulaEntity>();

            string cacheName = "listgroupformulasLoad" + structureId + "." + identiteId + "_" + langCode;
            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                try
                {
                    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                    listGroupFormulas = wcfThemis.LoadGroupFormulas(structureId, langCode, identiteId).ToList();
                    GestionTrace.WriteLog(structureId, "LoadGroupFormulas listGroupFormulas " + listGroupFormulas.Count);
                    log.LogMsg(structureId, LogLevel.INFO, "LoadGroupFormulas listGroupFormulas " + listGroupFormulas.Count);

                }
                catch (Exception ex)
                {
                    logger.Error("error !! Load group formulas " + structureId + " langcode " + langCode + " " + ex.StackTrace);
                    throw new Exception(ex.Message);
                }

                object _addLock = new object();

                lock (_addLock)
                {
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listGroupFormulas, GetUpdateCache(structureId), DateTime.Now.AddSeconds(60), System.Web.Caching.Cache.NoSlidingExpiration, System.Web.Caching.CacheItemPriority.Default, null);
                }
            }
            else
            {
                listGroupFormulas = ((List<ws_DTO.GroupeFormulaEntity>)System.Web.HttpContext.Current.Cache.Get(cacheName)).ToList();

                GestionTrace.WriteLog(structureId, "Load group formulas cache listGroupFormulas " + listGroupFormulas.Count);
                log.LogMsg(structureId, LogLevel.INFO, "Load group formulas cache listGroupFormulas " + listGroupFormulas.Count);
            }

            //var advSearchParams = JsonConvert.DeserializeObject<List<FormulaEntity>>(listFormulas);
            return Json(listGroupFormulas, JsonRequestBehavior.AllowGet);

        }


        [Throttle(Name = "Load formulas", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        public JsonResult Load(int structureId, int identiteId, string langCode)
        {
          
            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "Load formulas start ('" + structureId + " " + langCode + ")");

            GestionTrace.WriteLog(structureId, "Load formulas start ('" + structureId + " " + langCode);
            List<FormulaEntity> listFormulas = new List<FormulaEntity>();

            string cacheName = "listformulasLoad" + structureId + "." + identiteId + "_" + langCode;
            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                try
                {

                    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                    listFormulas = wcfThemis.LoadFormulas(structureId, langCode, identiteId).ToList();
                    GestionTrace.WriteLog(structureId, "Load formulas listFormulas " + listFormulas.Count);
                    log.LogMsg(structureId, LogLevel.INFO, "Load formulas listFormulas " + listFormulas.Count);

                }
                catch (Exception ex)
                {
                    logger.Error("error !! Load Formulas " + structureId + " langcode " + langCode + " " + ex.StackTrace);
                    throw new Exception(ex.Message);
                }

                object _addLock = new object();

                lock (_addLock)
                {
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listFormulas, GetUpdateCache(structureId), DateTime.Now.AddSeconds(60), System.Web.Caching.Cache.NoSlidingExpiration, System.Web.Caching.CacheItemPriority.Default, null);
                }
            }
            else
            {
                listFormulas = ((List<ws_DTO.FormulaEntity>)System.Web.HttpContext.Current.Cache.Get(cacheName)).ToList();

                GestionTrace.WriteLog(structureId, "Load formulas cache listFormulas " + listFormulas.Count);
                log.LogMsg(structureId, LogLevel.INFO, "Load formulas cache listFormulas " + listFormulas.Count);
            }

            //var advSearchParams = JsonConvert.DeserializeObject<List<FormulaEntity>>(listFormulas);
            return Json(listFormulas, JsonRequestBehavior.AllowGet);
            
        }



    }
}
