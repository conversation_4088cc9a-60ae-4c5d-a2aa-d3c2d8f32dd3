﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ModuleLogin.aspx.cs" Inherits="customerArea.ModuleLogin" %>

<%@ Register Assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
Namespace="System.Web.UI" TagPrefix="asp" %>

<%@ Register Src="wctrlLoginConnect.ascx" TagName="wctrlLoginConnect" TagPrefix="uc1" %>
<%@ Register Src="wctrLoginCreation.ascx" TagName="wctrLoginCreation" TagPrefix="uc2" %>
<%@ Register Src="wctrLoginCreationLight.ascx" TagName="wctrLoginCreationLight" TagPrefix="ucLight" %>



<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml" lang="<%=System.Web.HttpContext.Current.Session["SVarLangue"] %>">
<head runat="server" id="myhead">
  <title></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.9/css/intlTelInput.css" />

  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
  <!-- Fonts CSS -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700,800" rel="stylesheet">
  <link href="https://use.fontawesome.com/releases/v5.0.6/css/all.css" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css">
  
   <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
  
	<!-- JQUERY UI 
	<script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>-->
    <!-- FANCYBOX 
	
    <script type="text/javascript" src="./assets/libs/jquery/fancybox-v2.1.5/jquery.fancybox.pack.js"></script>
    <script src="/assets/jquery/choosen/chosen.jquery.js"></script>
	  
	  
    <script src="/assets/LIBS/jquery/birthday-picker/jquery-birthday-picker.min.js"></script>-->
	
  <!-- Internationnal Tel input -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.9/js/intlTelInput.js"></script>
	  
	<!-- GOOGLEMAPS API -->
    <script src="https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places"></script>
	
	
    <script src="./javascriptfiles/traduction_commune.js"></script>
    <script src="./javascriptfiles/commons_module.js"></script>
	<!-- <script src="./javascriptfiles/commons.js"></script> -->
	

	<script src="./javascriptfiles/modulelogin.js"></script>
</head>
<body>




  <form id="form1" runat="server">


    <div class="container-fluid">

      <div class="panel panel-primary">
        <div class="panel-heading d-none"><span runat="server" id="spanIdentification" clientidmode="Static">Identification</span></div>
        <div id="steps">
          <asp:Literal ID="litForSteps" runat="server"></asp:Literal></div>
          <div id="commentsPageHaut">
            <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal></div>

            <div class="panel-body" id="logincontainer">
              <!-- Nav tabs -->
              <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item">
                   <a class="nav-link active show" href="#loginTab" role="tab" data-toggle="tab" runat="server" id="aDejaUnCompte" clientidmode="Static">J'ai déjà un compte</a>
                </li>
                <li class="nav-item">
                 <a class="nav-link" href="#createprofilTab" role="tab" data-toggle="tab" runat="server" id="aCreerCompte" clientidmode="Static">Créer un compte</a>
                </li>
              </ul>

              <div class="tab-content">
                <div class="tab-pane fade in active show " id="loginTab">
                  <div id="divconnect">
                    <uc1:wctrlLoginConnect ID="WctrlLoginConnect1" runat="server" />
                  </div>
                </div>
                <div class="tab-pane fade" id="createprofilTab">
                  <div id="divcreate">
                    <uc2:wctrLoginCreation ID="WctrLoginCreation1" runat="server" />
                    <ucLight:wctrLoginCreationLight ID="WctrLoginCreationLight" runat="server" />

                  </div>
                </div>
              </div>
            </div>
            <div id="divConnectFacebook" runat="server" visible="false" class="text-center">
              <div class="orsepar">ou</div>
                <button class="btn btn-facebook btn-block" id="btn_facebook" data-trad="btn_connect_facebook">Connexion avec Facebook</button>
            </div>

            <div id="divCommentaireBas">
              <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
            </div>

          </div>

        </div>


      </form>

  <!-- Modals -->
    <div class="modal inmodal" id="modalForMessage" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content animated flipInY">
                <div class="modal-header" id="modalForMessageHeader">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                    <%--<h4 class="modal-title">Modal title</h4>--%>
                </div>
                <div class="modal-body" id="modalForMessageBody">
                </div>
            </div>
        </div>
    </div>
	
      <script>
          // This is called with the results from from FB.getLoginStatus().
          /* function statusChangeCallback(response) {
               console.log('statusChangeCallback');
               console.log(response);
               // The response object is returned with a status field that lets the
               // app know the current login status of the person.
               // Full docs on the response object can be found in the documentation
               // for FB.getLoginStatus().
               if (response.status === 'connected') {
                   // Logged into your app and Facebook.
                   //testAPI();
                  LoginAPI();
               } else if (response.status === 'not_authorized') {
                   // The person is logged into Facebook, but not your app.
                   document.getElementById('status').innerHTML = 'Please log ' +
                     'into this app.';
               } else {
                   // The person is not logged into Facebook, so we're not sure if
                   // they are logged into this app or not.
                   document.getElementById('status').innerHTML = 'Please log ' +
                     'into Facebook.';
               }
             }*/

          // This function is called when someone finishes with the Login
          // Button.  See the onlogin handler attached to it in the sample
          // code below.
          /*  function checkLoginState() {
                FB.getLoginStatus(function (response) {
                    statusChangeCallback(response);
                });
            }
            */
          window.fbAsyncInit = function () {
              FB.init({
                  appId: '1103870345745420',
                  cookie: true,  // enable cookies to allow the server to access 
                  // the session
                  xfbml: true,  // parse social plugins on this page
                  version: 'v2.8' // use graph api version 2.8
              });

              // Now that we've initialized the JavaScript SDK, we call 
              // FB.getLoginStatus().  This function gets the state of the
              // person visiting this page and can return one of three states to
              // the callback you provide.  They can be:
              //
              // 1. Logged into your app ('connected')
              // 2. Logged into Facebook, but not your app ('not_authorized')
              // 3. Not logged into Facebook and can't tell if they are logged into
              //    your app or not.
              //
              // These three cases are handled in the callback function.

              /*  FB.getLoginStatus(function (response) {
                    statusChangeCallback(response);
                  });*/

          };



          var urlJsLang = "//connect.facebook.net/en_US/sdk.js";
          switch ($('html').attr('lang')) {
              case "de": urlJsLang = "//connect.facebook.net/de_DE/sdk.js";
                  break;

              case "fr": urlJsLang = "//connect.facebook.net/fr_FR/sdk.js";
                  break;

              default: urlJsLang = "//connect.facebook.net/en_US/sdk.js";
                  break;
          }

          // Load the SDK asynchronously
          (function (d, s, id) {
              var js, fjs = d.getElementsByTagName(s)[0];
              if (d.getElementById(id)) return;
              js = d.createElement(s); js.id = id;
              js.src = urlJsLang;
              //js.src = "//connect.facebook.net/en_US/sdk.js";
              fjs.parentNode.insertBefore(js, fjs);
          }(document, 'script', 'facebook-jssdk'));

          // Here we run a very simple test of the Graph API after login is
          // successful.  See statusChangeCallback() for when this call is made.
          /*  function testAPI() {
                console.log('Welcome!  Fetching your information.... ');
                FB.api('/me', function (response) {
                    console.log('Successful login for: ' + response.name);
                    document.getElementById('status').innerHTML =
                      'Thanks for logging in, ' + response.name + '!';
                });
    
                FB.api('/me', { fields: 'email' }, function (response) {
                    console.log('Successful login for: ' + response.email);
    
                    ConnectUserFB(response.id, response.email);
                });
    
              }*/


          function LoginAPI() {
              FB.login(function (response) {

                  if (response.authResponse) {
                      console.log('Welcome!  Fetching your information.... ');
                      //console.log(response); // dump complete info
                      access_token = response.authResponse.accessToken; //get access token
                      user_id = response.authResponse.userID; //get FB UID

                      FB.api('/me', { fields: 'email' }, function (response) {
                          var email = response.email;
                          var name = response.name;

                          // window.location = 'http://localhost:61720/login.aspx?IdStructure=0994';
                          // window.location = 'http://localhost:12962/Account/FacebookLogin/' + email + '/' + name;
                          // used in my mvc3 controller for //AuthenticationFormsAuthentication.SetAuthCookie(email, true);     

                          //call webservice
                          ConnectUserFB(user_id, email);
                      });

                  } else {
                      //user hit cancel button
                      console.log('User cancelled login or did not fully authorize.');

                  }
              }, {
                  scope: 'email'
              });

          }

          </script>
         
          <!-- bootstrap -->
          <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
          <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
          <!-- font awesome-->
          <script defer src="https://use.fontawesome.com/releases/v5.0.6/js/all.js"></script>



		  
          <!-- <script type="text/javascript" src="/assets/bootstrap/bootstrap-tab.js"></script>-->
          <!--<script src="/assets/LIBS/formvalidation/0.6.3/dist/js/framework/bootstrap.min.js"></script>
          <script src="/assets/LIBS/formvalidation/0.6.3/dist/js/formValidation.js"></script>-->

        </body>
        </html>
