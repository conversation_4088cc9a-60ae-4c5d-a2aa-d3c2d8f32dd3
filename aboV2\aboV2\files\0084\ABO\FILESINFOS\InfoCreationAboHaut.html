<p class="commentaireetape">Ce<PERSON> &eacute;tape vous permet de modifier votre abonnement en :</p>

<p class="commentaireetape"><u>Ajoutant&nbsp;un ou des spectacles</u> en cliquant sur <strong>"Ajouter un spectacle"</strong></p>

<p class="commentaireetape"><u>Modifiant&nbsp;une date</u> en cliquant sur <strong>"s&eacute;ance"</strong> </p>

<p class="commentaireetape"><u>Supprimant un spectacle</u> en cliquant sur l'ic&ocirc;ne <strong>"poubelle".</strong></p>

<p class="commentaireetape">Votre abonnement sera valid&eacute; en cliquant sur le bouton "Etape suivante".<br /></p>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-13']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    </script>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-2']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    &lt;/script&gt;</body></html></body></html></body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html></script>