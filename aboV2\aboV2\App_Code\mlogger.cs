﻿using log4net;
using log4net.Repository.Hierarchy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace aboV2.App_Code
{

    //public enum LogLevel : int
    //{
    //    DEBUG = 0,
    //    INFO = 1,
    //    WARN = 2,
    //    ERROR = 3,
    //    FATAL = 4
    //}

    //public enum LogModes : int
    //{
    //    GROUP_1 = 0,
    //    GROUP_2 = 1,
    //    GROUP_3 = 2
    //}

    //public class _mlogger : logger
    //{

    //    private static readonly ILog logger4net = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

    //    logger objLogger = new logger();

    //    public void Debug_(string msg)
    //    {
    //        objLogger.LogMsg(0, LogLevel.DEBUG, msg);
    //    }
    //    public void Debug(string idstructure, string msg)
    //    {
    //        objLogger.LogMsg(int.Parse(idstructure), LogLevel.DEBUG, msg);
    //    }

    //    public void Error_(string msg)
    //    {
    //        logger4net.Error(msg);
    //    }
    //    public void Error(string idstructure, string msg)
    //    {
    //        objLogger.LogMsg(int.Parse(idstructure), LogLevel.ERROR, msg);
    //    }

    //    public void Warn_(string msg)
    //    {
    //        logger4net.Warn(msg);
    //    }
    //    public void Warn(string idstructure, string msg)
    //    {
    //        objLogger.LogMsg(int.Parse(idstructure), LogLevel.WARN, msg);
    //        //logger4net.Debug(msg);
    //    }

    //    public void Fatal_(string msg)
    //    {
    //        logger4net.Fatal(msg);
    //    }
    //    public void Fatal(string idstructure, string msg)
    //    {
    //        objLogger.LogMsg(int.Parse(idstructure), LogLevel.FATAL, msg);
    //        //logger4net.Debug(msg);
    //    }
    //}


    //public class _logger
    //{
    //    public void LogMsg(int idstructure, LogLevel level, object msg)
    //    {
    //        string strLogFileName = String.Empty;

    //        switch (idstructure)
    //        {
    //            case 0:
    //                strLogFileName = "common.log";
    //                break;

    //            default:
    //                strLogFileName = idstructure.ToString("0000") + ".log";
    //                break;
    //        }
    //        string IP = "";
    //        var currContext = HttpContext.Current;
    //        if (currContext != null && currContext.Handler != null)
    //        {

    //            IP = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
    //            if (IP == null)
    //            {
    //                IP = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
    //            }
    //            if (IP == null)
    //            {
    //                IP = System.Web.HttpContext.Current.Request.UserHostAddress.ToString().Replace("'", "''");
    //            }
    //        }

    //        log4net.GlobalContext.Properties["ip"] = IP;
    //        log4net.GlobalContext.Properties["structureId"] = idstructure.ToString("0000");
    //        log4net.Config.XmlConfigurator.Configure();

    //        //        string
    //        //configfileForNet = ConfigurationManager.AppSettings["configFileForLog4net"];
    //        //        log4net.Config.DOMConfigurator.Configure(new System.IO.FileInfo(configfileForNet));

    //        ILog log = LogManager.GetLogger(typeof(Logger));

    //        switch (level)
    //        {
    //            case LogLevel.DEBUG:
    //                log.Debug(msg);
    //                break;

    //            case LogLevel.INFO:
    //                log.Info(msg);
    //                break;

    //            case LogLevel.WARN:
    //                log.Warn(msg);
    //                break;

    //            case LogLevel.ERROR:
    //                log.Error(msg);
    //                break;

    //            case LogLevel.FATAL:
    //                log.Fatal(msg);
    //                break;
    //        }
    //    }
    //}
}