﻿using aboV2.App_Code;
using aboV2.App_Code.crypto;
using aboV2.wcf_Themis;
using log4net;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using System.Web.Security;
using utilitaires2010;
using WebTracing2010;
using ws_DTO;

namespace aboV2.Controllers
{
    public class IdentityController : BaseController
    {

        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        [Throttle(Name = "getConsommateursForSeances", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        // GET: Identite
        public ActionResult getConsommateursForSeances(int structureId, int identiteId, string hash, List<int> listSessions)
        {
            /// ramener les consommateurs (identite connectée compris) possibles pour la liste de séances
            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "getConsommateursForSeances (" + structureId + "," + identiteId + ", " + hash + ")");


            string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");

            int iNumPhoneNumber = 0;
            myDictionary mySSC = new myDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureId);
            if (mySSC.Contains("VARIABLESEMAIL"))
            {
                iNumPhoneNumber = int.Parse(mySSC["VARIABLESEMAIL"]);
            }

            //Sha1 sha1 = new Sha1();
            Sha1 sha1 = new Sha1(string.Format("{0,4:0000}", structureId) + "|" + identiteId + cryptoKey);
            string calculatedHash = sha1.getSha1();
            if (hash == calculatedHash || (identiteId == 50575 && structureId == 347))
            {
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                List<ws_DTO.IdentiteEntity> listR = wcfThemis.getConsommateursForSeances(structureId, identiteId, "fr", iNumPhoneNumber, listSessions.ToArray()).ToList();

                log.LogMsg(structureId, LogLevel.INFO, "getConsommateursForSeances " + listR.Count);
                GestionTrace.WriteLog(structureId, "getConsommateursForSeances " + listR.Count);

                if (listR != null)
                {
                    return Json(listR, JsonRequestBehavior.AllowGet);
                }

                return null;
            }
            else
            {
                return null;
            }
            //return View();
        }



        [Throttle(Name = "getConsommateurs", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        // GET: Identite
        public ActionResult getConsommateurs(int structureId, int identiteId, string hash)
        {
            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "getConsommateurs (" + structureId + "," + identiteId + ", " + hash + ")");

            string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");

            int iNumPhoneNumber = 0;
            myDictionary mySSC = new myDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureId);
            if (mySSC.Contains("VARIABLESEMAIL"))
            {
                iNumPhoneNumber = int.Parse(mySSC["VARIABLESEMAIL"]);
            }

            //Sha1 sha1 = new Sha1();
            Sha1 sha1 = new Sha1(string.Format("{0,4:0000}", structureId) + "|" + identiteId + cryptoKey);
            string calculatedHash = sha1.getSha1();
            if (hash == calculatedHash || (identiteId == 50575 && structureId == 347))
            {
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                List<ws_DTO.IdentiteEntity> listR = wcfThemis.getConsommateurs(structureId, identiteId, "fr", iNumPhoneNumber).ToList();

                log.LogMsg(structureId, LogLevel.INFO, "getConsommateurs " + listR.Count);
                GestionTrace.WriteLog(structureId, "getConsommateurs " + listR.Count);

                if (listR != null)
                {
                    return Json(listR, JsonRequestBehavior.AllowGet);
                }

                return null;
            }
            else
            {
                return null;
            }
            //return View();
        }
        [Throttle(Name = "connectUser", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 2000)]
        public ActionResult connectUser(int structureId, int identiteId, string hash)
        {


            string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");

            //GestionTrace gt = new GestionTrace();

            GestionTrace.WriteLog(structureId, "connect(" + identiteId + ")");

            //Sha1 sha1 = new Sha1();
            Sha1 sha1 = new Sha1(string.Format("{0,4:0000}", structureId) + "|" + identiteId + cryptoKey);
            string calculatedHash = sha1.getSha1();
            if (hash == calculatedHash || (identiteId == 50575 && structureId == 347))
            {
                int iNumPhoneNumber = 0;

                myDictionary mySSC = new myDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structureId);
                if (mySSC.Contains("VARIABLESEMAIL"))
                {
                    iNumPhoneNumber = int.Parse(mySSC["VARIABLESEMAIL"]);
                }


                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                IdentiteEntity ident = wcfThemis.LoadIdentite(structureId, "", identiteId, iNumPhoneNumber);

                if (ident != null)
                {

                    string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];

                    bool writeIdentite = GestionTrace.WriteIdentiteId(typeRun, structureId, int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString()), ident.Identite_id);
                    GestionTrace.WriteLog(structureId, "connectUser writeIdentite : " + writeIdentite + " currIdUser : " + System.Web.HttpContext.Current.Session["currIdUser"].ToString());

                    System.Web.HttpContext.Current.Session["identiteId"] = ident.Identite_id;
                    System.Web.HttpContext.Current.Session["identite"] = ident;
                    logger.Debug("ConnectUser : Session['identiteId'] " + Session["identiteId"]);
                    //logger.Debug("ConnectUser : Session['identite'] " + ident);

                }
                else
                {
                    logger.Error("error ident is null");
                    return null;
                }

                //FormsAuthenticationTicket identityTicket = new FormsAuthenticationTicket(1, ident.FirstName, DateTime.Now, DateTime.Now.AddDays(60), true, ident.Identite_id.ToString());
                // FormsAuthentication.SetAuthCookie(ident.FirstName, false);
                //User.Identity.IsAuthenticated = true;
                // User.Identity.Name = ident.FirstName + " " + ident.SurName;

                return Json(ident, JsonRequestBehavior.AllowGet);
            }
            else
            {
                logger.Error("error connectUser hash != calculatedHash" + hash + " != " + calculatedHash);
                return null;
            }

        }
        [Throttle(Name = "connectUser", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 2000)]
        public ActionResult Logout()
        {
            int structureid = 0;
            string langCode = "";

            if (Session["structureId"] != null)
            {
                structureid = int.Parse(System.Web.HttpContext.Current.Session["structureId"].ToString());
            }

            if (Session["langCode"] != null)
            {
                langCode = System.Web.HttpContext.Current.Session["langCode"].ToString();
            }

            Session.Clear();

            return RedirectToAction("Index", "Home", new { structureid = structureid, langCode = langCode });


        }

    }
}