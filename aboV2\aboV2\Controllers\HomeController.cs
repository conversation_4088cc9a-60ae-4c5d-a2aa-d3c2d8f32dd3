﻿using System.Web.Mvc;

namespace aboV2.Controllers
{
    public class HomeController : BaseController
    {
     
        
        public ActionResult GetCurrentBasket(int structureId)
        {
            int identiteId = 0;
            if (Session["identiteId"] != null)
            {
                identiteId = int.Parse(Session["identiteId"].ToString());
            }

            ws_DTO.BasketEntity thisCurrentBasket = HasBasketForIdentity(structureId);
            if (thisCurrentBasket != null)
            {
                if (thisCurrentBasket.User != null)
                {
                    Session["currIdUser"] = thisCurrentBasket.User.WebUserId;
                }
            }
            else
            {
                Session["currIdUser"] = null;
                Session.Clear();
            }

            return Json(thisCurrentBasket, JsonRequestBehavior.AllowGet);
        }


    }
}