﻿using aboV2.App_Code;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Xml.Linq;
using utilitaires2010;
using WebTracing2010;
using ws_DTO;

namespace aboV2.Controllers
{
    public class JsonController : BaseController
    {
        private static readonly ILog logger4net = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        // GET: Json
        public ActionResult Index()
        {
            return View();
        }
    
        public JsonResult Translate(int structureId, string langCode, bool isDefault)
        {
            int eventId = 0;
            int profilAcheteurId = 0;
            if (Session["eventId"] != null)
                eventId = int.Parse(Session["eventId"].ToString());
            if (Session["profilAcheteurId"] != null)
                profilAcheteurId = int.Parse(Session["profilAcheteurId"].ToString());
            //string langCode = Session["langCode"].ToString();



            if (System.Web.HttpContext.Current.Cache["xmlTranslate" + structureId + langCode + isDefault] != null)
            {
                string xml = System.Web.HttpContext.Current.Cache["xmlTranslate" + structureId + langCode + isDefault].ToString();


                return Json(xml.ToString(), JsonRequestBehavior.AllowGet);

            }
            else
            {

                string XmlTranslateFile = LoadXmlTranslateFiles(structureId, eventId, profilAcheteurId, "." + langCode, "translate", isDefault);
                // string XmlTranslateFileDefault = LoadXmlTranslateFiles(structureId, eventId, profilAcheteurId, "." + langCode, "translate", true);

                if (!string.IsNullOrEmpty(XmlTranslateFile))
                {
                    XDocument xml = XDocument.Load(XmlTranslateFile);

                    // ViewBag.xmlTranslate = xml.ToString();

                    // var json = new JavaScriptSerializer().Serialize(GetXmlData(XElement.Parse(xml.ToString())));
                    //System.Web.Caching.CacheDependency ca = new System.Web.Caching.CacheDependency(XmlTranslateFile);
                    //10 mins
                    System.Web.HttpContext.Current.Cache.Insert("xmlTranslate" + structureId + langCode + isDefault, xml.ToString(), new System.Web.Caching.CacheDependency(XmlTranslateFile), DateTime.Now.AddSeconds(600), TimeSpan.Zero);


                    return Json(xml.ToString(), JsonRequestBehavior.AllowGet);
                }
            }
            return Json("", JsonRequestBehavior.AllowGet);
        }



        private static Dictionary<string, object> GetXmlData(XElement xml)
        {
            var attr = xml.Attributes().ToDictionary(d => d.Name.LocalName, d => (object)d.Value);
            if (xml.HasElements) attr.Add("value", xml.Elements().Select(e => GetXmlData(e)));
            else if (!xml.IsEmpty) attr.Add("value", xml.Value);

            return new Dictionary<string, object> { { xml.Name.LocalName, attr } };
        }


        public JsonResult GetDeviseCode(int structureId)
        {

            myDictionary mySSC = new myDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureId);

            DeviseEntity devise = new DeviseEntity();

            if (mySSC.Contains("PARAMDEVISE_CODE") && mySSC["PARAMDEVISE_CODE"] != null && mySSC["PARAMDEVISE_CODE"] != "")
                devise.Code = mySSC["PARAMDEVISE_CODE"].ToString();

            if (mySSC.Contains("PARAMDEVISE_BEFORE") && mySSC["PARAMDEVISE_BEFORE"] != null && mySSC["PARAMDEVISE_BEFORE"] != "")
                devise.Before = Convert.ToBoolean(mySSC["PARAMDEVISE_BEFORE"].ToString());

            if (mySSC.Contains("PARAMDEVISE_SEPARATOR") && mySSC["PARAMDEVISE_SEPARATOR"] != null && mySSC["PARAMDEVISE_SEPARATOR"] != "")
                devise.Separator = mySSC["PARAMDEVISE_SEPARATOR"].ToString();


            return Json(devise, JsonRequestBehavior.AllowGet);
        }

        /*
         /// <summary>
        /// groupes de contraintes sur la formule
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langCode"></param>
        /// <param name="formulaId"></param>
        /// <returns></returns>
        [Throttle(Name = "Flag", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        public ActionResult LoadConstraintes(int structureId, int identiteId, string langCode, string formulaId)
        {
            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "LoadConstraintes start ('" + structureId + " " + langCode + ")");
            GestionTrace.WriteLog(structureId, "LoadConstraintes start ('" + structureId + " " + langCode + ")");

            string[] arrformEtTarif = formulaId.Split(':');
            int iformulaId = int.Parse(arrformEtTarif[0]);

            string cacheName = "LoadConstraintes." + structureId + ".i" + identiteId + "_l" + langCode + "_f" + formulaId;

            List<ws_DTO.FormulaContraintesGroupeOpenEntity> listConstraints = new List<ws_DTO.FormulaContraintesGroupeOpenEntity>();
            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                listConstraints = wcfThemis.LoadContraintesOfFormulas(structureId, langCode, identiteId, iformulaId).ToList();

                log.LogMsg(structureId, LogLevel.INFO, "listConstraints " + listConstraints.Count);
                GestionTrace.WriteLog(structureId, "Load listConstraints " + listConstraints.Count);

                if (listConstraints.Count > 0)
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listConstraints, GetUpdateCache(structureId), DateTime.Now.AddSeconds(CacheDelayS), TimeSpan.Zero);
            }
            else
            {
                listConstraints = (List<ws_DTO.FormulaContraintesGroupeOpenEntity>)System.Web.HttpContext.Current.Cache[cacheName];
                log.LogMsg(structureId, LogLevel.INFO, "listConstraints cache " + listConstraints.Count);
                GestionTrace.WriteLog(structureId, "listConstraints cache" + listConstraints.Count);
            }

            FormulaWithConstraints formWithConstraints = new FormulaWithConstraints();
            formWithConstraints.formulaId = iformulaId;
            formWithConstraints.listConstraints = listConstraints;

            return Json(formWithConstraints, JsonRequestBehavior.AllowGet);
        }
        */

    }
}