﻿using aboV2.App_Code;
using SmartSoft.QueryStringEncryption;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using utilitaires2010;
using utilitaires2010.crypto;
using WebTracing2010;
using ws_bll.DAOOpen;
using ws_bll.WT;
using ws_DTO;
using ws_DTO.wt;

namespace aboV2.Controllers
{
    public class SummaryController : BaseController
    {

        public ActionResult Load()
        {
            return View();
        }

        [Throttle(Name = "LoadProductOfFormulas summary", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 1000)]
        public ActionResult LoadProductOfFormulas(int structureId, int identiteId, customBasket jsbasket)
        {
            Logger log = new Logger();
            try
            {
                GestionTrace.WriteLog(structureId, "LoadProductOfFormulas( identiteId " + identiteId);

                List<ProductEntity> listProds = new List<ProductEntity>();

                List<int> listDistinctsFormulas = new List<int>();
                List<int> listDistinctsSessions = new List<int>();
                List<int> listDistinctsTarifs = new List<int>();

                if (jsbasket != null && jsbasket.listFormulas != null && jsbasket.listFormulas.Count > 0)
                {
                    foreach (customFormulas formulB in jsbasket.listFormulas)
                    {
                        if (!listDistinctsFormulas.Contains(formulB.formulaid))
                        {
                            listDistinctsFormulas.Add(formulB.formulaid);
                        }
                        foreach (customTarif tarif in formulB.listTarifs)
                        {
                            if (!listDistinctsTarifs.Contains(tarif.tarifid))
                            {
                                listDistinctsTarifs.Add(tarif.tarifid);
                            }

                            foreach (customAbo abo in tarif.listAbos)
                            {

                                foreach (customEvent session in abo.listEvents)
                                {
                                    if (!listDistinctsSessions.Contains(session.sessionid))
                                        listDistinctsSessions.Add(session.sessionid);
                                }

                                //FormulaGroupeManager.Insert();
                            }
                        }
                        if (jsbasket.listEventsHA != null)
                        {
                            foreach (var session in jsbasket.listEventsHA)
                            {
                                if (!listDistinctsSessions.Contains(session.sessionid))
                                    listDistinctsSessions.Add(session.sessionid);
                            }
                        }

                    }

                    string langCode = "fr";
                    if (Session["langCode"] != null)
                        langCode = Session["langCode"].ToString();


                    //listProds = GetProducts(structureId, identiteId, listDistinctsFormulas, langCode);
                    listProds = GetProducts(structureId, identiteId, listDistinctsFormulas, langCode);


                    GestionTrace.WriteLog(structureId, "LoadProductOfFormulas( listProds " + listProds.Count);

                    return Json(listProds, JsonRequestBehavior.AllowGet);

                }
                else return null;
            }
            catch (Exception ex)
            {

                log.LogMsg(structureId, LogLevel.ERROR, "LoadProductOfFormulas (" + structureId + "," + identiteId + ",jsbasket");
                return null;
            }
        }

        [Throttle(Name = "LoadFraisProductOfFormulas summary", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 1000)]
        public ActionResult LoadQuestionnaire(int structureId, int identiteId, customBasket jsbasket)
        {
            Logger log = new Logger();
            try
            {
                string langCode = "en";
                if (Session["langCode"] != null)
                {
                    langCode = Session["langCode"].ToString();
                }
                List<int> listDistinctsFormulas = new List<int>();
                if (jsbasket != null && jsbasket.listFormulas != null && jsbasket.listFormulas.Count > 0)
                {
                    foreach (customFormulas formulB in jsbasket.listFormulas)
                    {
                        if (!listDistinctsFormulas.Contains(formulB.formulaid))
                        {
                            listDistinctsFormulas.Add(formulB.formulaid);
                        }
                    }
                }
                List<ProductQuestionaire> listQuest = new List<ProductQuestionaire>();
                listQuest = GetQuestionnaireProducts(structureId, identiteId, listDistinctsFormulas, langCode);
                log.LogMsg(structureId, LogLevel.DEBUG, "après appel WCF GetQuestionnaireProducts " + listQuest.Count);
                return Json(listQuest, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                log.LogMsg(structureId, LogLevel.ERROR, "LoadQuestionnaire (" + structureId + "," + identiteId + ",jsbasket");
                return null;
            }
        }


        [Throttle(Name = "LoadFraisProductOfFormulas summary", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 1000)]
        public ActionResult LoadFraisProductOfFormulas(int structureId, int identiteId, customBasket jsbasket)
        {
            Logger log = new Logger();
            try
            {

                log.LogMsg(structureId, LogLevel.DEBUG, "LoadFraisProductOfFormulas (" + structureId + "," + identiteId + ", " + jsbasket);
                GestionTrace.WriteLog(structureId, "LoadFraisProductOfFormulas( identiteId " + identiteId);

                List<ProductEntity> listProds = new List<ProductEntity>();

                List<int> listDistinctsFormulas = new List<int>();
                List<int> listDistinctsSessions = new List<int>();
                List<int> listDistinctsTarifs = new List<int>();

                if (jsbasket != null && jsbasket.listFormulas != null && jsbasket.listFormulas.Count > 0)
                {
                    foreach (customFormulas formulB in jsbasket.listFormulas)
                    {
                        if (!listDistinctsFormulas.Contains(formulB.formulaid))
                        {
                            listDistinctsFormulas.Add(formulB.formulaid);
                        }
                        foreach (customTarif tarif in formulB.listTarifs)
                        {
                            if (!listDistinctsTarifs.Contains(tarif.tarifid))
                            {
                                listDistinctsTarifs.Add(tarif.tarifid);
                            }

                            foreach (customAbo abo in tarif.listAbos)
                            {

                                foreach (customEvent session in abo.listEvents)
                                {
                                    if (!listDistinctsSessions.Contains(session.sessionid))
                                        listDistinctsSessions.Add(session.sessionid);
                                }


                                //FormulaGroupeManager.Insert();
                            }
                        }
                        if (jsbasket.listEventsHA != null)
                        {
                            foreach (var session in jsbasket.listEventsHA)
                            {
                                if (!listDistinctsSessions.Contains(session.sessionid))
                                    listDistinctsSessions.Add(session.sessionid);
                            }
                        }

                    }

                    log.LogMsg(structureId, LogLevel.DEBUG, "avant appel WCF GetFraisProducts");
                    GestionTrace.WriteLog(structureId, "LoadFraisProductOfFormulas avant appel WCF GetFraisProducts ");


                    string langCode = "en";
                    if (Session["langCode"] != null)
                    {
                        langCode = Session["langCode"].ToString();
                    }

                    listProds = GetFraisProducts(structureId, identiteId, listDistinctsFormulas, langCode);
                    log.LogMsg(structureId, LogLevel.DEBUG, "après appel WCF GetFraisProducts " + listProds.Count);
                    GestionTrace.WriteLog(structureId, "LoadFraisProductOfFormulas après appel WCF GetFraisProducts " + listProds.Count);

                    //    getProducts();

                    return Json(listProds, JsonRequestBehavior.AllowGet);

                }
                else return null;
            }
            catch (Exception ex)
            {

                log.LogMsg(structureId, LogLevel.ERROR, "LoadFraisProductOfFormulas (" + structureId + "," + identiteId + ",jsbasket");
                return null;
            }
        }


        [Throttle(Name = "LoadMO summary", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        public ActionResult LoadMO(int structureId, int identiteId, string langCode, customBasket jsbasket)
        {
            Logger log = new Logger();
            try
            {
                GestionTrace.WriteLog(structureId, "LoadMO " + identiteId);

                List<ProductFraisEnvois> listProdPs = new List<ProductFraisEnvois>();

                //List<int> listDistinctsFormulas = new List<int>();
                //List<int> listDistinctsSessions = new List<int>();
                //List<int> listDistinctsTarifs = new List<int>();
                List<int> listGestionPlaceId = new List<int>();

                if (jsbasket != null && jsbasket.listFormulas != null && jsbasket.listFormulas.Count > 0)
                {

                    jsbasket.listFormulas.ForEach(delegate (customFormulas formula)
                    {
                        formula.listTarifs.ForEach(delegate (customTarif tarif)
                        {
                            tarif.listAbos.ForEach(delegate (customAbo abo)
                            {
                                listGestionPlaceId.AddRange(abo.listEvents.Select(gp => gp.GestionPlaceId));
                            });
                        });
                    });

                    if (jsbasket.listEventsHA != null)
                    {
                        jsbasket.listEventsHA.ForEach(delegate (customEventHA eventHa)
                        {
                            eventHa.listTarifs.ForEach(delegate (customTarifHA tarifHa)
                            {
                                listGestionPlaceId.Add(tarifHa.gpid);
                            });
                        });
                    }


                    /*
                    foreach (customFormulas formulB in jsbasket.listFormulas)
                    {
                        if (!listDistinctsFormulas.Contains(formulB.formulaid))
                        {
                            listDistinctsFormulas.Add(formulB.formulaid);
                        }
                        foreach (customTarif tarif in formulB.listTarifs)
                        {
                            if (!listDistinctsTarifs.Contains(tarif.tarifid))
                            {
                                listDistinctsTarifs.Add(tarif.tarifid);
                            }

                            foreach (customAbo abo in tarif.listAbos)
                            {

                                foreach (customEvent session in abo.listEvents)
                                {
                                    if (!listDistinctsSessions.Contains(session.sessionid))
                                        listDistinctsSessions.Add(session.sessionid);
                                }



                                //FormulaGroupeManager.Insert();
                            }
                        }
                        if (jsbasket.listEventsHA != null)
                        {
                            foreach (var session in jsbasket.listEventsHA)
                            {
                                if (!listDistinctsSessions.Contains(session.sessionid))
                                    listDistinctsSessions.Add(session.sessionid);

                                foreach (customTarifHA tarifHA in session.listTarifs)
                                {
                                    if (!listDistinctsTarifs.Contains(tarifHA.priceId))
                                    {
                                        listDistinctsTarifs.Add(tarifHA.priceId);
                                    }
                                }
                           

                            }
                        }

                    }
                    */


                    listProdPs = GetModeObtention(structureId, listGestionPlaceId, langCode);
                    //listProdPs = GetModeObtention(structureId, listDistinctsFormulas, listDistinctsSessions, listDistinctsTarifs, "fr");


                    GestionTrace.WriteLog(structureId, "avant getHashProduit LoadMO " + listProdPs.Count);

                    List<ProductFraisEnvois> listProds = new List<ProductFraisEnvois>();
                    /// ajout du hash pour controler les manipulations eventuelles de l'internaute                    
                    foreach (ProductFraisEnvois prod in listProdPs)
                    {
                        //ProductFraisEnvois prodMO = prod.As<ProductFraisEnvois>();

                        prod.hashKey = getHashProduit(prod);
                        //prodMO.modeObtentionType = "ND";
                        //listProds.Add(prodMO);

                    }

                    GestionTrace.WriteLog(structureId, "après getHashProduit LoadMO " + listProdPs.Count);

                    return Json(listProdPs, JsonRequestBehavior.AllowGet);

                }
                else return null;
            }
            catch (Exception ex)
            {

                log.LogMsg(structureId, LogLevel.ERROR, "LoadMO (" + structureId + "," + identiteId + ",jsbasket");
                return null;
            }
        }

        private string getHashProduit(ProductEntity prod)
        {
            string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");

            //GestionTrace gt = new GestionTrace();

            //GestionTrace.WriteLog(structureId, "connect(" + identiteId + ")");

            //Sha1 sha1 = new Sha1();
            Sha1 sha1 = new Sha1(prod.ProduitId + "|" + prod.MontantTTCinCent + cryptoKey);

            string calculatedHash = sha1.getSha1();


            return calculatedHash;
        }

        [Throttle(Name = "urlOfPayment summary", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        protected string urlOfPayment(int structure_id, int basketId)
        {
            Logger log = new Logger();
            try
            {
                string completeUrl = "";
                string urldeBase = Initialisations.GetKeyAppSettings("SitePaiement"); // System.Configuration.ConfigurationManager.AppSettings["SitePaiement"];

                myDictionary mySSC = new myDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structure_id);
                if (mySSC.Contains("PAIEMENTURLSITE") && mySSC["PAIEMENTURLSITE"] != null && mySSC["PAIEMENTURLSITE"] != "")
                {
                    urldeBase = mySSC["PAIEMENTURLSITE"].ToString();
                }


                if (Session["identiteId"] == null)
                {
                    log.LogMsg(structure_id, LogLevel.ERROR, "dans urlOfPayment(" + structure_id + ", " + basketId + "):Session['identiteId'] is null !");
                    Exception ex = new Exception("web session is lost");
                    throw ex;
                }


                int identite_id = int.Parse(Session["identiteId"].ToString());
                string user_id = Session["currIdUser"].ToString();

                if (mySSC.Contains("PAIEMENTVERSION") && mySSC["PAIEMENTVERSION"].ToUpper() == "V3")
                {
                    string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");
                    Sha1 sha1 = new Sha1("paiementV3_" + structure_id + "_" + basketId + "_" + cryptoKey);
                    string hash = sha1.getSha1();

                    string thisLang = System.Web.HttpContext.Current.Session["langCode"].ToString();
                    urldeBase = urldeBase.Replace("[structureid]", structure_id.ToString())
                        .Replace("[basketid]", basketId.ToString())
                        .Replace("[platformname]", "GROUPE")
                        .Replace("[lang]", thisLang)
                        .Replace("[haskey]", hash)
                        .Replace("[identiteid]", identite_id.ToString());

                    completeUrl = urldeBase;

                }
                else
                {

                    NameValueCollection queryStringsToPass = new NameValueCollection();
                    queryStringsToPass.Add("structure", structure_id.ToString("0000"));
                    queryStringsToPass.Add("user", user_id);
                    queryStringsToPass.Add("identite", identite_id.ToString());
                    queryStringsToPass.Add("panier", basketId.ToString());
                    queryStringsToPass.Add("filiere", "GROUPE");
                    queryStringsToPass.Add("status", "YES");

                    if (System.Web.HttpContext.Current.Session["langCode"] != null)
                        queryStringsToPass.Add("lang", System.Web.HttpContext.Current.Session["langCode"].ToString());

                    string encryptedString = CryptoQueryStringHandler.EncryptQueryStrings(queryStringsToPass, System.Configuration.ConfigurationManager.AppSettings["CryptoKey"]);
                    completeUrl = urldeBase + "?" + encryptedString;
                }

                return completeUrl;
            }
            catch (Exception ex)
            {

                log.LogMsg(structure_id, LogLevel.ERROR, "dans urlOfPayment(" + structure_id + ", " + basketId + "):" + ex.Message + " " + ex.StackTrace); ;
                //logger.Error("dans urlOfPayment: " + ex.Message + " " + ex.StackTrace);
                throw ex;
            }
        }

        [Throttle(Name = "urlOfPayment summary", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        /// <summary>
        /// ajout en table panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="basketRecu"></param>
        /// <returns></returns>
        public ActionResult AddBasket(int structureId, int identiteId, customBasket jsbasket)
        {
            if (jsbasket.prodQuestionnaire == null)
            {
                jsbasket.prodQuestionnaire = new List<ProductEntity>();
            }

            bool breturn = false;

            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "AddBasket (" + structureId + "," + identiteId + ",jsbasket" + ")");

            try
            {
                if (identiteId != 0)
                {

                    string langCode = "en";
                    if (Session["langCode"] != null)
                    {
                        langCode = Session["langCode"].ToString();
                    }

                    List<int> listDistinctsFormulasId = new List<int>();

                    if (jsbasket.mobtention == null)
                    {
                        log.LogMsg(structureId, LogLevel.ERROR, "mobtention is null ?!");
                    }


                    ProductFraisEnvois MObention = jsbasket.mobtention;

                    string myHK = MObention.hashKey;
                    string calculatedHK = getHashProduit(MObention);
                    if (calculatedHK != myHK)
                    {
                        log.LogMsg(structureId, LogLevel.ERROR, "hask key du MO different !");
                        Exception ex = new Exception("calculatedHK != myHK (" + calculatedHK + "!=" + myHK + ")");
                        throw ex;
                    }
                    else
                    {

                        GestionTrace.WriteLog(structureId, "addBasket...");
                        if (System.Web.HttpContext.Current.Session["currIdUser"] != null)
                        {
                            int web_user_id = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());

                            List<int> listDistinctsEvents = new List<int>();
                            List<int> listDistinctsSessions = new List<int>();

                            if (jsbasket != null && jsbasket.listFormulas != null && jsbasket.listFormulas.Count > 0)
                            {
                                bool baskIsValide = true;
                                foreach (customFormulas formulB in jsbasket.listFormulas)
                                {
                                    listDistinctsFormulasId.Add(formulB.formulaid);

                                    foreach (customTarif tarif in formulB.listTarifs)
                                    {
                                        foreach (customAbo abo in tarif.listAbos)
                                        {
                                            int thisIdentite = abo.identiteId;
                                            string hk = abo.hk;
                                            if (thisIdentite != 0)
                                            {
                                                // TODO check hash identite/hk ...
                                                bool checkHash = true;
                                                if (!checkHash)
                                                    baskIsValide = false;
                                            }
                                            foreach (customEvent session in abo.listEvents)
                                            {
                                                if (!listDistinctsEvents.Contains(session.eventid))
                                                    listDistinctsEvents.Add(session.eventid);
                                                if (!listDistinctsSessions.Contains(session.sessionid))
                                                    listDistinctsSessions.Add(session.sessionid);

                                                if (session.listSeats != null)
                                                {

                                                    foreach (customFlaggedSeat seat in session.listSeats)
                                                    {
                                                        int Manif_id = session.eventid;
                                                        int thissessionid = session.sessionid;
                                                        int Seat_id = seat.seatid;
                                                        string hashKey = getHash(Seat_id + "/" + thissessionid, thisSalt);
                                                        if (seat.hashKey != hashKey)
                                                        {
                                                            baskIsValide = false;
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    log.LogMsg(structureId, LogLevel.WARN, "listSeats is null ?! pour " + session.eventName + " " + session.sessionDescription);
                                                }
                                            }



                                            //FormulaGroupeManager.Insert();
                                        }
                                    }
                                    if (jsbasket.listEventsHA != null)
                                    {
                                        foreach (var session in jsbasket.listEventsHA)
                                        {
                                            if (!listDistinctsEvents.Contains(session.eventid))
                                                listDistinctsEvents.Add(session.eventid);
                                            if (!listDistinctsSessions.Contains(session.sessionid))
                                                listDistinctsSessions.Add(session.sessionid);


                                            foreach (customTarifHA tarif in session.listTarifs)
                                            {

                                                //var hashCalcule = getHash(tarif.priceId + "_" + tarif.vtsId + "_" + session.categid + "/" + session.sessionid, thisSalt);
                                                //if (tarif.hashKey != hashCalcule)
                                                //{
                                                //    log.LogMsg(structureId, LogLevel.ERROR, $"tarif hash reçu {tarif.hashKey}");
                                                //    log.LogMsg(structureId, LogLevel.ERROR, $"tarif hash calculé {hashCalcule} {tarif.priceId}_{tarif.vtsId}_{session.categid}/{session.sessionid}");

                                                //    baskIsValide = false;
                                                //}
                                                //else
                                                //{
                                                List<SeatEntity> listSeats = new List<SeatEntity>();

                                                if (tarif.listSeats != null)
                                                {
                                                    if (tarif.vtsId == 0)
                                                    {
                                                        // vtsId == 0 ???
                                                        log.LogMsg(structureId, LogLevel.WARN, "tarif.vts_id==0 ?! pour " + session.eventName + " " + session.sessionDescription + " tarifid=" + tarif.priceId + " (" + tarif.pricename + ")");
                                                        baskIsValide = false;
                                                    }
                                                    foreach (customFlaggedSeat seat in tarif.listSeats)
                                                    {
                                                        string hashKey = getHash(seat.seatid + "/" + session.sessionid, thisSalt);
                                                        if (seat.hashKey != hashKey)
                                                        {
                                                            baskIsValide = false;

                                                            log.LogMsg(structureId, LogLevel.ERROR, $"hash reçu {tarif.hashKey}");
                                                            log.LogMsg(structureId, LogLevel.ERROR, $"seat hash calculé {hashKey} {seat.seatid}/{session.sessionid}");


                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    log.LogMsg(structureId, LogLevel.WARN, "listSeats HA is null ?! pour " + session.eventName + " " + session.sessionDescription);
                                                }
                                                // }
                                            }
                                        }
                                    }
                                }

                                if (baskIsValide)
                                {
                                    bool thereIsPrintHome = false;


                                    List<ProductEntity> lstProducts = GetProducts(structureId, identiteId, listDistinctsFormulasId, langCode);

                                    myDictionary mySSC = new myDictionary();
                                    mySSC = mySSC.GetDictionaryFromCache(structureId);

                                    int offreIdHorsAbo = 0;
                                    if (mySSC.Contains("ABOV2OFFREIDHORSABO") && mySSC["ABOV2OFFREIDHORSABO"] != null && mySSC["ABOV2OFFREIDHORSABO"] != "")
                                    {
                                        offreIdHorsAbo = int.Parse(mySSC["ABOV2OFFREIDHORSABO"]);
                                    }



                                    List<EventEntity> listEventsFromDb = GetAllEvents(structureId, listDistinctsEvents, langCode);


                                    if (listEventsFromDb.Count == 0)
                                    {
                                        //erreur


                                    }


                                    List<GestionPlaceEntity> listMyGps = new List<GestionPlaceEntity>();
                                    List<int> listMyGpsId = new List<int>();
                                    foreach (int eventId in listDistinctsEvents)
                                    {
                                        // gp de l'evenements :
                                        List<GestionPlaceEntity> listGp = GetGrilleTarif(structureId, eventId, langCode, offreIdHorsAbo);
                                        // obtenir la liste de gp de la session 

                                        foreach (GestionPlaceEntity gpE in listGp)
                                        {
                                           // if (listDistinctsSessions.Contains(gpE.SessionId))
                                           // {
                                                listMyGps.Add(gpE);
                                                listMyGpsId.Add(gpE.gestion_place_id);
                                           // }
                                        }
                                    }

                                    string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];

                                    Dictionary<int, int> dicoOfGpsMaquettes = GetMaquettesOfGpMo(structureId, listMyGpsId, MObention.ProduitId);

                                    BasketsManager.SetInvalide(web_user_id, identiteId, typeRun, structureId);
                                    BasketEntity tbasket = BasketsManager.CreateBasket(typeRun, structureId, web_user_id, identiteId);


                                    //Recup url summary
                                    
                                    var application = System.Web.HttpContext.Current.Request.ApplicationPath;
                                    var controllerName = System.Web.HttpContext.Current.Request.RequestContext.RouteData.Values["controller"].ToString().ToLower();

                                    try
                                    {
                                        string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/');
                                        string urlOfSummary = $"{baseUrl}/{controllerName}";
                                        //Update url du basket
                                        BasketsManager.UpdateUrl(structureId, typeRun, tbasket.BasketId, urlOfSummary);

                                    }
                                    catch 
                                    {

                                    }
                                
                                    #region places d'abonnemnts 


                                    foreach (customFormulas formul in jsbasket.listFormulas)
                                    {
                                        foreach (customTarif tarif in formul.listTarifs)
                                        {
                                            foreach (customAbo abo in tarif.listAbos)
                                            {
                                                ws_DTO.FormulaGroupeEntity myAbo = new ws_DTO.FormulaGroupeEntity();
                                                myAbo.BasketId = tbasket.BasketId;
                                                myAbo.Identite_id = abo.identiteId == 0 ? identiteId : abo.identiteId;
                                                myAbo.Formula_id = abo.formulaid;
                                                myAbo.Formula_name = formul.formulaName;
                                                myAbo.Type_tarif_id = tarif.tarifid;
                                                myAbo.Type_tarif_name = tarif.tarifName;
                                                myAbo.NbPlace = 1;
                                                ws_bll.FormulaGroupeManager.Insert(typeRun, tbasket, ref myAbo);



                                                foreach (customEvent session in abo.listEvents)
                                                {

                                                    var thisEventFromDb = listEventsFromDb.Where(e => e.EventId == session.eventid).FirstOrDefault();
                                                    if (thisEventFromDb == null)
                                                    {
                                                        log.LogMsg(structureId, LogLevel.ERROR, "ne retrouve pas dans listEventsFromDb l'event " + session.eventid);
                                                    }
                                                    var thisSessionFromDb = thisEventFromDb.ListSessions.Where(e => e.SessionId == session.sessionid).FirstOrDefault();


                                                    var thisCategFromDb = thisSessionFromDb.ListCategories.Where(e => e.CategId == session.categid).FirstOrDefault();


                                                    if (session.listSeats != null)
                                                    {

                                                        foreach (customFlaggedSeat seat in session.listSeats)
                                                        {
                                                            SeatFormulaGroupeEntity mySeat = new SeatFormulaGroupeEntity();
                                                            mySeat.Manif_id = session.eventid;
                                                            mySeat.Seance_id = session.sessionid;
                                                            mySeat.Seat_id = seat.seatid;
                                                            mySeat.FormulaGroupeId = myAbo.FormulaGroupeId;
                                                            mySeat.Rank = seat.rank;
                                                            mySeat.Seat = seat.seat;
                                                            mySeat.Event_name = thisEventFromDb.EventName;
                                                            mySeat.Seance_description = thisSessionFromDb.sSessionStartDate;

                                                            mySeat.Type_envoi_id = MObention.ProduitId;

                                                            //  mySeat.Event_name = "TODO EventName";

                                                            mySeat.Contrainte = session.constraintId; // TODO ??
                                                            mySeat.GroupeId = session.constraintId; // TODO

                                                            mySeat.Categ_id = session.categid;
                                                            mySeat.Categ_name = thisCategFromDb.Category_name;


                                                            // TODO ... etc...
                                                            List<GestionPlaceEntity> listThisGps = listMyGps.Where(p => p.EventId == session.eventid
                                                                 && p.SessionId == session.sessionid
                                                                 && p.PriceId == tarif.tarifid
                                                                 && p.CategoryId == session.categid).ToList();
                                                            if (listThisGps.Count == 0)
                                                            {
                                                                // ne retrouve pas le tarif ?!?!
                                                                string msgError = "ne retrouve pas le tarif " + tarif.tarifid + " pour categ " + session.categid + ", seance " + session.sessionid + ", manif " + session.eventid + " !!";
                                                                Exception ex = new Exception(msgError);
                                                                throw ex;
                                                            }
                                                            else
                                                            {

                                                                var thisGp = listMyGps.Where(p => p.EventId == session.eventid
                                                                                           && p.SessionId == session.sessionid
                                                                                           && p.PriceId == tarif.tarifid
                                                                                           && p.CategoryId == session.categid
                                                                                           && p.formulaId == abo.formulaid).FirstOrDefault();


                                                                mySeat.MontantTTCinCent = thisGp.priceEnt.UnitTTCAmount;
                                                                mySeat.FraisinCent = thisGp.priceEnt.UnitFeeAmount;
                                                                mySeat.Vts_id = thisGp.priceEnt.VtsId;

                                                                mySeat.Gestion_place_id = thisGp.gestion_place_id;

                                                                if (dicoOfGpsMaquettes.ContainsKey(mySeat.Gestion_place_id))
                                                                    mySeat.Maquette_id = dicoOfGpsMaquettes[mySeat.Gestion_place_id];
                                                                else
                                                                {
                                                                    log.LogMsg(structureId, LogLevel.ERROR, "AddBasket:ne retrouve pas la maquetteid dans dicoOfGpsMaquettes pour gpId=" + mySeat.Gestion_place_id
                                                                        + "(" + seat.seatid + ",form=" + myAbo.Formula_id + ") et moId=" + MObention.ProduitId + "=> maquette passée à 0");
                                                                    mySeat.Maquette_id = 0; // ??????????
                                                                }

                                                                if (mySeat.Maquette_id > 0) // sauv le fait qu'on soit en print@home => passer l'info aux produits
                                                                    thereIsPrintHome = true;

                                                                if (thisGp.isPlacementLibre)
                                                                {

                                                                    mySeat.Rank = "LIBRE";
                                                                    mySeat.Seat = "LIBRE";
                                                                }
                                                                else
                                                                {
                                                                    mySeat.Rank = seat.rank;
                                                                    mySeat.Seat = seat.seat;
                                                                }
                                                                ws_bll.WT.SeatFormulaGroupeManager.Insert("", structureId, mySeat);

                                                            }
                                                        }
                                                    }


                                                }


                                                //ajout produits liés à la formule
                                                if (abo.listmandatoryproducts != null)
                                                {
                                                    foreach (ProductPur prod in abo.listmandatoryproducts)
                                                    {
                                                        if (prod != null)
                                                        {
                                                            if (lstProducts.Count > 0)
                                                            {
                                                                var thisprod = lstProducts.Where(prd => prd.ProduitId == prod.ProduitId).FirstOrDefault();
                                                                thisprod.Type_ligne = "PROD";
                                                                thisprod.Nombre = 1;
                                                                thisprod.Type_envoi_id = MObention.ProduitId;
                                                                if (!thereIsPrintHome)
                                                                    thisprod.Maquette_id = 0;

                                                                ws_bll.WT.ProductEntityManager.InsertFormuleProduit(typeRun, structureId, myAbo.FormulaGroupeId, thisprod);
                                                            }


                                                        }
                                                    }
                                                }


                                                //FormulaGroupeManager.Insert();
                                            }
                                        }
                                    }
                                    #endregion

                                    #region hors abo et places supp
                                    if (jsbasket.listEventsHA != null && jsbasket.listEventsHA.Count > 0)
                                    {
                                        foreach (customEventHA eventHA in jsbasket.listEventsHA)
                                        {
                                            foreach (customTarifHA tarif in eventHA.listTarifs)
                                            {
                                                List<SeatEntity> listSeats = new List<SeatEntity>();

                                                List<GestionPlaceEntity> listThisGps = listMyGps.Where(p => p.EventId == eventHA.eventid
                                                     && p.SessionId == eventHA.sessionid
                                                    && p.PriceId == tarif.priceId
                                                    && p.CategoryId == eventHA.categid // && p.gestion_place_id == tarif.gpid
                                                    ).ToList();

                                                if (tarif.listSeats != null)
                                                {

                                                    foreach (customFlaggedSeat seat in tarif.listSeats)
                                                    {
                                                        SeatEntity seatE = new SeatEntity();

                                                        seatE.Structureid = structureId.ToString("0000");

                                                        seatE.Manif_id = eventHA.eventid;
                                                        seatE.Event_name = eventHA.eventName;
                                                        seatE.Seance_id = eventHA.sessionid;
                                                        seatE.Seance_description = eventHA.sessionDescription;

                                                        seatE.Seat = seat.seat;
                                                        seatE.Rank = seat.rank;
                                                        seatE.Seat_id = seat.seatid;

                                                        seatE.FraisinCent = listThisGps[0].priceEnt.UnitFeeAmount;
                                                        seatE.MontantTTCinCent = listThisGps[0].priceEnt.UnitTTCAmount;

                                                        seatE.Type_tarif_id = tarif.priceId;
                                                        seatE.Type_tarif_name = tarif.pricename;
                                                        seatE.Vts_id = tarif.vtsId;

                                                        seatE.Categ_id = eventHA.categid;
                                                        seatE.Categ_name = (eventHA.categoryName == null) ? "" : eventHA.categoryName;

                                                        seatE.Event_name = eventHA.eventName;
                                                        seatE.Seance_description = eventHA.sessionDescription;
                                                        seatE.Gestion_place_id = tarif.gpid;

                                                        seatE.Maquette_id = dicoOfGpsMaquettes[tarif.gpid];

                                                        seatE.Type_envoi_id = MObention.ProduitId;

                                                        listSeats.Add(seatE);
                                                    }
                                                    BasketsManager.AddBasketLines(typeRun, tbasket, listSeats);
                                                }
                                            }
                                        }
                                    }
                                    #endregion

                                    #region mode d'obtention
                                    ProductEntity myModeObtentionFrais = new ProductEntity();
                                    myModeObtentionFrais.BasketId = tbasket.BasketId;
                                    myModeObtentionFrais.ProduitId = MObention.ProduitId;
                                    //va chercher le  montant et frais..
                                    myModeObtentionFrais.Product_name = MObention.Product_name;
                                    myModeObtentionFrais.MontantTTCinCent = MObention.MontantTTCinCent;
                                    myModeObtentionFrais.FraisinCent = 0;
                                    myModeObtentionFrais.Nombre = 1;
                                    myModeObtentionFrais.Type_ligne = "FRAIS";

                                    //ajouter au panier les mode obtention en tant que frais
                                    ProductEntityManager.Insert("", structureId, myModeObtentionFrais);
                                    #endregion

                                    // produit frais dossier :
                                    #region produits dossier
                                    List<ProductEntity> listProduitDossier = GetFraisProducts(structureId, identiteId, listDistinctsFormulasId, langCode);
                                    foreach (ProductEntity prod in listProduitDossier)
                                    {
                                        ProductEntity myprodDossier = new ProductEntity();
                                        myprodDossier.BasketId = tbasket.BasketId;
                                        myprodDossier.ProduitId = prod.ProduitId;
                                        //va chercher le  montant et frais..
                                        myprodDossier.Product_name = prod.Product_name;
                                        myprodDossier.MontantTTCinCent = prod.MontantTTCinCent;
                                        //myprodDossier.FraisinCent = 0;
                                        myprodDossier.Nombre = 1;
                                        myprodDossier.Type_ligne = "FRAIS";

                                        if (!thereIsPrintHome)
                                        {
                                            myprodDossier.Maquette_id = 0;
                                        }
                                        //myprodDossier.Maquette_id 

                                        //ajouter au panier les mode obtention en tant que frais
                                        ProductEntityManager.Insert("", structureId, myprodDossier);
                                    }
                                    #endregion

                                    #region produits questionnaires       

                                    List<ProductQuestionaire> listQuest = GetQuestionnaireProducts(structureId, identiteId, listDistinctsFormulasId, langCode);
                                    //List<ProductEntity> listProduitQuestionnaire = GetFraisProducts(structureId, identiteId, listDistinctsFormulasId, langCode);
                                    if (jsbasket.prodQuestionnaire != null)
                                    {
                                        foreach (ProductEntity prod in jsbasket.prodQuestionnaire)
                                        {
                                            //ProductEntity myprodDossier = new ProductEntity();
                                            prod.BasketId = tbasket.BasketId;
                                            //myprodDossier.ProduitId = prod.ProduitId;
                                            //va chercher le  montant et frais..


                                            //myprodDossier.FraisinCent = 0;

                                            prod.Type_ligne = "PROD";

                                            ProductEntityManager.delete("", tbasket, prod); // delete d'office
                                            if (prod.Nombre > 0)
                                            {
                                                //listQuest.Select(p =)
                                                var thisprodDB = listQuest.Where(prd => prd.ProduitId == prod.ProduitId).FirstOrDefault();
                                                if (thisprodDB == null)
                                                {
                                                    log.LogMsg(structureId, LogLevel.ERROR, "ne retrouve pas le produit questionnaire " + prod.ProduitId + " ?!?");
                                                }
                                                else
                                                {
                                                    prod.MontantTTCinCent = thisprodDB.MontantTTCinCent;
                                                    prod.Product_name = thisprodDB.Product_name;

                                                    ProductEntityManager.Insert("", structureId, prod);
                                                }
                                            }
                                        }
                                    }
                                    #endregion


                                    //return "url=" + urlOfPayment(structureId, tbasket.BasketId);
                                    if (tbasket == null)
                                    {
                                        log.LogMsg(structureId, LogLevel.ERROR, "dans AddBasket(" + structureId + ", " + identiteId + ",null) tbasket is null ?!");
                                    }
                                    else if (tbasket.BasketId == 0)
                                    {
                                        log.LogMsg(structureId, LogLevel.ERROR, "dans AddBasket(" + structureId + ", " + identiteId + ",tbasket) tbasket.BasketId =0 ?!");
                                    }
                                    // transformation panier ici !
                                    wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                                    wcf.BasketTransformation(structureId, tbasket.BasketId);

                                    return Json("url=" + urlOfPayment(structureId, tbasket.BasketId), JsonRequestBehavior.AllowGet);

                                }
                                else
                                {
                                    GestionTrace.WriteLogError(structureId, "AddBasket basket is not valid");
                                }
                            }
                        }
                    }
                }
                else
                {
                    // iidentite = 0 ?!?!!!
                }
            }
            catch (Exception ex)
            {
                log.LogMsg(structureId, LogLevel.ERROR, ex.Message + " " + ex.StackTrace);

                string sjsBasket = Newtonsoft.Json.JsonConvert.SerializeObject(jsbasket);

                log.LogMsg(structureId, LogLevel.ERROR, "dans AddBasket(" + structureId + ", " + identiteId + "," + sjsBasket + ")"); ;

                GestionTrace.WriteLogError(structureId, ex.Message);
                breturn = false;
            }
            //customBasketReturn
            return Json(breturn, JsonRequestBehavior.AllowGet);
        }

        public JsonResult GetHasKeyCustomerArea(int structureId, string absoluteUrl)
        {
            if (Session["structureId"] != null && Session["identiteId"] != null)
            {
                // GestionTrace.WriteLog(structureId, "GetHasKeyCustomerArea " + structureId + " absoluteUrl " + absoluteUrl);

                int thisStructureId = int.Parse(Session["structureId"].ToString());
                int thisIdentiteId = int.Parse(Session["identiteId"].ToString());

                if (structureId == thisStructureId)
                {

                    string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");

                    absoluteUrl = absoluteUrl + "&hash=[hash]";
                    absoluteUrl = absoluteUrl.Replace("[structureid]", thisStructureId.ToString("0000")).Replace("[identiteid]", thisIdentiteId.ToString());

                    GestionTrace.WriteLog(structureId, "GetHasKeyCustomerArea " + structureId + " absoluteUrl " + absoluteUrl);

                    Sha1 sha1 = new Sha1(absoluteUrl.ToUpper().Replace("/", "*") + cryptoKey);
                    string hash = sha1.getSha1();

                    absoluteUrl = absoluteUrl.Replace("[hash]", hash);

                    GestionTrace.WriteLog(structureId, "GetHasKeyCustomerArea " + structureId + " absoluteUrl " + absoluteUrl);

                    return Json(absoluteUrl, JsonRequestBehavior.AllowGet);
                }

            }
            return Json(null, JsonRequestBehavior.AllowGet);

        }

        public JsonResult InvalidBasket(int structureId, int identiteId, int userId)
        {

            if (userId > 0)
            {
                string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];
                var basketIsInvalid = BasketsManager.SetInvalide(userId, identiteId, typeRun, structureId);

                return Json(basketIsInvalid, JsonRequestBehavior.AllowGet);
            }
            return Json(null, JsonRequestBehavior.AllowGet);
        }

    }
}