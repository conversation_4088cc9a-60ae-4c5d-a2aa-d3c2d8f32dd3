﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Caching;
using System.Web.Configuration;
using System.Web.Mvc;
using utilitaires2010;
using utilitaires2010.sql.sqlserver;

namespace aboV2.App_Code
{

    /// <summary>
    /// Decorates any MVC route that needs to have client requests limited by time.
    /// </summary>
    /// <remarks>
    /// Uses the current System.Web.Caching.Cache to store each client request to the decorated route.
    /// </remarks>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class ThrottleAttribute : ActionFilterAttribute
    {
        /// <summary>
        /// A unique name for this Throttle.
        /// </summary>
        /// <remarks>
        /// We'll be inserting a Cache record based on this name and client IP, e.g. "Name-***********"
        /// </remarks>
        public string Name { get; set; }

        /// <summary>
        /// The number of seconds clients must wait before executing this decorated route again.
        /// </summary>
        public double milliSeconds { get; set; }

        /// <summary>
        /// A text message that will be sent to the client upon throttling.  You can include the token {n} to
        /// show this.Seconds in the message, e.g. "Wait {n} seconds before trying again".
        /// </summary>
        public string Message { get; set; }

        public override void OnActionExecuting(ActionExecutingContext c)
        {
            // en comm, cree des erreurs, à gerer mieux 

        //    var key = string.Concat(Name, "-", c.HttpContext.Request.UserHostAddress);
        //    var allowExecute = false;

        //    if (HttpRuntime.Cache[key] == null)
        //    {
        //        HttpRuntime.Cache.Add(key,
        //            true, // is this the smallest data we can have?
        //            null, // no dependencies
        //            DateTime.Now.AddMilliseconds(milliSeconds), // absolute expiration
        //            Cache.NoSlidingExpiration,
        //            CacheItemPriority.Low,
        //            null); // no callback

        //        allowExecute = true;
        //    }

        //    if (!allowExecute)
        //    {
        //        if (String.IsNullOrEmpty(Message))
        //            Message = "You may only perform this action every {n} seconds.";

        //        c.Result = new ContentResult { Content = Message.Replace("{n}", (milliSeconds / 1000).ToString()) };
        //        // see 409 - http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html
        //        c.HttpContext.Response.StatusCode = (int)HttpStatusCode.Forbidden;
        //    }
        }
    }

    //public class FileAttente
    //{

    //    static FileAttente()
    //    {
    //    }

    //    public Boolean IsFull(int _nbrMaxForMyStruct, int _sinceSecondsForMyStruct, int _idStructure, string typeRun)
    //    {
    //        try
    //        {

    //            int MaxUsersAllStructs = int.Parse(WebConfigurationManager.AppSettings["FileAttenteMaxActivesUsers"]);
    //            int SinceSecondsAllStruct = int.Parse(WebConfigurationManager.AppSettings["FileAttenteDelayInSeconds"]);
    //            if (_idStructure != 0)
    //            {
    //                int[] ArrayNbrConnexions;
    //                ArrayNbrConnexions = GetNumbersOfUsers(SinceSecondsAllStruct, _sinceSecondsForMyStruct, _idStructure, typeRun);
    //                return (ArrayNbrConnexions[0] >= MaxUsersAllStructs || ArrayNbrConnexions[1] >= _nbrMaxForMyStruct); // limite atteinte all ou limite atteinte mystruct
    //            }
    //            else
    //            {
    //                return GetNumbersOfUsers(SinceSecondsAllStruct, typeRun) > MaxUsersAllStructs;
    //            }
    //        }
    //        catch
    //        {
    //            return true;
    //        }
    //    }

    //    public Boolean IsFull()
    //    {
    //        // les params ne sont presents dans le config.ini de la structure, on prend les valeurs definies par defaut dans le web.config
    //        int MaxUsersDefault = int.Parse(WebConfigurationManager.AppSettings["FileAttenteMaxActivesUsersForOneStructDefaultValue"]);
    //        int SinceSecondsDefault = int.Parse(WebConfigurationManager.AppSettings["FileAttenteDelayInSecondsForOneStructDefaultValue"]);

    //        string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];

    //        return IsFull(MaxUsersDefault, SinceSecondsDefault, 0, typeRun);
    //    }

    //    private int GetNumbersOfUsers(int SinceSecondsAllStruct, string typeRun)
    //    {
    //        SqlServerConnexion sqlConn = null;
    //        SqlDataReader dsP = null;
    //        int n = 999999999;
    //        try
    //        {
    //            sqlConn = DBFunctions.ConnectWebTracing(typeRun);

    //            string sql = "SELECT count(distinct(user_id)) from users  WITH (READUNCOMMITTED) "
    //                + "WHERE start_date>dateadd(second, -" + SinceSecondsAllStruct.ToString() + ", getdate())";

    //            RequeteSelect reqS = new RequeteSelect();

    //            dsP = reqS.getReader(sqlConn.getCnx(), sql);
    //            if (dsP.HasRows)
    //            {
    //                dsP.Read();
    //                n = int.Parse(dsP[0].ToString());
    //            }
    //        }

    //        catch { }
    //        finally
    //        {
    //            if (dsP != null)
    //            {
    //                dsP.Close();
    //                dsP.Dispose();
    //            }

    //            if (sqlConn != null)
    //                sqlConn.closeConnection();
    //        }
    //        return n;
    //    }
    //    private int[] GetNumbersOfUsers(int SinceSecondsAllStruct, int SinceSecondsMyStruct, int structure_id, string typeRun)
    //    {
    //        int[] arrayToReturn = { 999999999, 999999999 };
    //        SqlServerConnexion sqlConn = null;
    //        DataSet ds = new DataSet();
    //        try
    //        {
    //            sqlConn = DBFunctions.ConnectWebTracing(typeRun);

    //            string sql = "/* all structs */ SELECT count(distinct(user_id)) from users WITH (READUNCOMMITTED) " + Environment.NewLine
    //                + "WHERE start_date>dateadd(second, -" + SinceSecondsAllStruct.ToString() + ", getdate());" + Environment.NewLine;

    //            sql += "/* this struct */ SELECT count(distinct(user_id)) from users WITH (READUNCOMMITTED) " + Environment.NewLine
    //                            + "WHERE start_date>dateadd(second, -" + SinceSecondsMyStruct.ToString() + ", getdate())" + Environment.NewLine
    //                + "AND structure_id=" + structure_id.ToString();

    //            RequeteSelect reqS = new RequeteSelect();

    //            reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref ds, "filatt");
    //            if (ds.Tables.Count == 2 && ds.Tables[0].Rows.Count == 1 && ds.Tables[1].Rows.Count == 1)
    //            {
    //                arrayToReturn[0] = int.Parse(ds.Tables[0].Rows[0][0].ToString());
    //                arrayToReturn[1] = int.Parse(ds.Tables[1].Rows[0][0].ToString());
    //            }
    //            else
    //            {
    //                //dsP.Close();
    //                arrayToReturn[0] = 999999999;
    //                arrayToReturn[1] = 999999999;
    //            }
    //        }
    //        catch
    //        {
    //            arrayToReturn[0] = 999999999;
    //            arrayToReturn[1] = 999999999;
    //        }
    //        finally
    //        {
    //            if (ds != null)
    //                ds.Dispose();

    //            if (sqlConn != null)
    //                sqlConn.closeConnection();

    //        }
    //        return arrayToReturn;
    //    }
    //}

}