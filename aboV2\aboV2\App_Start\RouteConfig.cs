﻿using System.Web.Mvc;
using System.Web.Routing;

namespace aboV2
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");



            /*  routes.MapRoute(
                  name: "Formulas",
                  url: "Formulas/{action}/{structureId}/{identiteId}/{langCode}",
                      defaults: new
                      {
                          controller = "Formulas",
                          action = "Load",
                          structureId = UrlParameter.Optional,
                          identiteId = UrlParameter.Optional,
                          langCode = UrlParameter.Optional,
                      },
                      constraints: new { structureId = @"\d+", identiteId = @"\d+", langCode = @"\w{2}" }
              );

              routes.MapRoute(
                  name: "FormulasEdit",
                  url: "Formulas/{action}/{id}",
                  defaults: new
                  {
                      controller = "Formulas",
                      action = "Edit",
                      id = UrlParameter.Optional

                  }
                  );
                  */

            routes.MapRoute(
                  name: "Events",
                  url: "Events/{action}/{structureId}/{identiteId}/{langCode}/{formulaId}",
                      defaults: new
                      {
                          controller = "Events",
                          action = "Load",
                          structureId = UrlParameter.Optional,
                          identiteId = UrlParameter.Optional,
                          langCode = UrlParameter.Optional,
                          formulaId = UrlParameter.Optional
                      },
                      constraints: new { structureId = @"\d+", identiteId = @"\d+", langCode = @"\w{2}" }
              );




            routes.MapRoute(
                name: "HomePlusParam",
                url: "Home/{structureId}/{langCode}/{paramOptionnel}",
                defaults: new
                {
                    controller = "Home",
                    action = "Index",
                    structureId = UrlParameter.Optional,
                    langCode = UrlParameter.Optional,
                    paramOptionnel = UrlParameter.Optional
                }
            // constraints: new { structureId = @"\d+", langCode = @"\w{2}" }
            );

            routes.MapRoute(
                name: "HomeDepuisQueuing",
                url: "Home/{structureId}/{langCode}/{tnQueuing}/{hashKQueuing}",
                defaults: new
                {
                    controller = "Home",
                    action = "Index",
                    structureId = UrlParameter.Optional,
                    langCode = UrlParameter.Optional,
                    tnQueuing = UrlParameter.Optional,
                    hashKQueuing = UrlParameter.Optional
                }
            );

            routes.MapRoute(
                name: "HomeDepuisQueuingAvecParamOptionnel",
                url: "Home/{structureId}/{langCode}/{paramOptionnel}/{tnQueuing}/{hashKQueuing}",
                defaults: new
                {
                    controller = "Home",
                    action = "Index",
                    structureId = UrlParameter.Optional,
                    langCode = UrlParameter.Optional,
                    paramOptionnel = UrlParameter.Optional,
                    tnQueuing = UrlParameter.Optional,
                    hashKQueuing = UrlParameter.Optional
                }
            );

            routes.MapRoute(
                  name: "Home",
                  url: "Home/{structureId}/{langCode}",
                    defaults: new
                    {
                        controller = "Home",
                        action = "Index",
                        structureId = UrlParameter.Optional,
                        langCode = UrlParameter.Optional
                    }
              // constraints: new { structureId = @"\d+", langCode = @"\w{2}" }
              );



            routes.MapRoute(
             name: "SeatsPlan",
            url: "SeatsPlan/{action}/{structureId}/{identiteId}/{eventId}/{sessionId}/{categId}/{langCode}/{typeHorsAbo}/{lstGestionPlaceId}/{zoneId}/{floorId}/{sectionId}/{nbSeats}/{sessionIdOrigine}",
           defaults: new
           {
               controller = "SeatsPlan",
               action = "Load",
               structureId = UrlParameter.Optional,
               identiteId = UrlParameter.Optional,
               eventId = UrlParameter.Optional,
               sessionId = UrlParameter.Optional,
               categId = UrlParameter.Optional,
               langCode = UrlParameter.Optional,
               typeHorsAbo = UrlParameter.Optional,
               lstGestionPlaceId = UrlParameter.Optional,
               zoneId = UrlParameter.Optional,
               floorId = UrlParameter.Optional,
               sectionId = UrlParameter.Optional,
               aboidx = UrlParameter.Optional,
               nbSeats = UrlParameter.Optional,
               sessionIdOrigine = UrlParameter.Optional
           }
        );

            //$(this).attr('href', "/HorsAbo/partIndex/347/0/todo/" + evenidid + "/" + sessionid + "/fr");

            routes.MapRoute(
                    name: "HorsAbo",
                    url: "HorsAbo/{action}/{structureId}/{identiteId}/{formulaId}/{eventId}/{sessionId}/{zoneId}/{floorId}/{sectionId}/{categId}/{aboidx}/{langCode}",
                    //url: "HorsAbo/{action}/{structureId}/{identiteId}/{formulaId}/{eventId}/{sessionId}/{langCode}",
                    defaults: new
                    {
                        controller = "HorsAbo",
                        action = "partIndex",
                        structureId = UrlParameter.Optional,
                        identiteId = UrlParameter.Optional,
                        eventId = UrlParameter.Optional,
                        sessionId = UrlParameter.Optional,
                        zoneId = UrlParameter.Optional,
                        floorId = UrlParameter.Optional,
                        sectionId = UrlParameter.Optional,
                        categId = UrlParameter.Optional,
                        aboidx = UrlParameter.Optional,
                        langCode = UrlParameter.Optional
                    }
            );


            //Load(int structureId, int identiteId, int eventId, int sessionId, string langCode)

            routes.MapRoute(
                            name: "Default",
                            url: "{controller}/{action}/{*id}",
                            defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
                        );
        }

    }
}

