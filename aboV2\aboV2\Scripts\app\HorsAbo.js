﻿var oEventGp;

$(document).ready(function () {

    //  $('table#prices').hide();
    resizeParentIframe();

    Translate(false);
    Translate(true);

    $.ajax({
        type: "POST",
        url: urlajaxabov2 + '/HorsAbo/LoadGT',
        data: {
            structureId: structureIdCurrent,
            identiteId: identityCurrent,
            langCode: langCodeCurrent,
            listformulaId: "",
            eventId: eventIdCurrent,
            sessionId: sessionIdCurrent,
            categId: categIdCurrent
        },
        success: function (retour) {

            //retour.contains('error:'
            if ($.isPlainObject(retour)) {

                DrawEvent(retour);
                oEventGp = retour;

                //affiche les boutons generic ==> annuler et valider
                $('#spectacleHorsAboGenericButtons').removeClass('d-none');
            } else {
                if (retour.contains('error:')) {
                    //affiche le message d'erreur avec le bouton fermer					
                    $('#spectacleHorsAboMsgError').removeClass('d-none');
                    $('#spectacleHorsAboMsgError .text-danger').html(retour.split(':')[1]);

                    $('#spectacleHorsAboErrorButtons').removeClass('d-none');
                }
                //parent.tempAlert(retour.split(':')[1]);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        }, complete: function () {
            $('#loadingcircle-modal').addClass('d-none');
        }


    });
    //CheckChoice();


    $('#cancelExtraShow, #cancelErrorExtraShow').on("click", function (e) {
        window.parent.$('#modalGeneric').modal('hide');
    });

    $('#addExtraShow').on("click", function (e, i) {
        //window.parent.modalTypeHorsAbo = window.parent.$('#modalGeneric .modal-body').data('type');
        //window.parent.$('#modalGeneric .modal-body').data('ishorsabo', false)
        var modalTypeHorsAbo = window.parent.$('#modalGeneric .modal-body').data('type');


        nb = 0;

        $.each($('input[name="price"]'), function (i, itminput) {
            thisNb = parseInt($(itminput).val());
            if (thisNb > 0) {
                nb += thisNb;
            }
        });

        var sessionid = $('#prices').data('sessionid');
        var zoneid = $('#prices').data('zoneid');
        var floorid = $('#prices').data('floorid');
        var sectionid = $('#prices').data('sectionid');
        var categoryid = $('#prices').data('categoryid');

        allisOk = true; // TODO nbr total de places, etc...

        if (nb > 0 && allisOk) {
            var arrBasket = localStorage.getObj("arrPanierHA");
            if (arrBasket == null) {
                var arrBasket = {};
                arrBasket.listEventsHA = [];
            }
            else {
                if (arrBasket.listEventsHA == undefined)
                    arrBasket.listEventsHA = [];
            }

            if (Enumerable.From(arrBasket.listEventsHA).Where(function (x) { return x.eventid == eventIdCurrent }).ToArray().length > 0) {
                console.log("supp old choice");

                /*	arrBasket.listEventsHA = $.grep(arrBasket.listEventsHA, function(x) {
                     return x.eventid == eventIdCurrent && x.sessionid==sessionid && x.zoneid==zoneid && x.floorid == floorid && x.sectionid == sectionid && x.categoryid == categoryid;
                    })*/

                //supprime toutes les manifs qui ne sont pas égales a eventIdCurrent
                var arrSameInfos = $.grep(arrBasket.listEventsHA, function (x) {
                    return x.eventid == eventIdCurrent && x.sessionid == sessionid && x.zoneid == zoneid && x.floorid == floorid && x.sectionid == sectionid && x.categid == categoryid;
                });


                if (arrSameInfos.length > 0) {
                    arrBasket.listEventsHA = arrBasket.listEventsHA.filter(function (el) {
                        return el != arrSameInfos[0]
                    })

                }

                /* $.each(Enumerable.From(arrBasket.listEventsHA).Where(function (x) { return x.eventid == eventIdCurrent }).ToArray(), function (i, e) {
                     e.eventid = 0; // pour supp l'ancien choix
                 });
 */


                //arrBasket.listEventsHA = $.grep(arrBasket.listEventsHA, function(x) {  return x.eventid != 0 });
                /*arrBasket.listEventsHA = $.grep(arrBasket.listEventsHA, function(x) {
				 return x.eventid != 0;
				})*/

                //events.push(oevent)
            }


            var typeName = "";
            switch (modalTypeHorsAbo) {
                case "HA":
                    typeName = ReadXmlTranslate("lbl_events_HA")
                    break;

                case "PS":
                    typeName = ReadXmlTranslate("lbl_events_PS");
                    break;
            }

            var objChoixHA = {};
            objChoixHA.listTarifs = [];
            objChoixHA.eventid = eventIdCurrent;
            objChoixHA.eventName = oEventGp.EventName;
            objChoixHA.sessionid = sessionid;
            //objChoixHA.sessionStartDate = ($('#sessions select').length == 0) ? $('#sessions span').text() : $('#sessions select option:selected ').text()

            objChoixHA.zoneid = zoneid;
            objChoixHA.floorid = floorid;
            objChoixHA.floorName = ($('#etages select').length == 0) ? $('#etages  span').text() : $('#etages select option:selected ').text()
            objChoixHA.sectionid = sectionid;
            objChoixHA.sectionName = ($('#sections select').length == 0) ? $('#sections  span').text() : $('#sections select option:selected ').text()


            objChoixHA.categid = categoryid;
            objChoixHA.type = modalTypeHorsAbo;
            objChoixHA.typeName = typeName;

            var objthisSession = Enumerable.From(oEventGp.ListSessions).Where(function (x) { return x.SessionId == sessionid }).ToArray()[0];

            objChoixHA.sessionDescription = objthisSession.sSessionStartDate;
            objChoixHA.lieuName = objthisSession.lieuName;
            categName = Enumerable.From(objthisSession.ListCategories).Where(function (x) { return x.CategId == categoryid }).ToArray()[0].Category_name;
            objChoixHA.categoryName = categName;



            $.each($('.priceLine input[name="price"]'), function (i, opt) {
                thisNb = parseInt($(opt).val());
                if (thisNb > 0) {
                    var objTarif = {}
                    objTarif.nb = thisNb;

                    gpid = $(this).closest('.priceLine').data('gpid');
                    objTarif.gpid = gpid;
                    thisGp = Enumerable.From(objthisSession.listGps).Where(function (x) { return x.gestion_place_id == gpid }).ToArray();
                    objTarif.priceId = thisGp[0].priceEnt.PriceId;
                    objTarif.pricename = thisGp[0].priceEnt.Price_name;
                    objTarif.unitTTCAmount = thisGp[0].priceEnt.UnitTTCAmount;
                    objTarif.vtsId = thisGp[0].priceEnt.VtsId;
                    objTarif.hashKey = thisGp[0].priceEnt.hashKey;

                    objTarif.isChoixPlaceSurPlan = thisGp[0].isAutomatique;
                    objTarif.isPlacementLibre = thisGp[0].isPlacementLibre;
                    objTarif.isVoirPlacement = thisGp[0].isSurPlan;


                    objChoixHA.listTarifs.push(objTarif)

                    // addToBasketT 

                }
            });
            arrBasket.listEventsHA.push(objChoixHA);
        }

        if (arrBasket != undefined)
            localStorage.setObj("arrPanierHA", arrBasket);

        if (window.parent == window.top) {
            window.parent.$('#modalGeneric').modal('hide');
        }
        //alert('nb=' + nb);
    });


});

/*function CheckChoice() {
    nb = 0;
    $.each($('.priceLine select option:selected'), function (i, opt) {
        nb += parseInt($(opt).val());
    });
    if (nb > 0) {
        $("#btnValidChoiceGp").removeAttr("disabled");
    }
    else {
        $("#btnValidChoiceGp").attr("disabled", "disabled");
    }
}
*/
function DrawEvent(oEvent) {
    $('.eventName').html(oEvent.EventName);
    $('.eventName').attr('data-eventid', oEvent.EventId);

    listSessions = Enumerable.From(oEvent.ListSessions).Where(function (x) {
        return x.SessionId != 0 && x.listGps.length > 0
    }).ToArray();

    DrawSession(listSessions);

}

function DrawSession(listSessions) {
    if (listSessions.length == 1) {
        //ajout dans la div id=sessions		
        $('#sessions').append('<span data-sessionid="' + listSessions[0].SessionId + '" >' + listSessions[0].sSessionStartDate + '</span>');

        DrawZone(listSessions[0].listGps, listSessions[0].SessionId)
    }
    else {
        //+sieurs sessions

        var $select = $("<select class='selectSession'></select>");
        $select.append("<option data-sessionid='0' value='0'>Sélectionner une séance</option>");
        $.each(listSessions, function (i, oSession) {
            $select.append("<option data-sessionid='" + oSession.SessionId + "' value='" + oSession.SessionId + "'>" + oSession.sSessionStartDate + "</option>")
        });
        // <option data-dispo="high" data-thumbnail="img/dispo-green.png" value="jeudi 25 janvier 2018 - 19:30" data-price="17,50€">jeudi 25 janvier 2018 - 19:30</option>
        $('#sessions').append($select);


        $select.on("change", function (i, e) {
            $('#prices .priceLine').remove();
            sessionId = $(this).find("option:selected").data("sessionid");
            var objthisSession = Enumerable.From(listSessions).Where(function (x) { return x.SessionId == sessionId }).ToArray()[0];

            if (objthisSession == 0) {
                $select.parent().nextAll().find('select, .sphorsabooption').remove();
                $('#prices').addClass('d-none');
            }
            if (sessionId > 0)
                DrawZone(objthisSession.listGps, sessionId);
            else {
                $(this).parent().nextAll().find('select, .sphorsabooption').remove();
                $('#prices').addClass('d-none');
            }
        });

        /*  var $select = $("<select class='selectSession'></select>");
          $.each(listSessions, function (i, oSession) {
              $select.append("<option data-sessionid='" + oSession.SessionId + "' val='" + oSession.SessionId + "'>" + oSession.sSessionStartDate + "</option>")
          });
          $select.appendTo($('td.session'));
  
  
          var sessionidselected = $select.find('option:selected').data('sessionid');
          DrawZone(listSessions[0].listGps, sessionidselected);
  
  
  
          $select.on("change", function (i, e) {
              $('#prices .priceLine').remove();
              sessionId = $(this).find("option:selected").data("sessionid");
              var objthisSession = Enumerable.From(listSessions).Where(function (x) { return x.SessionId == sessionId }).ToArray()[0];
  
              DrawZone(objthisSession.listGps, sessionId);
  
          });*/
    }
}

function DrawZone(tabGp, sessionId) {
    arrDistinctZones = [];
    $('#zones').html("");

    var objthisSession = Enumerable.From(tabGp).Where(function (x) { return x.SessionId == sessionId }).ToArray();
    arrDistinctZones = Enumerable.From(objthisSession).Distinct(function (x) { return x.ZoneId; }).Select(function (a) { return a; }).ToArray();

    $('#zones').data('sessionid', sessionId);

    if (arrDistinctZones.length == 1) {
        $('#zones').append('<span data-zoneid="' + listSessions[0].ZoneId + '" class="sphorsabooption">' + arrDistinctZones[0].ZoneName + '</span>');

        DrawEtage(tabGp, sessionId, arrDistinctZones[0].ZoneId)
    }
    else {
        // + sieurs zones      
        var $select = $("<select class='selectZone'></select>");
        $select.append("<option data-zoneid='0' value='0'>Zone</option>");
        $.each(arrDistinctZones, function (i, oGp) {

            // if (oGp.FloorId == categIdCurrent && categIdCurrent > 0)
            //     $select.append("<option data-floorid='" + oGp.FloorId + "' val='" + oGp.FloorId + "'>" + oGp.FloorName + "</option>");
            // else
            $select.append("<option data-zoneid='" + oGp.ZoneId + "' value='" + oGp.ZoneId + "'>" + oGp.ZoneName + "</option>");
        });
        $('#zones').append($select);

        var zoneidselected = $select.find('option:selected').data('zoneid');

        if (zoneidselected == 0) {
            $select.parent().nextAll().find('select, .sphorsabooption').remove();
            $('#prices').addClass('d-none');
        }
        // DrawSection(tabGp, sessionId, zoneId, flooridselected);

        $select.on("change", function (i, e) {
            $('#prices .priceLine').remove();
            thisZone = $(this).find("option:selected").data("zoneid");

            //si un étage alors on draw les section sinon on remove
            if (thisZone > 0)
                DrawEtage(tabGp, sessionId, thisZone);
            else {
                $(this).parent().nextAll().find('select, .sphorsabooption').remove();
                $('#prices').addClass('d-none');
            }
        });


    }
}
function DrawEtage(tabGp, sessionId, zoneId) {

    $('#etages').html("");

    arrDistinctFloors = [];
    var objthisZone = Enumerable.From(tabGp).Where(function (x) { return x.SessionId == sessionId && x.ZoneId == zoneId }).ToArray();

    arrDistinctFloors = Enumerable.From(objthisZone).Distinct(function (x) { return x.FloorId; }).Select(function (a) { return a; }).ToArray();

    $('#etages').data('sessionid', sessionId);
    $('#etages').data('zoneid', zoneId);

    if (arrDistinctFloors.length == 1) {

        $('#etages').append('<span data-floorid="' + arrDistinctFloors[0].FloorId + '"  class="sphorsabooption">' + arrDistinctFloors[0].FloorName + '</span>');

        DrawSection(tabGp, sessionId, zoneId, arrDistinctFloors[0].FloorId);

    }
    else {
        // + sieurs étages
        var $select = $("<select class='selectFloor'></select>");
        $select.append("<option data-floorid='0' value='0'>Etage</option>");
        $.each(arrDistinctFloors, function (i, oGp) {

            // if (oGp.FloorId == categIdCurrent && categIdCurrent > 0)
            //     $select.append("<option data-floorid='" + oGp.FloorId + "' val='" + oGp.FloorId + "'>" + oGp.FloorName + "</option>");
            // else
            $select.append("<option data-floorid='" + oGp.FloorId + "' value='" + oGp.FloorId + "'>" + oGp.FloorName + "</option>");
        });

        $('#etages').append($select);

        var flooridselected = $select.find('option:selected').data('floorid');

        if (flooridselected == 0) {
            $select.parent().nextAll().find('select, .sphorsabooption').remove();
            $('#prices').addClass('d-none');
        }
        // DrawSection(tabGp, sessionId, zoneId, flooridselected);

        $select.on("change", function (i, e) {
            $('#prices .priceLine').remove();
            thisFloor = $(this).find("option:selected").data("floorid");

            //si un étage alors on draw les section sinon on remove
            if (thisFloor > 0)
                DrawSection(tabGp, sessionId, zoneId, thisFloor);
            else {
                $(this).parent().nextAll().find('select, .sphorsabooption').remove();
                $('#prices').addClass('d-none');
            }
        });
    }
}
function DrawSection(tabGp, sessionId, zoneId, floorId) {
    arrDistinctSections = [];

    $('#sections').html("");

    objthisFloor = Enumerable.From(tabGp).Where(function (x) { return x.SessionId == sessionId && x.ZoneId == zoneId && x.FloorId == floorId }).ToArray();
    arrDistinctSections = Enumerable.From(objthisFloor).Distinct(function (x) { return x.SectionId; }).Select(function (a) { return a; }).ToArray();

    $('#sections').attr('data-sessionid', sessionId);
    $('#sections').attr('data-zoneid', zoneId);
    $('#sections').attr('data-floorid', floorId);

    if (arrDistinctSections.length == 1) {
        $('#sections').append('<span  data-sectionid="' + arrDistinctSections[0].SectionId + '"  class="sphorsabooption">' + arrDistinctSections[0].SectionName + '</span>');

        DrawCategs(tabGp, sessionId, zoneId, floorId, arrDistinctSections[0].SectionId);
    }
    else {
        // + sieurs section


        var $select = $("<select class='selectSections'></select>");
        $select.append("<option data-sectionid='0' value='0'>Sections</option>");
        $.each(arrDistinctSections, function (i, oGp) {

            // if (oGp.CategoryId == categIdCurrent && categIdCurrent > 0)
            //    $select.append("<option data-sectionid='" + oGp.SectionId + "' val='" + oGp.CategoryId + "'>" + oGp.CategoryName + "</option>");
            //else
            $select.append("<option data-sectionid='" + oGp.SectionId + "' value='" + oGp.SectionId + "'>" + oGp.SectionName + "</option>");
        });
        //$select.appendTo($('td.category'));

        $('#sections').append($select);
        var sectionidselected = $select.find('option:selected').data('sectionid');

        if (sectionidselected == 0) {
            $select.parent().nextAll().find('select, .sphorsabooption').remove();
            $('#prices').addClass('d-none');
        }
        // DrawCategs(tabGp, sessionId, zoneId, floorId, sectionidselected);

        $select.on("change", function (i, e) {
            $('#prices .priceLine').remove();
            thisSection = $(this).find("option:selected").data("sectionid");
            if (thisSection > 0)
                DrawCategs(tabGp, sessionId, zoneId, floorId, thisSection);
            else {
                $(this).parent().nextAll().find('select, .sphorsabooption').remove();
                $('#prices').addClass('d-none');
            }
        });
    }
}
function DrawCategs(tabGp, sessionId, zoneId, floorId, sectionId) {

    $('#categories').html("");

    arrDistinctCategs = [];


    objthisSection = Enumerable.From(tabGp).Where(function (x) { return x.SessionId == sessionId && x.ZoneId == zoneId && x.FloorId == floorId && x.SectionId == sectionId }).ToArray();
    arrDistinctCategs = Enumerable.From(objthisSection).Distinct(function (x) { return x.CategoryId; }).Select(function (a) { return a; }).ToArray();

    $('#categories').data('sessionid', sessionId);
    $('#categories').data('zoneid', zoneId);
    $('#categories').data('floorid', floorId);
    $('#categories').data('sectionid', sectionId);

    if (arrDistinctCategs.length == 1) {
        $('#categories').append('<span data-categoryid="' + arrDistinctCategs[0].CategoryId + '"  class="sphorsabooption">' + arrDistinctCategs[0].CategoryName + '</span>');

        DrawPrices(tabGp, sessionId, zoneId, floorId, sectionId, arrDistinctCategs[0].CategoryId);

    }
    else {
        // + sieurs categs
        var $select = $("<select class='selectCateg'></select>");
        $select.append("<option data-categoryid='0' value='0'>Catégories</option>");
        $.each(arrDistinctCategs, function (i, oGp) {

            if (oGp.CategoryId == categIdCurrent && categIdCurrent > 0)
                $select.append("<option data-categoryid='" + oGp.CategoryId + "' value='" + oGp.CategoryId + "'>" + oGp.CategoryName + "</option>");
            else
                $select.append("<option data-categoryid='" + oGp.CategoryId + "' value='" + oGp.CategoryId + "'>" + oGp.CategoryName + "</option>");
        });
        $select.appendTo($('td.category'));


        $('#categories').append($select);

        var categidselected = $select.find('option:selected').data('categoryid');

        // DrawPrices(tabGp, sessionId, zoneId, floorId, sectionId, categidselected);

        if (categidselected == 0)
            $('#prices').addClass('d-none');

        $select.on("change", function (i, e) {
            $('#prices .priceLine').remove();
            thisCateg = $(this).find("option:selected").data("categoryid");
            //si une categ alors on draw les le tableau 
            if (thisCateg > 0)
                DrawPrices(tabGp, sessionId, zoneId, floorId, sectionId, thisCateg);
            else
                $('#prices').addClass('d-none');

        });

    }
}

function DrawPrices(tabGp, sessionId, zoneId, floorId, sectionId, categId) {
    arrDistinctPrices = [];

    $('#prices .priceLine').remove();


    $('#prices').data('sessionid', sessionId);
    $('#prices').data('zoneid', zoneId);
    $('#prices').data('floorid', floorId);
    $('#prices').data('sectionid', sectionId);
    $('#prices').data('categoryid', categId);


    objthisCateg = Enumerable.From(tabGp).Where(function (x) { return x.SessionId == sessionId && x.ZoneId == zoneId && x.FloorId == floorId && x.SectionId == sectionId && x.CategoryId == categId }).ToArray();

    //récupère les valeurs des tarifs sélectionnés précédemment
    var arrBasketHA = localStorage.getObj("arrPanierHA");
    var arrOfTarifs = [];
    if (arrBasketHA != null)
        arrOfTarifs = $.grep(arrBasketHA.listEventsHA, function (x) { return x.zoneid == zoneId && x.sessionid == sessionId && x.floorid == floorId && x.sectionid == sectionId && x.categid == categId })


    if (objthisCateg.length > 0) {
        $('#prices').removeClass('d-none')
        $.each(objthisCateg, function (i, oPrice) {

            var objPrice = oPrice.priceEnt;
            var $tr = $('<tr class="priceLine" data-gpId="' + oPrice.gestion_place_id + '"></tr>');

            var $tds = '';
            if (arrOfTarifs.length == 1) {
                var thistarif = Enumerable.From(arrOfTarifs[0].listTarifs).Where(function (x) { return x.priceid == objPrice.PriceId && x.gpid == oPrice.gestion_place_id }).ToArray();

                if (thistarif.length == 1) {
                    $tds = $(
					'<td data-type="nbplace"><input id="tarifunique-' + objPrice.PriceId + '" data-desc="' + objPrice.Price_name + '" data-price="' + objPrice.UnitTTCAmount + '" type="text" value="' + thistarif[0].nb + '" name="price" class="form-control touchspin" data-bts-min="' + oPrice.nbMin + '" data-bts-max="' + oPrice.nbMax + '"></td>' +
					'<td>' + objPrice.Price_name + '</td>' +
					'<td><span class="aboprice">' + (parseInt(objPrice.UnitTTCAmount) / 100).toFixed(2) + ' €</span></td>'
					)
                } else {
                    $tds = $(
					'<td data-type="nbplace"><input id="tarifunique-' + objPrice.PriceId + '" data-desc="' + objPrice.Price_name + '" data-price="' + objPrice.UnitTTCAmount + '" type="text" value="0" name="price" class="form-control touchspin" data-bts-min="' + oPrice.nbMin + '" data-bts-max="' + oPrice.nbMax + '"></td>' +
					'<td>' + objPrice.Price_name + '</td>' +
					'<td><span class="aboprice">' + (parseInt(objPrice.UnitTTCAmount) / 100).toFixed(2) + ' €</span></td>'
					)
                }

            } else {
                $tds = $(
				'<td data-type="nbplace"><input id="tarifunique-' + objPrice.PriceId + '" data-desc="' + objPrice.Price_name + '" data-price="' + objPrice.UnitTTCAmount + '" type="text" value="0" name="price" class="form-control touchspin" data-bts-min="' + oPrice.nbMin + '" data-bts-max="' + oPrice.nbMax + '"></td>' +
				'<td>' + objPrice.Price_name + '</td>' +
				'<td><span class="aboprice">' + (parseInt(objPrice.UnitTTCAmount) / 100).toFixed(2) + ' €</span></td>'
				)

            }



            $tr.append($tds)
            // $tr.data("gpId", oPrice.gestion_place_id);
            $tr.appendTo($('#prices'));

        });

        /*
		$('.priceLine select').change(function (i, e) {
			CheckChoice()
		});*/


        $('#prices .priceLine').hide();
        $('#prices .priceLine').fadeIn(1000);
        //supprime la ligne total précédemment créée
        $('#prices tr.trtotal').remove();
        //ajoute la ligne total
        var $trtotal = $('<tr class="table-info trtotal" ></tr>');
        var $tdTotal = $('<td>' + ReadXmlTranslate("lbl_total_HA") + '</td>' +
			'<td  class="nbPlaceTotal"></td>' +
			'<td class="totalprice"> </td>'
			)
        $trtotal.append($tdTotal);
        $('#prices').append($trtotal);


        $(".touchspin").TouchSpin();

        /*var totalPrice=0;
		var nbPlaceTotal = 0;*/
        $('input[name="price"]').on('change', function () {


            UpdateTotalRow();
            /*	totalPrice += $(this).data('price')
            nbPlaceTotal += parseInt(nbprice);
            
            
            $('#prices tr.trtotal td.nbPlaceTotal').html(nbPlaceTotal);
            $('#prices tr.trtotal td.totalprice').html((parseInt(totalPrice) / 100).toFixed(2) + ' €');*/


        });

        resizeParentIframe();
    } else {
        console.log("aucune categ pour la GP");
    }
}


//post un objet en message au parent avec la propriete height et l'id de l'iframe
function resizeParentIframe() {
    var heightpage = $(document).height();
    var objmessage = { heightpage: heightpage, id: "iframeplacesupp" };

    window.parent.postMessage(objmessage, "*");
    //window.parent.postMessage("height:" + heightpage , "*");

    $(window).resize(function (event) {
        var heightpage = $('body').height();
        var objmessage = { heightpage: heightpage, id: "iframeplacesupp" };

        window.parent.postMessage(objmessage, "*");
        // window.parent.postMessage("height:" + heightpage , "*");
    });
}

function UpdateTotalRow() {

    var totalPrice = 0;
    var nbPlaceTotal = 0;
    $.each($('tr.priceLine'), function (indx, itemrow) {

        var nbplacechoisie = parseInt($(itemrow).find('td[data-type="nbplace"] input').val());

        nbPlaceTotal += nbplacechoisie;
        totalPrice += (nbplacechoisie * $(itemrow).find('td[data-type="nbplace"] input').data('price'));

        console.log($(itemrow));
    });


    if (totalPrice > 0) {
        $('#addExtraShow').attr('disabled', false);
    } else {
        $('#addExtraShow').attr('disabled', true);
    }

    $('#prices tr.trtotal td.nbPlaceTotal').html(nbPlaceTotal);
    $('#prices tr.trtotal td.totalprice').html((parseInt(totalPrice) / 100).toFixed(2) + ' €');

}