﻿
//var domaineCustomerArea = "http://localhost:61720";
//var domaineCustomerArea = "http://localhost:64349";
var domaineCustomerArea = "https://customer.themisweb.fr";

$(document).ready(function () {

    /*
	var previousBasket = sessionStorage.getObj("previousBasket");				
	if(previousBasket != null)
	{
		var listEventsHA = { "listEventsHA" : previousBasket.customBasket.listEventsHA }
		localStorage.setObj('arrPanierHA', listEventsHA)
	}
	*/

    if (identiteIdCurrent > 0) {
        var urlCA = 'https://test.themisweb.fr/customerv2/linkedconsumerslist.aspx?idstructure=[structureid]&ididentite=[identiteid]&isPop=1';
        var urlCustomer = GetHashKeyCustomerArea(urlCA);

        if (urlCustomer != "") {
            $('.menugestionprofil').attr('href', urlCustomer);
            $('#btnAddProfil').data('href', urlCustomer);
        }
    }



	if (sessionClear)
	{
		localStorage.clear();
		sessionStorage.clear();
	}

    $('.CacheDelete').on("click", function () {
        localStorage.clear();
        sessionStorage.clear();
        //console.log("localStorage et sessionstorage vidé");
    });

    if (errorCode != 0) {
        if (errorCode == 1) {
            /* swal({
			title: "Session web perdue!",
			text: "Veuillez vous reconnecter",
			type: "error",
			confirmButtonText: "Ok"
		});*/
		tempAlert(errorMessage)

            //window.location.href = "https://test.themisweb.fr/aboV2/Home/347/fr";

            if (localStorage.getObj("firstPage") != null) {
                window.location.href = localStorage.getObj("firstPage");
            } else {

            }
            localStorage.clear();
            sessionStorage.clear();
        }
        else {
            alert(errorMessage)
        }
    }
    if (firstPage != "") {
        localStorage.setObj("firstPage", firstPage);
    }
    if (sessionStorage.getObj("myIdentity") == null) {
        CheckConnectCustomerArea(structureIdCurrent);
    }

    /*     if (identiteIdCurrent==0)
         {
           $("#btnConnection").click();
         }
         */

    $('#lnklogout').on('click', function () {
        DisconnectCustomerArea();

       });

    // ******** appel help_tour (plugin aide visuelle)
    /*tour.init();

	$('.startTour').click(function(){
		tour.restart();
		// Start the tour
		// tour.start();
	});
	*/
    DeflagAllSeats();
});



function ShowMiniBasket()
{
	
	var arrBasketHA = localStorage.getObj('arrPanierHA')
	var objEventChoice =	sessionStorage.getObj("objEventsChoice");
	
	if((objEventChoice != null && objEventChoice.length > 0) || (arrBasketHA != null && arrBasketHA.listEventsHA.length > 0))
	{
		var nbPlaceTotal=0 , sumTotal = 0;
		var htmlrowcollapse ='<div id="accordion-card" class="px-4 py-3" >';

        $.each(objEventChoice, function (indx, item) {
            var thisformuleId = item.formulaid;
            $.each(item.listTarifs, function (idx, itemtarif) {
                var thistarifId = itemtarif.tarifid
                //on se positionne sur ce ou ces card(s) ==> objets html
                //$('.card[data-formuleid="'+thisformuleId+'"][data-tarifid="'+thistarifId+'"]')

                //parcours chaque abonné
                $.each(itemtarif.listAbos, function (idxabo, itemabo) {
                    var nbtotalOfThisAbo = 0;
                    nbPlaceTotal += itemabo.listEvents.length;

                    $.grep(itemabo.listEvents, function (x) {
                        return nbtotalOfThisAbo += x.unitTTCamount;
                    });

                    /*** creation mini panier ****/
                    htmlrowcollapse += '<div class="row" data-toggle="collapse" data-target="#collapsecard' + itemabo.idx + '" aria-expanded="true" aria-controls="collapsecard' + itemabo.idx + '">';

                    //si c'est un panier récupérer il faut ajouter +1 à l'index
                    if (sessionStorage.getObj('previousBasket') != null) {
                        htmlrowcollapse += ' <div class="abotitle col">' + ReadXmlTranslate("lbl_abonne_number").replace('[NUMBER]', parseInt(itemabo.idx + 1)) + '</div>';
                    } else {
                        htmlrowcollapse += ' <div class="abotitle col">' + ReadXmlTranslate("lbl_abonne_number").replace('[NUMBER]', parseInt(itemabo.idx)) + '</div>';
                    }

                    // htmlrowcollapse += ' <div class="abotitle col">'+ReadXmlTranslate("lbl_abonne_number").replace('[NUMBER]',parseInt(itemabo.idx)) +'</div>';
                    htmlrowcollapse += '<div class="aboprice col-auto">' + (parseInt(nbtotalOfThisAbo) / 100).toFixed(2) + ' €</div>';
                    htmlrowcollapse += ' </div>';


                    htmlrowcollapse += '<div id="collapsecard' + itemabo.idx + '" class="collapse show" aria-labelledby="headingOne" data-parent="#accordion-card">';
                    //<!-- abo 1 -->
                    var idx = itemabo.idx;
/*
                    //si un panier a été récupérer idx recommence a 0 donc on le force à 1
                    if (sessionStorage.getObj('previousBasket') != null) {
                        idx = idx + 1;
                    }
*/
                    $.each(itemabo.listEvents, function (idxevt, itemevt) {
                        htmlrowcollapse += '<div class="row">';

                        if (sessionStorage.getObj('previousBasket') != null) {
                            //si on a un panier l'objet sur la manifestion change il y  a un e minuscule dans eventName
                            htmlrowcollapse += '<div class="abomanif col"><strong>' + itemevt.eventName + '</strong>' + ReadXmlTranslate("lbl_abo_event").replace('[SESSION_DESCRIPTION]', itemevt.sessionDescription).replace('[CATEGORIE_NAME]', '') + '</div>';
                            htmlrowcollapse += '<div class="aboprice col-auto">' + (parseInt(itemevt.unitTTCamount) / 100).toFixed(2) + ' €</div>';

                        } else {
                            htmlrowcollapse += '<div class="abomanif col"><strong>' + itemevt.EventName + '</strong>' + ReadXmlTranslate("lbl_abo_event").replace('[SESSION_DESCRIPTION]', itemevt.sessionDescription).replace('[CATEGORIE_NAME]', itemevt.categName) + '</div>';
                            htmlrowcollapse += '<div class="aboprice col-auto">' + (parseInt(itemevt.unitTTCamount) / 100).toFixed(2) + ' €</div>';

                        }
                        htmlrowcollapse += '</div>';
                    });

                    sumTotal += nbtotalOfThisAbo;
                    htmlrowcollapse += ' <div class="dropdown-divider"></div></div>';
                });
            });
        });


		//<!-- total -->
		
		if(arrBasketHA != null)
		{

            var types = {};
            for (var i = 0; i < arrBasketHA.listEventsHA.length; i++) {
                var typeName = arrBasketHA.listEventsHA[i].type;
                if (!types[typeName]) {
                    types[typeName] = [];
                }
                types[typeName].push(arrBasketHA.listEventsHA[i]);
            }

            $.each(types, function (index, objTypes) {

                htmlrowcollapse += '<div class="row" data-toggle="collapse" data-target="#collapsecardHA' + index + '" aria-expanded="true" aria-controls="collapsecard' + index + '">';
                htmlrowcollapse += ' <div class="abotitle col">' + objTypes[0].typeName + '</div>';
                //htmlrowcollapse += '<div class="aboprice col-auto">'+(parseInt(nbtotalOfThisAbo) / 100).toFixed(2)+' €</div>';
                htmlrowcollapse += ' </div>';
                htmlrowcollapse += '<div id="collapsecardHA' + index + '" class="collapse show" aria-labelledby="headingOne" data-parent="#accordion-card">';
                $.each(objTypes, function (index, objGroup) {

                    var nbtotalOfThisAboHA = 0
                    nbPlaceTotal += objGroup.listTarifs[0].nb;



                    if (sessionStorage.getObj('previousBasket') != null) {
                        $.grep(objGroup.listTarifs, function (x) {
                            return nbtotalOfThisAboHA += x.unitTTCamount;
                        });

                        $.each(objGroup.listTarifs, function (index, objTarif) {

                            nb = objTarif.nb;
                            gpId = objTarif.gpid;
                            amountU = objTarif.unitTTCamount;
                            amountT = parseInt(amountU) * parseInt(nb);
                            //for (var i = 0; i < objTarif.nb; i++) {
                            htmlrowcollapse += '<div class="row">';
                            htmlrowcollapse += '<div class="abomanif col"><strong>' + objGroup.eventName + '</strong>' + ReadXmlTranslate("lbl_tarif_mini_basket").replace('[SESSION_DESCRIPTION]', objGroup.sessionDescription).replace('[FLOOR_NAME]', '').replace('[SECTION_NAME]', '').replace('[CATEGORIE_NAME]', '') + '</div>';
                            htmlrowcollapse += '<div class="aboprice col-auto">' + (parseInt(amountT) / 100).toFixed(2) + ' €</div>';
                            htmlrowcollapse += '</div>';

                        });

                        sumTotal += nbtotalOfThisAboHA;

                    } else {
                        $.grep(objGroup.listTarifs, function (x) {
                            return nbtotalOfThisAboHA += x.unitTTCAmount;
                        });


                        $.each(objGroup.listTarifs, function (index, objTarif) {

                            nb = objTarif.nb;
                            gpId = objTarif.gpid;
                            amountU = objTarif.unitTTCAmount;
                            amountT = parseInt(amountU) * parseInt(nb);
                            //for (var i = 0; i < objTarif.nb; i++) {
                            htmlrowcollapse += '<div class="row">';
                            htmlrowcollapse += '<div class="abomanif col"><strong>' + objGroup.eventName + '</strong>' + ReadXmlTranslate("lbl_tarif_mini_basket").replace('[SESSION_DESCRIPTION]', objGroup.sessionDescription).replace('[FLOOR_NAME]', objGroup.floorName).replace('[SECTION_NAME]', objGroup.sectionName).replace('[CATEGORIE_NAME]', objGroup.categoryName) + '</div>';
                            htmlrowcollapse += '<div class="aboprice col-auto">' + (parseInt(amountT) / 100).toFixed(2) + ' €</div>';
                            htmlrowcollapse += '</div>';

                            //}
                            /*$.each(objTarif.nb, function(index, nbtarif) {
                            
                            });*/
                        });

                        sumTotal += nbtotalOfThisAboHA;
                    }

                });
                htmlrowcollapse += '</div>';

            });


            htmlrowcollapse += '</div>';

        }

		
		htmlrowcollapse += '</div>'; //fin accordion-card
	
		
		htmlrowcollapse += '<div class="dropdown-divider"></div>';
		htmlrowcollapse += '<div class="row px-4 py-1" id="totalcart">';
		htmlrowcollapse += '  <div class="col">';
        htmlrowcollapse += '<strong>'+ReadXmlTranslate("lbl_total")+'</strong> - <small>'+ReadXmlTranslate("lbl_nb_seats").replace('[NB_SEATS]', nbPlaceTotal) +'</small>';

        htmlrowcollapse += '</div>';
        htmlrowcollapse += ' <div class="aboprice col-auto">' + (parseInt(sumTotal) / 100).toFixed(2) + ' €</div>';
        htmlrowcollapse += ' </div>';
        htmlrowcollapse += '</div>';

		$('#mainmenu #divcartdetail').html(htmlrowcollapse);
	}
	else{
        var htmlrowcollapse ='<div  id="cartempty">'+ReadXmlTranslate("lbl_empty_basket")+' </div>';
		$('#mainmenu #divcartdetail').html(htmlrowcollapse)
	}
}

//Réinsert les informations sélectionné au refresh de la page 

function GetEventsChoice()
{
	console.log('start GetEventChoice');
	var objEventChoice =	sessionStorage.getObj("objEventsChoice");
	
	if(objEventChoice != null && objEventChoice.length > 0)
	{
		var nbPlaceTotal=0 , sumTotal = 0;
		var htmlrowcollapse ='<div id="accordion-card" class="px-4 py-3" >';

		$.each(objEventChoice, function(indx, item) {
			var thisformuleId = item.formulaid;
			$.each(item.listTarifs, function(idx, itemtarif){
				var thistarifId = itemtarif.tarifid		
				//on se positionne sur ce ou ces card(s) ==> objets html
				//$('.card[data-formuleid="'+thisformuleId+'"][data-tarifid="'+thistarifId+'"]')
				
				//parcours chaque abonné
				$.each(itemtarif.listAbos, function(idxabo, itemabo){
					var nbtotalOfThisAbo = 0;  
					nbPlaceTotal += itemabo.listEvents.length;
					
					$.grep(itemabo.listEvents, function(x) {         
						return  nbtotalOfThisAbo+=x.unitTTCamount;
					});

                    //<!-- abo 1 -->
                    var idx = itemabo.idx;
                    //si un panier a été récupérer idx recommence a 0 donc on le force à 1
                    if (sessionStorage.getObj('previousBasket') != null) {
                        idx = idx + 1;
                    }

                    $.each(itemabo.listEvents, function (idxevt, itemevt) {

                        var thisconstraintId = itemevt.constraintId;
                        var thisForm = $('.card[data-formuleid="' + thisformuleId + '"][data-tarifid="' + thistarifId + '"]').find('#abo-' + thisformuleId + '-' + thistarifId + '-colappse' + parseInt(idx)).find('form[name="cat' + thisconstraintId + '"]')

                        if (thisForm.length > 0) {
                            if (!$(thisForm).find('select[name="abospectacles"] option[value="' + itemevt.eventid + '"]').hasAttr('disabled')) {
                                $(thisForm).find('select[name="abospectacles"]').val(itemevt.eventid)
                                $(thisForm).find('select[name="abospectacles"]').trigger('change');

                                $(thisForm).find('select[name="abodate_' + thisformuleId + '_' + thistarifId + '"]').val(itemevt.sessionid);
                                $(thisForm).find('select[name="abodate_' + thisformuleId + '_' + thistarifId + '"]').trigger('change');

                                $(thisForm).find('select[name="abocategorie_' + thisformuleId + '_' + thistarifId + '"]').val(itemevt.categId)
                                $(thisForm).find('select[name="abocategorie_' + thisformuleId + '_' + thistarifId + '"]').trigger('change');

                                //var thisselect = $(constructAddSpectable(form)).appendTo(form.closest(".tab-pane").find('form[name='+formName+'] .aboItems'));
                                //var thisselect = $(constructAddSpectable(form)).appendTo(form.find('.aboItems'));



                                $(constructAddSpectable(thisForm, itemevt.unitTTCamount)).appendTo($(thisForm).find('.aboItems'));


                                setCompleteAbos(thisForm);
                                ShowBadgesAbo(thisForm)
                                //addSpectacleForOne($(thisForm));
                            }
                        }
                    });

                    sumTotal += nbtotalOfThisAbo;

                });
            });
        });

    }
    else {
        var htmlrowcollapse = '<div  id="cartempty">' + ReadXmlTranslate("lbl_empty_basket") + ' </div>';
        $('#mainmenu #divcartdetail').html(htmlrowcollapse)
    }

    //réattribut les mêmes objets que le sessionStorage si il n'est pas null
    if (objEventChoice != null)
        objChoices = sessionStorage.getObj("objEventsChoice");

}



//force l'état du  panier à 'I' pour invalider
function InvalidBasket() {

    var previousBasket = sessionStorage.getObj('previousBasket');
    if (previousBasket != null) {
        $.ajax({
            type: "POST",
            url: urlajaxabov2 + '/Summary/InvalidBasket',
            async: true,
            data: {
                structureId: structureIdCurrent,
                identiteId: identiteIdCurrent,
                userId: userIdCurrent
            },
            success: function (retour) {
                // alert(retour);
                if (retour)
                    console.log('retour : ' + retour);

			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				alert(textStatus);
			},
			 complete: function(){
                localStorage.removeItem('arrPanier');
                localStorage.removeItem('end_time_buy');
                localStorage.removeItem('end_time_select');
                localStorage.removeItem('isTimerBuy');
                localStorage.removeItem('start_time_buy');
                localStorage.removeItem('start_time_select');
            }
        });

    }



}



function DeflagAllSeats() {


    //Déflag les places prises dans le plan et pas validées
    if (sessionStorage.getObj("arrOfSeatFlaggedTempo") != null) {
    	var lstCustomSessionAndSeat = sessionStorage.getObj("arrOfSeatFlaggedTempo")

        if (lstCustomSessionAndSeat.length > 0) {
            $.ajax({
                type: "POST",
                url: urlajaxabov2 + '/SeatsPlan/UnFlagListSeats',
                data: {

                    structureId: structureIdCurrent,
                    identiteId: identiteIdCurrent,
                    lstCustomSessionAndSeat: lstCustomSessionAndSeat
                },
                success: function (retour) {
                    // alert(retour);
                    if (retour == "OK")
                    	sessionStorage.setObj("arrOfSeatFlaggedTempo", null);
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                	alert(textStatus);
                }
            });

    	}


    }

}
function DisconnectCustomerArea(){

    $.ajax({
        type: "POST",
        url: domaineCustomerArea + '/customer/commons.asmx/DisconnectUser',
        //url: domaineCustomerArea +'/'+numToNDigitStr(structureIdCurrent, 4)+'/commons.asmx/DisconnectUser',

        crossDomain: true,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        xhrFields: {
            withCredentials: true
        },
        success: function (response) {
            //alert(response.d);			
            identiteIdCurrent = 0;

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
        },
        complete: function () {
            sessionStorage.removeItem('myIdentity');
        }
    });

}
/*
//pas encore utilisée
function WaitDialog(msg, title, time) {
    if (time == undefined || time == 0 || time == -1)
        time = 2000;


    waitingDialog.show(msg);
    setTimeout(function () {
        waitingDialog.hide();
    }, time);
}*/



$(document).ajaxStart(function () {
	$('#loadingcircle').stop( true, true ).removeClass('d-none').fadeTo( "fast", 1.0);
	$('#maincontent').stop( true, true ).fadeTo( "slow" , 0.5).addClass('d-none');
}).ajaxStop(function () {
	$('#loadingcircle').fadeTo( "fast" , 0.0).addClass('d-none');;
	$('#maincontent').removeClass('d-none').fadeTo( "slow",1.0);
});



//permet de mettre en sessionstorage des objets
Storage.prototype.object = function (key, val) {
	if (typeof val === "undefined") {
		var value = this.getItem(key);
		return value ? JSON.parse(value) : null;
	} else {
		this.setItem(key, JSON.stringify(val));
	}
}


Storage.prototype.setObj = function (key, obj) {
	return this.setItem(key, JSON.stringify(obj))
};

Storage.prototype.getObj = function (key) {
	return JSON.parse(this.getItem(key))
};



function connect() {
    $.ajax({
        type: "POST",
        url: urlajaxabov2 + '/Identity/connectUser',
        asynch: false,
        'data': {
            structureId: structureIdCurrent,
            identiteId: identiteIdCurrent,
            hash: hashCurrent
        },
        success: function (retour) {
            //retour.formulaId = val;
            if (retour != "") {
            	sessionStorage.setObj("myIdentity", retour);
            	//location.reload();
            }
            //arrOfConstraintesOfFormula.push(retour);

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
        	alert(textStatus);
        },
        complete: function () {


        	var currentofthisbasket = GetCurrentBasket(structureIdCurrent);
        	if(currentofthisbasket.panier.BasketId == 0){
        		location.reload();
        	} else {

                var modalText = ReadXmlTranslate("msg_alert_recover_existing_basket");
                ShowConfirmModal(ReadXmlTranslate("msg_title_recover_existing_basket"), modalText, ReadXmlTranslate("msg_response_recover_existing_basket_yes"), ReadXmlTranslate("msg_response_recover_existing_basket_no"), "btnGetBasket", "btnClearAllBasket");

        }
    });
}

function BindButtonConfirmBasket() {

    $('#btnGetBasket').on('click', function (e) {
        e.preventDefault();

        var previousBasket = sessionStorage.getObj("previousBasket");
        sessionStorage.setObj('arrChoixTarifs', previousBasket.customBasket.listFormulas);
        sessionStorage.setObj("objEventsChoice", previousBasket.customBasket.listFormulas);

        var listEventsHA = { "listEventsHA": previousBasket.customBasket.listEventsHA }
        //localStorage.setObj('arrPanierHA', listEventsHA)


        //  arrOfEventsHAnonFormule = listEventsHA
        // ShowSeatsChoicesHA();




        var arrHorsAboNonFormule = [];
        var arrHorsAboAvecFormule = [];
        var arrFormule = [];




        //  $.each(listEventsHA, function (i, oevent) {
        $.each(previousBasket.customBasket.listFormulas, function (indx, itemf) {
            $.each(itemf.listTarifs, function (indx, itemt) {
                $.each(itemt.listAbos, function (indx, itema) {

                    // var arr3 = Enumerable.From(itema.listEvents).Where(function (x) { return x.EventId == oevent.eventid }).ToArray();
                    arrFormule = itema.listEvents;
                    /*  var arr3 = Enumerable.From(itema.listEvents).Where(function (x) { return x.EventId == $.grep(oevent, function (i) { return i.eventid == x.eventid }) }).ToArray();
                      if (arr3.length == 0) {
                          //PS : Places supplémentaire
                          oevent.type = "PS";
                            arrHorsAboAvecFormule.push(oevent);
                          console.log("PS");
                      }
                      else {
                          oevent.type = "HA";
                            arrHorsAboNonFormule.push(oevent);


                          console.log("HA");
                      }
                      */

                    /*   $.each(itema.listEvents, function (indx, iteme) {


                           var arr = Enumerable.From(listEventsHA).Where(function (x) { return x.EventId == iteme.eventid }).ToArray();

                           var arr2 = Enumerable.From(previousBasket.customBasket.listFormulas).Where(function (x) { return x.EventId == iteme.eventid }).ToArray();

                           if (arr.length == 0) {
                               //PS : Places supplémentaire
                               oevent.type = "PS";
                               arrHorsAboAvecFormule.push(oevent);
                           }
                           else {
                               oevent.type = "HA";
                               arrHorsAboNonFormule.push(oevent);
                           }

                       });
                       */



                });


            });



        });



        //});


        $.each(previousBasket.customBasket.listEventsHA, function (i, oevent) {
            //var arr = Enumerable.From(arrFormule).Where(function (x) { return x.eventId == oevent.eventid }).ToArray();
            var arr = $.grep(arrFormule, function (y) { return y.eventid == oevent.eventid })

            if (arr.length == 0) {
                //PS : Places supplémentaire
                oevent.type = "PS";
                oevent.typeName = "Spectacles hors abonnement";
                arrHorsAboAvecFormule.push(oevent);
            }
            else {
                oevent.type = "HA";
                oevent.typeName = "Spectacles supplémentaires";
                arrHorsAboNonFormule.push(oevent);
            }
        });


        localStorage.setObj('arrPanierHA', listEventsHA)


        arrChoix = sessionStorage.getObj("arrChoixTarifs");
        arrChoixTarifsDejaExistant = sessionStorage.getObj("arrChoixTarifs");
        setInput();

        ShowMiniBasket();
	});
	
	$('#btnClearAllBasket').on('click', function(e) {
		e.preventDefault();
		InvalidBasket();
		localStorage.removeItem('arrPanier');
		localStorage.removeItem('end_time_buy');
		localStorage.removeItem('end_time_select');
		localStorage.removeItem('isTimerBuy');
		localStorage.removeItem('start_time_buy');
		localStorage.removeItem('start_time_select');
		
		sessionStorage.removeItem('arrChoixTarifs');
		sessionStorage.removeItem('objEventsChoice');
	});
	
}




function GetCurrentBasket(structureId)
{
	var dataObject = JSON.stringify({
		'structureId': structureId
	});

	var thisbasket = null;

	$.ajax({
		type: "POST",
		url: urlajaxabov2+'/Events/GetCurrentBasket',
		async: false,
		data: {
			structureId: structureIdCurrent
		}, 
		success: function (retour) {
			if($.isPlainObject(retour)) {
                thisbasket = retour;
            }
            else if(retour.contains('danger')) {
                InvalidBasket();
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        }
    });


	localStorage.setItem('panierRecharger', false);

    return thisbasket;
}

/*
function ShowConfirmGetBasket(currentbasket, isHomePage) {

	if (currentbasket.panier.BasketId > 0) {
		
		var modalText = "Un panier à été récemment créé, voulez-vous le récupérer ?"
		ShowConfirmModal("Panier existant !", modalText,  "Oui, récupérer mon panier", "Non, effacé mon panier", "btnGetBasket", "btnClearAllBasket");
	
	
		  //modal demandant si on veut réscupérer le panier
		// $(' <span class="col-auto btngetbasket" data-toggle="modal" data-target="#modalGeneric" data-title="Récupérer le panier" data-type="getbasket" ></span>')

}


//déselectionne visuelement et deflag les places
function CancelAndDeflagsSeats(arrFormules) {

    var arrBasket = localStorage.getObj("arrPanier");

    //console.log('Yes');
    var formulacurrent = 0;
    var idxcurrent = 0;


    var arrCustomSession = [];

    $.each(arrBasket.listFormulas, function (indx, itemBasket) {
    	formulacurrent = itemBasket.formulaid;
        $.each(itemBasket.listTarifs, function (i, objTarif) { // each tarif

            $.each(objTarif.listAbos, function (i, objAbos) { // each tarif
            	idxcurrent = objAbos.idx;

                $.each(objAbos.listEvents, function (i, objEvent) { // each tarif

                    var customSession = {};
                    customSession.eventid = objEvent.eventid;
                    customSession.sessionid = objEvent.sessionid;

                	var customFlaggedSeat = [];

                    $.each(objEvent.listSeats, function (i, objSeat) { //each sieges
                        var listSeats = {};
                        listSeats.seatid = objSeat.seatid;
                        listSeats.hashKey = objSeat.hashKey;
                        customFlaggedSeat.push(listSeats);

                        customSession.listSeats = customFlaggedSeat;

						var eventId = objEvent.eventid
                    	var sessionId = objEvent.sessionid
                    	var seatId = objSeat.seatid
                    	var hk = objSeat.hashKey;
                        // $('#EventsOfFormulasTable_' + formulacurrent + ' td[data-formulaid="' + formulacurrent + '"][data-eventid="' + objEvent.eventid + '"][data-aboidx="' + idxcurrent + '"] input').iCheck('check');

                    });


                    arrCustomSession.push(customSession);


                }); //fi

            }); //fin each tarif
        }); //fin each objAbos
    });



	/*
    var sdata = JSON.stringify({ 'structureId': structureIdCurrent, 'identiteId': identiteIdCurrent }); //, 'lstSessionsToUnFlagged': arrCustomSession });


    $.ajax({
    	type: "POST",
    	dataType: "json",
    	url: urlajaxabov2 + '/seatsPlan/UnFlagAndUnFlagTemp',
    	data: {
    		structureId: structureIdCurrent,
    		identiteId: identiteIdCurrent,
    		lstSessionsToUnFlagged: arrCustomSession
    	},

        // data: sdata,
        success: function (retour) {
        	console.log("success:" + retour);

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
        	alert(textStatus);
        },
        complete: function () {
        	LoadEvents(arrFormules);
        }

    });
    //localStorage.removeItem('arrPanier');
    // sessionStorage.removeItem('arrPanierHA');
}
window.addEventListener("message", receiveMessage, false);




function receiveMessage(event) {
    if (event.origin !== domaineCustomerArea) {
        //on regarde si on a pas un data height
        if ($.isPlainObject(event.data)) {
            var result = event.data;
            $('iframe#' + event.data.id).attr('height', event.data.heightpage + "px");
        }
        return;
    }
    else {

        //console.log(event.data);
        resultc = event.data;

        if ($.isPlainObject(event.data)) {
            // $('iframe#iframeCustomer').height(resultc.split(":")[1] +"px");
            $('iframe#' + event.data.id).attr('height', event.data.heightpage + "px");
        } else {
            if (resultc.split(":")[0] == "Closeme") {
                $.fancybox.close();
            }

            if (resultc.split(":")[0] == "Update") {
                //$('#ctl00_authendiv').html('<img alt="..." src="App_Themes/AppTheme/Spinning_wheel_throbber.gif" />'); 
                if (resultc.split(":")[1] == "true") {
                    var idi = resultc.split(":")[2];
                    var txtIsIdentif = $('ctl00_ContentPlaceHolder_txtIsIdentif');
                    if (txtIsIdentif != null) {
                        txtIsIdentif.value = "true";
                    }
                    var pgmeth = PageMethods.ConnectUser(resultc.split(":")[2], resultc.split(":")[3], GetHtmlAuthentifdivMethodSuccess, MethodeJS_Error);
                }
                else {   // not authentif
                    var txtIsIdentif = $('ctl00_ContentPlaceHolder_txtIsIdentif');
                    if (txtIsIdentif != null) {
                        txtIsIdentif.value = "false";
                    }
                }
            }
            if (resultc.split(":")[0] == "Connect") {
                if (resultc.split(":")[1] == "true") {

					/*

					identiteIdCurrent=resultc.split(":")[2];
					hashCurrent=resultc.split(":")[3];*/

					localStorage.setItem("identitecurrent", resultc.split(":")[2]);
					localStorage.setItem("hashcurrent", resultc.split(":")[3]);


					identiteIdCurrent = resultc.split(":")[2];
					hashCurrent = resultc.split(":")[3];

					//identiteIdCurrent = 60169;
					//hashCurrent = "215A7E77879316E880E5B23F40093A2DCF60AD8F";
					connect();
					// $('#ctl00_authendiv').html('<img alt="..." src="App_Themes/AppTheme/Spinning_wheel_throbber.gif" />');
					// var pgmeth = PageMethods.ConnectUser(resultc.split(":")[2], resultc.split(":")[3], GetHtmlAuthentifdivMethodSuccess, MethodeJS_Error);
				}

            }

            if (resultc.split(":")[0] == "Create") {
                if (resultc.split(":")[1] == "true") {

                    localStorage.setItem("identitecurrent", resultc.split(":")[2]);
                    localStorage.setItem("hashcurrent", resultc.split(":")[3]);


					identiteIdCurrent = resultc.split(":")[2];
					hashCurrent = resultc.split(":")[3];
					connect();
					//var pgmeth = PageMethods.ConnectUser(resultc.split(":")[2], resultc.split(":")[3], GetHtmlAuthentifdivMethodSuccess, MethodeJS_Error);
				}

            }

        }

    }
}





// Removal of all expired items
if (window.localStorage) {

    // two mins - (1000 * 60 * 20) would be 20 mins
    var expiryTime = new Date().getTime() - (1000 * 60 * 2);

    var deleteRows = [];
    for (var i = 0; i < localStorage.length; i++) {
    	var key = localStorage.key(i);
    	var partsArray = key.split('-');
        // The last value will be a timestamp
        var lastRow = partsArray[partsArray.length - 1];

        if (lastRow && parseInt(lastRow) < expiryTime) {
        	deleteRows.push(key);
        }
    }
    // delete old data
    for (var j = 0; j < deleteRows.length; j++) {
    	localStorage.removeItem(deleteRows[j]);
    }
}



function CheckConnectCustomerArea(structureId) {


    $.ajax({
        type: "POST",
        url: domaineCustomerArea + '/customer/commons.asmx/IsConnected',
        // url: domaineCustomerArea + '/commons.asmx/IsConnected',
        crossDomain: true,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        data: '{"idStructure":' + numToNDigitStr(structureId, 4) + '}',

        xhrFields: {
            withCredentials: true
        },
        success: function (response) {

       	resultc = response.d;

       	if (resultc.split(":")[0] == "true") {

       		identiteIdCurrent = resultc.split(":")[1];
       		hashCurrent = resultc.split(":")[2];

       		connect();
                //var pgmeth = PageMethods.ConnectUser(resultc.split(":")[1], resultc.split(":")[2], GetHtmlAuthentifdivMethodSuccess, MethodeJS_Error);
            }else {

                // $('#btnConnection').trigger('click');
            }
            //alert(response.firstChild.textContent);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
        },
        complete: function () {

        }
    });
}

//permet de formater un integer sur autant de chiffres que l'on souhaite
//utiliser pour les structureid 0175
function numToNDigitStr(num, n) {
	if (num >= Math.pow(10, n - 1)) { return num; }
	return "0" + numToNDigitStr(num, n - 1);
}

function DeleteFlaggedSeatTempo() {
    $.each($('circle'), function (indx, item) {
        if ($(item).hasClass('flaggedseat_tempo')) {
            $(item).removeClass('flaggedseat_tempo');
        }
    });
}

$('#modalGeneric, #modalGenericSansRefresh').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget) // Button that triggered the modal
    var modalType = button.data('type') // Extract info from data-type attribute 
    var modalTitle = button.data('title') // Extract info from data-title attribute
    var modalHref = (button.attr('href') != "") ? button.attr('href') : button.data('href') // Extract info from data-href attribute
    
    var modalTypeHorsAbo = button.data('typehorsabo');
	//var modalTypeHorsAbo = (button.data('modalTypeHorsAbo') == undefined) ? : TypeHorsAbo ;
	var modalEventName = button.data('eventname') // Extract info from data-eventname attribute
    var modalCategId = button.data('categid') // Extract info from data-categid attribute
    var modalCollapseId = button.data('collapseid') // Extract info from data-collapseid attribute
    var modalEventId = button.data('eventid') // Extract info from data-eventid attribute
    var modalSession = button.data('session') // Extract info from data-session attribute
    var modalSessionId = button.data('sessionid') // Extract info from data-sessionid attribute
    
    switch(modalType){

    	case "placesupp": 


    	var $modal = $(this)

    	var modal = '<div class="modal-header">';
    	modal += '<h5 class="modal-title">'+modalTitle+'</h5>';
    	modal += '<button type="button" class="close" data-dismiss="modal" aria-label="Close">';
    	modal += '<span aria-hidden="true">&times;</span>';
    	modal += '</button>';
    	modal += '</div>';

	//ajoute le type (hors abo ou place supplémentaire) HA ou PS
	var modalContent = '<div class="modal-body" data-type="'+modalTypeHorsAbo+'" ><iframe width="100%" id="iframeplacesupp" frameborder="0" scrolling="no" allowtransparency="true"></iframe></div>';

            $modal.find('.modal-content').html(modal + '' + modalContent);


            // $modal.attr('data-type', modalTypeHorsAbo);

	 $modal.find('iframe').attr('src', modalHref);

	 break;

	 case "customer": 
	 var $modal = $(this)

	 var modal = '<div class="modal-header">';
	 modal += '<h5 class="modal-title">'+modalTitle+'</h5>';
	 modal += '<button type="button" class="close" data-dismiss="modal" aria-label="Close">';
	 modal += '<span aria-hidden="true">&times;</span>';
	 modal += '</button>';
	 modal += '</div>';
	 var modalContent = '<div class="modal-body" data-modal-refresh="true"><iframe width="100%" frameborder="0" scrolling="no" id="iframecustomer"  allowtransparency="true"></iframe></div>';

	 $modal.find('.modal-content').html(modal+''+modalContent);
	 $modal.find('iframe').attr('src', modalHref);

	 break;

            //quand on supprime une manifestation 
        case "manifdelete":
            var $modal = $(this)

		var modal = '<div class="modal-header">';
		modal += '<h5 class="modal-title">'+modalTitle+'</h5>';
		modal += '<button type="button" class="close" data-dismiss="modal" aria-label="Close">';
		modal += '<span aria-hidden="true">&times;</span>';
		modal += '</button>';
		modal += '</div>';



            var modalContent = '<div class="modal-body">';
            modalContent += ReadXmlTranslate("btn_delete_all_abonnes").replace('[EVENTNAME]', modalEventName).replace('[SESSION]', modalSession);
            modalContent += '</div>';

            modalContent += '<div class="modal-footer">';
            modalContent += '<div class="row">';
            modalContent += '<div class="col-12">';

            modalContent += ' <button type="button" class="col-12 col-md-auto btn btn-secondary" data-dismiss="modal">' + ReadXmlTranslate("lbl_cancel") + '</button>';

            modalContent += ' <button type="button" class="col-12 col-md-auto btn btn-primary" id="deleteAllAbo" data-collapseid="' + modalCollapseId + '" data-categid="' + modalCategId + '" data-eventid="' + modalEventId + '" data-sessionid="' + modalSessionId + '" data-dismiss="modal">' + ReadXmlTranslate("btn_delete_all_abonnes") + '</button>';
            modalContent += ' <button type="button" class="col-12 col-md-auto btn btn-primary" id="deleteThisAbo" data-collapseid="' + modalCollapseId + '" data-categid="' + modalCategId + '" data-eventid="' + modalEventId + '" data-sessionid="' + modalSessionId + '" data-dismiss="modal">' + ReadXmlTranslate("btn_delete_one_abonne") + '</button>';
            modalContent += '</div>';
            modalContent += '</div>';
            modalContent += '</div>';


		$modal.find('.modal-content').html(modal+''+modalContent);






		$('#deleteAllAbo').on('click', function(e) {      
			removeSpectacleForAll($(this).data('collapseid'), $(this).data('eventid'), $(this).data('sessionid'), $(this).data('categid'));
		    unFlag($(this).data('eventid'), $(this).data('sessionid'))
		});

            $('#deleteThisAbo').on('click', function (e) {
                removeSpectacleForOne($(this).data('collapseid'), $(this).data('eventid'), $(this).data('sessionid'), $(this).data('categid'));
                unFlag($(this).data('eventid'), $(this).data('sessionid'))
            });

            break;


        case "mentions":
            var $modal = $(this)

	 var modal = '<div class="modal-header">';
	 modal += '<h5 class="modal-title">'+modalTitle+'</h5>';
	 modal += '<button type="button" class="close" data-dismiss="modal" aria-label="Close">';
	 modal += '<span aria-hidden="true">&times;</span>';
	 modal += '</button>';
	 modal += '</div>';
	 var modalContent = '<div class="modal-body"><iframe width="100%"  style="position: absolute;   top: 0;   left: 0;     height: 100%;" frameborder="0" scrolling="yes" id="iframecustomer"  allowtransparency="true"></iframe></div>';

            $modal.find('.modal-content').html(modal + '' + modalContent);
            $modal.find('.modal-content').css('height', '80vh');
            $modal.find('iframe').attr('src', modalHref);
            break;


        case "conditions":
            var $modal = $(this)

	 var modal = '<div class="modal-header">';
	 modal += '<h5 class="modal-title">'+modalTitle+'</h5>';
	 modal += '<button type="button" class="close" data-dismiss="modal" aria-label="Close">';
	 modal += '<span aria-hidden="true">&times;</span>';
	 modal += '</button>';
	 modal += '</div>';
	 var modalContent = '<div class="modal-body"><iframe width="100%" style="position: absolute;   top: 0;   left: 0;     height: 100%;"frameborder="0" scrolling="yes" id="iframecustomer"  allowtransparency="true"></iframe></div>';

            $modal.find('.modal-content').html(modal + '' + modalContent);
            $modal.find('.modal-content').css('height', '80vh');
            $modal.find('iframe').attr('src', modalHref);
            break;
    }

})




function GetHashKeyCustomerArea(url) {


	var retUrl = "";
    //var url = "https://test.themisweb.fr/customer/linkedconsumerslist.aspx?idstructure=' + numToNDigitStr(structureIdCurrent, 4)+'&ididentite='+identiteIdCurrent+'&isPop=1'";
    //var url = "https://test.themisweb.fr/customer/linkedconsumerslist.aspx?idstructure=' + numToNDigitStr(structureIdCurrent, 4)+'&ididentite='+identiteIdCurrent+'&isPop=1'";
    $.ajax({
    	type: "POST",
    	url: urlajaxabov2 + '/Summary/GetHasKeyCustomerArea',
    	async: false,
    	data: {
    		structureId: structureIdCurrent,
            // identiteId: identiteIdCurrent,
            absoluteUrl: url
        },
        success: function(retour) {
            //retour.formulaId = val;
            retUrl = retour;
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
        	alert(textStatus);
        }
    });
    return retUrl;
}

function startTimer(start_time, end_time) {
    if (localStorage.getItem('isTimerBuy') != null && localStorage.getItem('isTimerBuy') == 'true') {
        timerBuy(start_time, end_time);
    } else {
        if (settings.global.showTimerSelect) {
            timerSelect(start_time, end_time)
        }
    }
}

function timerSelect(start_time, end_time) {
    console.log('timerSelect ' + start_time + ' ' + end_time)
    if (localStorage.getItem('start_time_select') == null || localStorage.getItem('start_time_select') == 'Invalid Date') {

        localStorage.setItem('start_time_select', start_time)
        localStorage.setItem('end_time_select', moment(start_time).add(end_time, 'minutes'));

        console.log('on redefini le start_time_select')
    }
    var startTimeSelect = localStorage.getItem('start_time_select');
    var endTimeSelect = localStorage.getItem('end_time_select')
    console.log('on récupère le start_time_select')

    showTimer(startTimeSelect, endTimeSelect);
}
function timerBuy(start_time, end_time) {
    console.log('timerBuy ' + start_time + ' ' + end_time)
    if (localStorage.getItem('start_time_buy') == null || localStorage.getItem('start_time_buy') == 'Invalid Date') {
        localStorage.setItem('start_time_buy', start_time)
        localStorage.setItem('end_time_buy', moment(start_time).add(end_time, 'minutes'));
        console.log('on redefini le start_time_buy')
    }
    var startTimeBuy = localStorage.getItem('start_time_buy');
    var endTimeBuy = localStorage.getItem('end_time_buy')
    console.log('on récupère le start_time_buy')


	showTimer(startTimeBuy,  endTimeBuy);
}


function showTimer(start, end) {

	$('#actualtimer').html('');
	$('#actualtimer').countdown({
		show_day: false,
		show_hour: false, 
		start_time  : start, 
		end_time: new Date(end),
		progress: $('#progressbartimer'),
		decrement: true,
		onComplete: function() {
			console.log('complete')
			timerFinish();
		},
		wrapper: function(unit) {

			  var wrapper = $('<span class="' + unit.toLowerCase() + '_wrapper" />');
            wrapper.append('<span class="counter" />');

            //evite de mettre : après les secondes
            if(unit.toLowerCase()  == 'minute')
                wrapper.append('<span class="title"> '+ReadXmlTranslate("lbl_minutes")+' </span>');


            return wrapper;
        },
        update_progress: function (progress, element) {
            //  console.log('progress ' + progress + ' element ' + element);


             if(localStorage.getItem('end_time_buy') != null)
            {
                var dEndTimeBuy = new Date(localStorage.getItem('end_time_buy'))
                var msTimerBuy = dEndTimeBuy - new Date();
                //calcul le temps restant du timer d'achat
                var minTimerBuy = Math.floor((msTimerBuy/1000/60) << 0), sec = Math.floor((msTimerBuy/1000) % 60);

                if (minTimerBuy > 0 && minTimerBuy <= settings.global.showMessageMinTimerBuyBeforeEnd && localStorage.getItem('isShowMessageTimerBuy') == null) {
                   //message timer achat
                    ShowConfirmModal(ReadXmlTranslate("msg_confirm_title_buy_timer"), ReadXmlTranslate("msg_confirm_before_buy_timer") + ' ' +minTimerBuy+' ' +ReadXmlTranslate("msg_confirm_after_buy_timer"),ReadXmlTranslate("btn_modal_buy_timer"), "", '', "btnModalBuyTimer", false, false, "static")
                
                    localStorage.setItem('isShowMessageTimerBuy', true);
                }


            }

            if (localStorage.getItem('end_time_buy') == null && localStorage.getItem('end_time_select') != null) {
                var dEndTimerSelect = new Date(localStorage.getItem('end_time_select'))
                var msTimerSelect = dEndTimerSelect - new Date();
                //calcul le temps restant
                var minTimerSelect = Math.floor((msTimerSelect/1000/60) << 0), sec = Math.floor((msTimerSelect/1000) % 60);


                if (minTimerSelect > 0 && minTimerSelect <= settings.global.showMessageMinTimerSelectBeforeEnd && localStorage.getItem('isShowMessageTimerSelect') == null) {
                    //message timer select
                    ShowConfirmModal(ReadXmlTranslate("msg_confirm_title_select_timer"), ReadXmlTranslate("msg_confirm_before_select_timer") + ' ' + minTimerSelect + ' ' + ReadXmlTranslate("msg_confirm_after_select_timer"), ReadXmlTranslate("btn_modal_buy_timer"), "", '', "btnModalSelectTimer", false, false, "static")


                    
                    localStorage.setItem('isShowMessageTimerSelect', true);
                }
            }
            //mets à jour la progressbar
            element.attr('aria-valuenow', progress); //We set a custom attribute, 'area-valuenow' containing the progress
            element.css('width', progress + '%'); //Fill the bar with percentage of progress
        }
    });
    $('#timer').removeClass('d-none');
    getHeaderHeight();
}
function timerFinish() {
    $('#timer').addClass('d-none');
    var modalText = ReadXmlTranslate("msg_basket_timer")

    ShowConfirmModal(ReadXmlTranslate("msg_title_basket_timer_finish"), modalText, ReadXmlTranslate("btn_basket_timer_finish_yes"), "", "btnClearEveryThing", "", false, false, "static");
    var firstPage = localStorage.getItem('firstPage').replace(/\"/g, "");

    InvalidBasket();
    localStorage.clear();
    sessionStorage.clear();
    $('#btnClearEveryThing').on('click', function (e) {
        e.preventDefault();
        window.location.href = firstPage;
    });

    //si il y a un panier en cours dans la DB et que le timer est fini alors on invalide tout
    if (sessionStorage.getObj('previousBasket') != null) {
        var previousBasket = sessionStorage.getObj('previousBasket');

    }
}

function ShowConfirmModal(title, message, textbuttonaccept, textbuttonrefuse, idbuttonaccept, idbuttonrefuse, hasClose, hasKeyboard, hasBackdrop, isRefresh) {
    hasClose = hasClose || true;
    hasKeyboard = hasKeyboard || true;
    hasBackdrop = hasBackdrop || true;
    isRefresh = isRefresh || false;

    var modalContent = '<div class="modal-header"><h5 class="modal-title">' + title + '</h5>';
    if (hasClose) {
        modalContent += '<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>';
    }
    modalContent += '</div>';
    modalContent += '<div class="modal-body" data-modal-refresh="' + isRefresh + '">' + message + '</div>';
    modalContent += '<div class="modal-footer"><div class="row"><div class="col-12">';


    if (textbuttonrefuse != "") {
        modalContent += ' <button type="button" class="col-12 col-md-auto btn btn-secondary" data-dismiss="modal" id="' + idbuttonrefuse + '">' + textbuttonrefuse + '</button>';
    }
    if (textbuttonaccept != "") {
        modalContent += ' <button type="button" class="col-12 col-md-auto btn btn-primary" id="' + idbuttonaccept + '" data-dismiss="modal">' + textbuttonaccept + '</button>';
    }
    modalContent += '</div></div></div>';

	//modalContent += '<div class="modal-footer"><div class="row"><div class="col-12"> <button type="button" class="col-12 col-md-auto btn btn-secondary" data-dismiss="modal" id="'+idbuttonrefuse+'">'+textbuttonrefuse+'</button>  <button type="button" class="col-12 col-md-auto btn btn-primary" id="'+idbuttonaccept+'" data-dismiss="modal">'+textbuttonaccept+'</button></div></div></div>';

	$('#modalGeneric').find('.modal-content').html('')
	$('#modalGeneric').find('.modal-content').html(modalContent);

	$('#modalGeneric').modal({
		show:true,
		keyboard:hasKeyboard,
		backdrop:hasBackdrop
	});
	

}



if (!('contains' in String.prototype)) {
	String.prototype.contains = function (str, startIndex) {
		"use strict";
		return -1 !== String.prototype.indexOf.call(this, str, startIndex);
	};
}


$.fn.hasAttr = function(name) {  
	return this.attr(name) !== undefined;
};


/**
 * detect IE
 * returns version of IE or false, if browser is not Internet Explorer
 */
function detectIE() {
    var ua = window.navigator.userAgent;

    // Test values; Uncomment to check result …

    // IE 10
    // ua = 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Trident/6.0)';

    // IE 11
    // ua = 'Mozilla/5.0 (Windows NT 6.3; Trident/7.0; rv:11.0) like Gecko';

    // Edge 12 (Spartan)
    // ua = 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.71 Safari/537.36 Edge/12.0';

    // Edge 13
    // ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586';

    var msie = ua.indexOf('MSIE ');
    if (msie > 0) {
        // IE 10 or older => return version number
        return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
    }

    var trident = ua.indexOf('Trident/');
    if (trident > 0) {
        // IE 11 => return version number
        var rv = ua.indexOf('rv:');
        return parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
    }

    var edge = ua.indexOf('Edge/');
    if (edge > 0) {
        // Edge (IE 12+) => return version number
        return parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
    }

    // other browser
    return false;
}


function SetDeviseCode(montant) {
    if (montant != "") {

        montant = montant.replace('&nbsp;', '')

        var devise = {
            Code: settings.global.deviseCode,
            Before: settings.global.deviseBefore,
            Separator: settings.global.deviseSeparator
        };
        if (devise != "") {
            if (devise.Before) {
                return devise.Code + " " + formatMoney(montant.replace(',', '.'), 2, devise.Separator, " ");

            } else {
                return formatMoney(montant.replace(',', '.'), 2, devise.Separator, " ") + " " + devise.Code;
            }
        } else {
            return montant;
        }
    }

}
//on verifie si amount est un chiffre (et non un autre element html) alors on le format.
function formatMoney(amount, decimalCount = 2, decimal = ".", thousands = ",") {

    if (!isNaN(parseFloat(amount))) {
        try {
            decimalCount = Math.abs(decimalCount);
            decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

            const negativeSign = amount < 0 ? "-" : "";

            let i = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();
            let j = (i.length > 3) ? i.length % 3 : 0;

            return negativeSign + (j ? i.substr(0, j) + thousands : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousands) + (decimalCount ? decimal + Math.abs(amount - i).toFixed(decimalCount).slice(2) : "");
        } catch (e) {
            console.log(e)
        }
    } else {
        return amount
    }
    /**/

};


function doActionByTypeEnvoi(codeTypeEnvoi) {
    switch (codeTypeEnvoi) {

        case 'ENVDMCL':
            alert('verifier votre adresse de livraison ?');
            break;
        default:
            break;
    }
}
