﻿using aboV2.App_Code;
using aboV2.wcf_Themis;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using ws_bll.WT;
using ws_DTO;

namespace aboV2.Controllers
{
    public class HorsAboController : BaseController
    {
        [Throttle(Name = "Load formulas", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        // GET: HorsAbo
        public ActionResult LoadEvents(int structureId, int identiteId, string langCode, string listformulaId)
        {
            //return View();
            List<ws_DTO.EventEntity> listEventsHorsAbo = LoadEventsHA(structureId, identiteId, langCode, listformulaId);

            return Json(listEventsHorsAbo, JsonRequestBehavior.AllowGet);

        }

        [Throttle(Name = "Load formulas", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        /// <summary>
        /// list des events avec sessions parametrés dans l'offre liée à l'abo (avec gestion de cache)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langCode"></param>
        /// <param name="listformulaId"></param>
        /// <returns></returns>
        private List<ws_DTO.EventEntity> LoadEventsHA(int structureId, int identiteId, string langCode, string listformulaId)
        {
            List<ws_DTO.EventEntity> listEventsHorsAbo = new List<ws_DTO.EventEntity>();
            List<ws_DTO.EventEntity> listEventsHorsAboReturn = new List<ws_DTO.EventEntity>();

            string cacheName = "listEventsHorsAbo" + structureId + "." + identiteId + "_" + langCode + "_" + string.Join(".", listformulaId);

            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {


                myDictionary mySSC = new myDictionary();
                mySSC = mySSC.GetDictionaryFromCache(structureId);

                int offreIdHorsAbo = 0;
                if (mySSC.Contains("ABOV2OFFREIDHORSABO") && mySSC["ABOV2OFFREIDHORSABO"] != null && mySSC["ABOV2OFFREIDHORSABO"] != "")
                {
                    offreIdHorsAbo = int.Parse(mySSC["ABOV2OFFREIDHORSABO"]);
                }

                //si il y a une offre de paramétrée dans le config.ini
                if (offreIdHorsAbo > 0)
                {
                    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                    List<int> listForm = new List<int>();
                    if (listformulaId == "")
                    {
                        listForm.Add(0);
                    }
                    else
                    {
                        foreach (string fid in listformulaId.Split(':'))
                        {
                            listForm.Add(int.Parse(fid));
                        }
                    }
                    ws_DTO.EventEntity[] arr = wcfThemis.LoadEventsHorsAbo(structureId, langCode, identiteId, listForm.ToArray(), offreIdHorsAbo);
                    if (arr != null)
                    {
                        listEventsHorsAbo = arr.ToList();
                    }

                    object _addLock = new object();

                    lock (_addLock)
                    {
                        System.Web.HttpContext.Current.Cache.Insert(cacheName, listEventsHorsAbo, null, DateTime.Now.AddSeconds(60 * 10), System.Web.Caching.Cache.NoSlidingExpiration, System.Web.Caching.CacheItemPriority.Default, null);
                    }
                }
                else
                {
                    listEventsHorsAbo = new List<ws_DTO.EventEntity>();

                }

            }
            else
            {
                //  listEventsHorsAbo = (List<ws_DTO.EventEntity>)System.Web.HttpContext.Current.Cache[cacheName];

                listEventsHorsAbo = ((List<ws_DTO.EventEntity>)System.Web.HttpContext.Current.Cache.Get(cacheName)).ToList();
            }



            listEventsHorsAboReturn = new List<ws_DTO.EventEntity>(listEventsHorsAbo);
            //foreach(ws_DTO.EventEntity evt in listEventsHorsAbo)
            //{
            //    listEventsHorsAboReturn.Add(evt); //////////////// ???????????????????????????? la liste est modifiée dans le cache
            //}
            //listEventsHorsAboReturn =  listEventsHorsAbo.ToList()
            //listEventsHorsAboReturn.AddRange()

            //listEventsHorsAbo.Select(item => (ws_DTO.EventEntity)item.Clone()).ToList();

            return listEventsHorsAboReturn.ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langCode"></param>
        /// <param name="listformulaId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="zoneId"></param>
        /// <param name="floorId"></param>
        /// <param name="sectionId"></param>
        /// <param name="categId"></param>
        /// <param name="typeGrilleTarif">HA: Hors abo, PS : places supplémentaires</param>
        /// <returns></returns>
        [Throttle(Name = "Load formulas", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 1000)]
        public ActionResult LoadGT(int structureId, int identiteId, string langCode, string listformulaId, int eventId, int sessionId, int zoneId, int floorId, int sectionId, int categId, string typeGrilleTarif)
        {
            myDictionary mySSC = new myDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureId);

            int offreIdHorsAbo = 0;
            if (mySSC.Contains("ABOV2OFFREIDHORSABO") && mySSC["ABOV2OFFREIDHORSABO"] != null && mySSC["ABOV2OFFREIDHORSABO"] != "")
            {
                offreIdHorsAbo = int.Parse(mySSC["ABOV2OFFREIDHORSABO"]);
            }


            //return View();
            wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
            List<int> listForm = new List<int>();
            if (listformulaId != "")
            {
                foreach (string fid in listformulaId.Split(':'))
                {
                    listForm.Add(int.Parse(fid));
                }
            }
            List<ws_DTO.GestionPlaceEntity> listGpGrilleTarif = new List<ws_DTO.GestionPlaceEntity>();
            if (typeGrilleTarif == "HA")
            {
                listGpGrilleTarif = wcfThemis.LoadGrilleTarifsHorsAbo(structureId, langCode, identiteId, 0, eventId, sessionId, new List<int>() { zoneId }.ToArray(), new List<int>() { floorId }.ToArray(), new List<int>() { sectionId }.ToArray(), new List<int>() { categId }.ToArray(), null, listForm.ToArray(), offreIdHorsAbo).ToList();
            }
            else
            {
                //récupère le paramètrage du fichier JSON 

                int lieuId = wcfThemis.GetLieuBySessionId(structureId, langCode, sessionId);

                dynamic settingsOfZapperZES = GetSettingsOfZapperZES(structureId, eventId, 0, langCode);

                var thisSettingsOfZapperZES = settingsOfZapperZES["default"];
                if(settingsOfZapperZES[lieuId.ToString()] != null)
                {
                    thisSettingsOfZapperZES = settingsOfZapperZES[lieuId.ToString()];

                }


                ws_DTO.EventEntity evt = new ws_DTO.EventEntity() { EventId = eventId };
                ws_DTO.SessionEntity sess = new ws_DTO.SessionEntity() { SessionId = sessionId };
                ws_DTO.LieuEntity lieu = new ws_DTO.LieuEntity() { LieuId = lieuId };

               SettingsLieuEntity settingsLieu = new SettingsLieuEntity(){
                    Event = evt,
                    Lieu = lieu,
                    Session = sess,
                    ZapperZone = (bool)thisSettingsOfZapperZES.zapperZone.Value,
                    ZappeFloor = (bool)thisSettingsOfZapperZES.zapperEtage.Value,
                    ZapperSection = (bool)thisSettingsOfZapperZES.zapperSection.Value
                };

                listGpGrilleTarif = wcfThemis.LoadGrilleTarifsPlacesSupplementaires(structureId, langCode, identiteId, 0, eventId, sessionId, new List<int>() { zoneId }.ToArray(), new List<int>() { floorId }.ToArray(), new List<int>() { sectionId }.ToArray(), new List<int>() { categId }.ToArray(), null, listForm.ToArray(), offreIdHorsAbo, settingsLieu).ToList();

            }



            if (listGpGrilleTarif.Count == 0)
            {
                //error aucun tarif paramètré

                GestionTraceManager.WriteLogError(structureId, "error:aucun tarif paramètré eventId:" + eventId + " sessionId  " + sessionId);

                string error = "error:msg_neither_price";
                return Json(error, JsonRequestBehavior.AllowGet);
            }

            List<ws_DTO.EventEntity> listEventsHorsAbo = LoadEventsHA(structureId, identiteId, langCode, listformulaId);

            ws_DTO.EventEntity oevt = listEventsHorsAbo.Where(e => e.EventId == eventId).FirstOrDefault();

            // AAAAAAAAAAA a corriger ******************* cloner l'object par valeur, pas par reference !!!!!!!!!!!! ????????

            // oevtWork.ListSessions = new List<ws_DTO.SessionEntity>();

            //if (sessionId != 0)
            //{
            //   // oevt.ListSessions = new List<ws_DTO.SessionEntity>();

            if (oevt != null)
            {
                foreach (var osess in oevt.ListSessions)
                {
                    //        //if (sessionId == 0 || osess.SessionId == sessionId)
                    //        //{
                    List<ws_DTO.GestionPlaceEntity> listGpThisSession = listGpGrilleTarif.Where(gp => gp.SessionId == osess.SessionId).ToList();
                    List<ws_DTO.GestionPlaceEntity> listGpThisSessionFiltree = new List<ws_DTO.GestionPlaceEntity>();

                    foreach (ws_DTO.GestionPlaceEntity gp in listGpThisSession)
                    {
                        if (categId == 0 || gp.CategoryId == categId)
                        {
                            if (sessionId == 0 || gp.SessionId == sessionId)
                            {
                                gp.hashKey = getHash(gp.gestion_place_id + "/" + gp.SessionId, thisSalt);
                                gp.priceEnt.hashKey = getHash(gp.priceEnt.PriceId + "_" + gp.priceEnt.VtsId + "_" + gp.CategoryId + "/" + gp.SessionId, thisSalt);
                                listGpThisSessionFiltree.Add(gp);
                            }
                        }

                        /*
                        var thisGestionPlace = listGpThisSession.Where(s => s.SessionId == osess.SessionId && s.ZoneId == thiszone.ZoneId && s.FloorId == thisfloor.FloorId && s.SectionId == thissection.SectionId)
                                                   .Select(s => s).FirstOrDefault();


                        if (thisGestionPlace != null)
                        {
                            thisGestionPlace.hashKey = getHash(thisGestionPlace.gestion_place_id + "/" + thisGestionPlace.SessionId, thisSalt);
                            thisGestionPlace.priceEnt.hashKey = getHash(thisGestionPlace.priceEnt.PriceId + "_" + thisGestionPlace.priceEnt.VtsId + "_" + thisGestionPlace.CategoryId + "/" + thisGestionPlace.SessionId, thisSalt);
                            listGpThisSessionFiltree.Add(thisGestionPlace);
                        }*/
                    }


                    if (listGpThisSession.Count > 0)
                    {
                        /* if (categId == 0 || gp.CategoryId == categId)
                         {
                             if (sessionId == 0 || gp.SessionId == sessionId)
                             {
                                 gp.hashKey = getHash(gp.gestion_place_id + "/" + gp.SessionId, thisSalt);
                                 gp.priceEnt.hashKey = getHash(gp.priceEnt.PriceId + "_" + gp.priceEnt.VtsId + "_" + gp.CategoryId + "/" + gp.SessionId, thisSalt);
                                 listGpThisSessionFiltree.Add(gp);
                             }
                         }
                         */

                        var lstZones = listGpThisSession.GroupBy(gb => new { gb.ZoneId, gb.ZoneName }).Select(z => new ws_DTO.ZoneEntity()
                        {
                            ZoneId = z.Key.ZoneId,
                            ZoneName = z.Key.ZoneName

                        })
                        .Distinct().ToList();

                        osess.listZones = lstZones;

                        foreach (var thiszone in lstZones)
                        {
                            var lstFloors = listGpThisSession.Where(z => z.ZoneId == thiszone.ZoneId).GroupBy(gb => new { gb.FloorId, gb.FloorName }).Select(f => new ws_DTO.FloorEntity()
                            {
                                FloorId = f.Key.FloorId,
                                FloorName = f.Key.FloorName
                            }).ToList();

                            thiszone.listFloors = lstFloors;

                            foreach (var thisfloor in lstFloors)
                            {
                                var lstSections = listGpThisSession.Where(f => f.ZoneId == thiszone.ZoneId && f.FloorId == thisfloor.FloorId).GroupBy(gb => new { gb.SectionId, gb.SectionName }).Select(f => new ws_DTO.SectionEntity()
                                {
                                    SectionId = f.Key.SectionId,
                                    SectionName = f.Key.SectionName
                                }).ToList();

                                thisfloor.listSections = lstSections;

                                foreach (var thissection in lstSections)
                                {

                                    var listCoupleTarifCategs = listGpThisSession.Where(s => s.ZoneId == thiszone.ZoneId && s.FloorId == thisfloor.FloorId && s.SectionId == thissection.SectionId)
                                                        .GroupBy(gb => new
                                                        {
                                                            gb.CategoryId,
                                                            gb.CategoryName,
                                                            gb.PriceId,
                                                            gb.priceEnt,
                                                            gb.gestion_place_id,
                                                            gb.hashKey,
                                                            gb.prisePlace,
                                                            gb.nbSeatDispo,
                                                            gb.isAutomatique,
                                                            gb.isPlacementLibre,
                                                            gb.isSurPlan,
                                                            gb.IsVoirPlace,
                                                            gb.nbMax,
                                                            gb.nbMin
                                                        }).Select(ctc => new ws_DTO.CouplePossibleTarifCateg()
                                                        {
                                                            categId = ctc.Key.CategoryId,
                                                            categName = ctc.Key.CategoryName,
                                                            tarifId = ctc.Key.PriceId,
                                                            tarifName = (ctc.Key.priceEnt == null) ? "" : ctc.Key.priceEnt.Price_name,
                                                            gpId = ctc.Key.gestion_place_id,
                                                            dispo = ctc.Key.nbSeatDispo,
                                                            ischoixauto = (ctc.Key.isAutomatique) ? 1 : 0,
                                                            ischoixplacesurplan = (ctc.Key.isSurPlan) ? 1 : 0,
                                                            isplacementlibre = (ctc.Key.isPlacementLibre) ? 1 : 0,
                                                            isvoirplacement = (ctc.Key.IsVoirPlace) ? 1 : 0,
                                                            NbMax = ctc.Key.nbMax,
                                                            NbMin = ctc.Key.nbMin,
                                                            HashKey = getHash(ctc.Key.gestion_place_id + "/" + osess.SessionId, thisSalt),

                                                        }).ToList();

                                    foreach (var thisCoupleTarifCateg in listCoupleTarifCategs)
                                    {
                                        var thisGestionPlace = listGpThisSession.Where(gp => gp.gestion_place_id == thisCoupleTarifCateg.gpId)
                                                   .Select(gp => gp).FirstOrDefault();

                                        if (thisGestionPlace != null)
                                            thisCoupleTarifCateg.UnitTTCAmount = thisGestionPlace.priceEnt.UnitTTCAmount;
                                    }

                                    thissection.listCoupleTarifCateg = listCoupleTarifCategs;

                                    /*

                                                                             var listCategories = listGpThisSession.Where(f => f.ZoneId == thiszone.ZoneId && f.FloorId == thisfloor.FloorId && f.SectionId == thissection.SectionId).GroupBy(cat => new { cat.CategoryId, cat.CategoryName })
                                                                                   .Select(f => new ws_DTO.CategoryEntity()
                                                                                   {
                                                                                       CategId = f.Key.CategoryId,
                                                                                       Category_name = f.Key.CategoryName
                                                                                   }).ToList();



                                               foreach (var thiscategorie in listCategories)
                                               {



                                                   var listGestionPlaces = listGpThisSession.Where(f => f.ZoneId == thiszone.ZoneId && f.FloorId == thisfloor.FloorId && f.SectionId == thissection.SectionId && f.CategoryId == thiscategorie.CategId)
                                                                                 .GroupBy(gp => new { gp.gestion_place_id, gp.isAutomatique, gp.isPlacementLibre, gp.isSurPlan, gp.IsVoirPlace, gp.prisePlace, gp.nbMin, gp.nbMax, gp.nbMaxOnSeance })
                                                                                 .Select(f => new ws_DTO.GestionPlaceEntity()
                                                                                 {
                                                                                     gestion_place_id = f.Key.gestion_place_id,
                                                                                     isAutomatique = f.Key.isAutomatique,
                                                                                     isPlacementLibre = f.Key.isPlacementLibre,
                                                                                     isSurPlan = f.Key.isSurPlan,
                                                                                     IsVoirPlace = f.Key.IsVoirPlace,
                                                                                     nbMin = f.Key.nbMin,
                                                                                     nbMax = f.Key.nbMax,
                                                                                     nbMaxOnSeance = f.Key.nbMaxOnSeance
                                                                                 }).ToList();






                                                   if (thisGestionPlace != null)
                                                   {
                                                       thisGestionPlace.hashKey = getHash(thisGestionPlace.gestion_place_id + "/" + thisGestionPlace.SessionId, thisSalt);
                                                       thisGestionPlace.priceEnt.hashKey = getHash(thisGestionPlace.priceEnt.PriceId + "_" + thisGestionPlace.priceEnt.VtsId + "_" + thisGestionPlace.CategoryId + "/" + thisGestionPlace.SessionId, thisSalt);
                                                       listGpThisSessionFiltree.Add(thisGestionPlace);
                                                   }



                                                   var listPrices = listGpThisSession.Where(f => f.ZoneId == thiszone.ZoneId && f.FloorId == thisfloor.FloorId && f.SectionId == thissection.SectionId && f.CategoryId == thiscategorie.CategId)
                                                                             .GroupBy(t => new { t.priceEnt })
                                                                             .Select(f => new ws_DTO.PriceEntity()
                                                                             {
                                                                                 gestion_place_id = f.Key.gestion_place_id,
                                                                                 isAutomatique = f.Key.isAutomatique,
                                                                                 isPlacementLibre = f.Key.isPlacementLibre,
                                                                                 isSurPlan = f.Key.isSurPlan,
                                                                                 IsVoirPlace = f.Key.IsVoirPlace,
                                                                                 nbMin = f.Key.nbMin,
                                                                                 nbMax = f.Key.nbMax,
                                                                                 nbMaxOnSeance = f.Key.nbMaxOnSeance
                                                                             }).ToList();



                                               }

            */

                                }

                            }

                        }
                    }





                    osess.listGps = listGpThisSessionFiltree;
                    //        //oevtWork.ListSessions.Add(osess);
                }


            }
            else
            {

                //return error listhorsabo ne contient pas la manifid passée en param
                var eventIdHorsAbo = listEventsHorsAbo.Where(e => e.EventId == eventId).FirstOrDefault();

                GestionTraceManager.WriteLogError(structureId, "error: Verifier le paramètrage offreIdHorsAbo:" + offreIdHorsAbo + " LoadGT ==> manifid dans listhorsabo différent de la manif passée en param " + eventIdHorsAbo.EventId + " != " + eventId);

                string error = "error:bad_parametrage";
                return Json(error, JsonRequestBehavior.AllowGet);
            }
            //}
            //else // session id = 0, toutes les séances
            //{
            //    oevt.ListSessions = new List<ws_DTO.SessionEntity>();
            //    foreach (var osess in oevt.ListSessions)
            //    {
            //        List<ws_DTO.GestionPlaceEntity> listGpThisSession = listGpGrilleTarif.Where(gp => gp.SessionId == osess.SessionId).ToList();
            //        List<ws_DTO.GestionPlaceEntity> listGpThisSessionFiltree = new List<ws_DTO.GestionPlaceEntity>();
            //        foreach (ws_DTO.GestionPlaceEntity gp in listGpThisSession)
            //        {
            //            if (categId == 0 || gp.CategoryId == categId)
            //            {
            //                if (sessionId == 0 || gp.SessionId == sessionId)
            //                {
            //                    gp.hashKey = getHash(gp.gestion_place_id + "/" + gp.SessionId, thisSalt);
            //                    gp.priceEnt.hashKey = getHash(gp.priceEnt.PriceId + "_" + gp.priceEnt.VtsId + "_" + gp.CategoryId + "/" + gp.SessionId, thisSalt);
            //                    listGpThisSessionFiltree.Add(gp);
            //                }
            //            }
            //        }
            //        osess.listGps = listGpThisSessionFiltree;
            //       // oevtWork.ListSessions.Add(osess);
            //    }
            //}

            if (typeGrilleTarif == "HA")
            {
                var lstPropertyOfEvent = wcfThemis.GetListPropertiesOfEvent(structureId, new List<int>() { eventId }.ToArray()).ToList();

                var isZapperZoneEtageIndiv = lstPropertyOfEvent.Where(p => p.Code == "ZapperEtag").Select(p => p.Value).FirstOrDefault();

                oevt.IsZapperZoneEtageIndiv = (lstPropertyOfEvent.Where(p => p.Code == "ZapperEtag").Select(p => p.Value).FirstOrDefault() == 1) ? true : false;
                oevt.IsZapperZoneEtageSectionIndiv = (lstPropertyOfEvent.Where(p => p.Code == "ZapperZES").Select(p => p.Value).FirstOrDefault() == 1) ? true : false;
            }
        

            return Json(oevt, JsonRequestBehavior.AllowGet);
            //return Json(listGpGrilleTarif, JsonRequestBehavior.AllowGet);

        }

        public ActionResult partIndex()
        {
            ViewBag.eventId = int.Parse(RouteData.Values["eventId"].ToString());
            ViewBag.sessionId = int.Parse(RouteData.Values["sessionId"].ToString());

            ViewBag.zoneId = int.Parse(RouteData.Values["zoneId"].ToString());
            ViewBag.floorId = int.Parse(RouteData.Values["floorId"].ToString());
            ViewBag.sectionId = int.Parse(RouteData.Values["sectionId"].ToString());
            ViewBag.categId = int.Parse(RouteData.Values["categId"].ToString());
            ViewBag.aboIdx = int.Parse(RouteData.Values["aboidx"].ToString());
            return PartialView("~/Views/HorsAbo/vPricesGridHorsAbo.cshtml");

        }


    }
}