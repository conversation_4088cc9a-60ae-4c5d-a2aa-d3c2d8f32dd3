﻿using aboV2.App_Code;
using aboV2.App_Code.crypto;
using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Web.Caching;
using System.Web.Configuration;
using System.Web.Hosting;
using System.Web.Mvc;
using System.Web.Routing;
using utilitaires2010;
using utilitaires2010.crypto;
using WebTracing2010;
using WebTracing2010.FileAttente;
using ws_DTO;
using ws_DTO.objets_liaisons;

namespace aboV2.Controllers
{

    public class BaseController : Controller
    {

        protected string thisSalt = "Ab0V2";
        public static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Mets en ViewBag la liste des langues
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        protected List<LanguageEntity> GetLanguagesList(int structureId)
        {

            if (structureId > 0)
            {

                List<LanguageEntity> lstLanguages = new List<LanguageEntity>();
                double delaiCache = 1800; // 30 mns

                string cacheNameLanguages = "lstLanguages" + structureId;
                if (System.Web.HttpContext.Current.Cache[cacheNameLanguages] != null)
                {
                    lstLanguages = (List<LanguageEntity>)System.Web.HttpContext.Current.Cache[cacheNameLanguages];
                }
                else
                {
                    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();

                    lstLanguages = wcfThemis.LoadLanguages(structureId).ToList();
                    System.Web.HttpContext.Current.Cache.Insert(cacheNameLanguages + structureId, lstLanguages, null, DateTime.Now.AddSeconds(delaiCache), TimeSpan.Zero);
                }
                ViewBag.LanguagesList = lstLanguages;

                return lstLanguages;
            }

            return new List<LanguageEntity>();


        }

        protected string getHash(string tohash, string salt)
        {
            App_Code.crypto.Sha1 sha1 = new App_Code.crypto.Sha1(tohash + salt);
            return sha1.getSha1();
        }

        /// <summary>
        /// get grille tarif de l'evenement (avec gestion du cache)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        protected List<GestionPlaceEntity> GetGrilleTarif(int structureId, int eventId, string langCode, int offreIdHorsAbo)
        {
            string cacheName = "GetGrilleTarif_" + structureId + "_" + eventId + "_" + langCode;

            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                List<int> listIntVide = new List<int>();
                List<GestionPlaceEntity> listGps = wcf.LoadAllInternetGrilleTarifs(structureId, langCode, 0, 0, eventId, 0, listIntVide.ToArray(), listIntVide.ToArray(), offreIdHorsAbo).ToList();
                System.Web.HttpContext.Current.Cache.Insert(cacheName, listGps, null, DateTime.Now.AddSeconds(60 * 10), TimeSpan.Zero);
                return listGps;
            }
            else
            {
                return (List<GestionPlaceEntity>)System.Web.HttpContext.Current.Cache[cacheName];
            }

        }

        protected List<EventEntity> GetAllEvents(int structureId, List<int> eventId, string langCode)
        {
            Logger log = new Logger();
            string cacheName = "GetAllEvents_" + structureId + "_" + string.Join("/", eventId) + "_" + langCode;

            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                List<int> listIntVide = new List<int>();
                var gestionPlaceList = wcf.LoadEventsInGp(structureId, langCode, eventId.ToArray());

                if (gestionPlaceList != null)
                {
                    List<EventEntity> listGps = gestionPlaceList.ToList();
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listGps, null, DateTime.Now.AddSeconds(60 * 10), TimeSpan.Zero);
                    return listGps;
                }

                log.LogMsg(structureId, LogLevel.ERROR, $"GetAllEvents gestionPlaceList retourne null");
                return new List<EventEntity>();
            }
            else
            {
                return (List<EventEntity>)System.Web.HttpContext.Current.Cache[cacheName];
            }
        }

        /// <summary>
        /// get mode d'obtention des formules
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="listGestionPlaceId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        protected List<ProductFraisEnvois> GetModeObtention(int structureId, List<int> listGestionPlaceId, string langCode)
        {
            Logger log = new Logger();
            try
            {
                string cacheName = "GetModeObtention" + structureId + "_" + string.Join("/", listGestionPlaceId) + "_" + langCode;

                log.LogMsg(structureId, LogLevel.DEBUG, "GetModeObtention, cach=" + cacheName);

                if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
                {
                    wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                    List<int> listIntVide = new List<int>();
                    List<ProductEntity> listMosW = wcf.LoadCommonsMO_ListFormulaTarifSeance_abo(structureId, langCode, listGestionPlaceId.ToArray()).ToList();
                    List<ProductFraisEnvois> listMos = new List<ProductFraisEnvois>();
                    foreach (ProductEntity prodW in listMosW)
                    {
                        ProductFraisEnvois pmo = prodW.As<ProductFraisEnvois>();
                        listMos.Add(pmo);
                    }


                    // chercher dans le config.ini les modes obtentions de type "envoi domicile" pour pouvoir generer une action js lors du choix des modes d'obtention
                    // a terme, devrait être rempli depuis bdd Rodrigue
                    myDictionary mySSC = new myDictionary();
                    mySSC = mySSC.GetDictionaryFromCache(structureId);
                    List<string> listEnvoiDomicile = new List<string>();
                    if (mySSC.Contains("TYPEPRODUCTSENVOIDOMICILE") && mySSC["TYPEPRODUCTSENVOIDOMICILE"] != null && mySSC["TYPEPRODUCTSENVOIDOMICILE"] != "")
                    {
                        listEnvoiDomicile = mySSC["TYPEPRODUCTSENVOIDOMICILE"].Split(',').ToList();
                    }
                    List<string> listPrintAtHome = new List<string>();
                    if (mySSC.Contains("TYPEPRODUCTSPRINTATHOME") && mySSC["TYPEPRODUCTSPRINTATHOME"] != null && mySSC["TYPEPRODUCTSPRINTATHOME"] != "")
                    {
                        listPrintAtHome = mySSC["TYPEPRODUCTSPRINTATHOME"].Split(',').ToList();
                    }

                    foreach (ProductFraisEnvois prod in listMos)
                    {
                        prod.typeProduct = "ND";
                        if (listEnvoiDomicile.Contains(prod.ProduitId.ToString()))
                            prod.typeProduct = "ENVDMCL";
                        if (listPrintAtHome.Contains(prod.ProduitId.ToString()))
                            prod.typeProduct = "PRNTATHM";
                    }

                    log.LogMsg(structureId, LogLevel.DEBUG, "GetModeObtention from db =>" + listMos.Count);
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listMos, null, DateTime.Now.AddSeconds(60 * 10), TimeSpan.Zero);

                    return listMos;
                }
                else
                {
                    List<ProductFraisEnvois> listMos = (List<ProductFraisEnvois>)System.Web.HttpContext.Current.Cache[cacheName];
                    log.LogMsg(structureId, LogLevel.DEBUG, "GetModeObtention from cache =>" + listMos.Count);
                    return listMos;
                }
            }
            catch (Exception ex)
            {
                GestionTrace.WriteLog(structureId, "GetModeObtention ('" + structureId + ",,,," + langCode + "):" + ex.Message);
                throw ex;
            }


        }

        /// <summary>
        /// get products liés aux formules
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="ListformulasIds"></param>
        /// <param name="ListSessionsIds"></param>
        /// <param name="ListtarifsIds"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        protected List<ProductEntity> GetProducts(int structureId, int identiteId, List<int> ListformulasIds, string langCode)
        {
            Logger log = new Logger();
            try
            {
                string cacheName = "GetProducts_Form" + structureId + "_" + string.Join("/", ListformulasIds) + "_" + langCode;
                log.LogMsg(structureId, LogLevel.DEBUG, "GetProducts_Form, cach=" + cacheName);

                if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
                {
                    wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                    List<int> listIntVide = new List<int>();
                    List<ProductEntity> listProducts = wcf.LoadProductsOfFormulas(structureId, langCode, identiteId, ListformulasIds.ToArray()).ToList();
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listProducts, null, DateTime.Now.AddSeconds(60 * 10), TimeSpan.Zero);

                    log.LogMsg(structureId, LogLevel.DEBUG, "GetProducts_Form from db =>" + listProducts.Count);

                    return listProducts;
                }
                else
                {
                    List<ProductEntity> listProducts = (List<ProductEntity>)System.Web.HttpContext.Current.Cache[cacheName];
                    log.LogMsg(structureId, LogLevel.DEBUG, "GetProducts_Form from cache =>" + listProducts.Count);
                    return listProducts;
                }
            }
            catch (Exception ex)
            {
                GestionTrace.WriteLog(structureId, "GetProducts_Form ('" + structureId + ",,,," + langCode + "):" + ex.Message);
                throw ex;
            }
        }

        protected Dictionary<int, int> GetMaquettesOfGpMo(int structureId, List<int> listGps, int moId)
        {
            Logger log = new Logger();
            try
            {
                string cacheName = "GetMaquettesOfGpMo" + structureId + "_" + string.Join("/", listGps) + "_" + "_" + moId;
                log.LogMsg(structureId, LogLevel.DEBUG, "GetMaquettesOfGpMo, cach=" + cacheName);

                if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
                {
                    wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                    Dictionary<int, int> dicoGpIdMaquette = wcf.LoadMaquettesOfMoGp(structureId, listGps.Distinct().ToArray(), moId);
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, dicoGpIdMaquette, null, DateTime.Now.AddSeconds(60 * 10), TimeSpan.Zero);

                    log.LogMsg(structureId, LogLevel.DEBUG, "GetMaquettesOfGpMo from db =>" + dicoGpIdMaquette.Keys.Count);
                    return dicoGpIdMaquette;
                }
                else
                {
                    Dictionary<int, int> dicoGpIdMaquette = (Dictionary<int, int>)System.Web.HttpContext.Current.Cache[cacheName];
                    log.LogMsg(structureId, LogLevel.DEBUG, "GetMaquettesOfGpMo from cache =>" + dicoGpIdMaquette.Keys.Count);
                    return dicoGpIdMaquette;
                }
            }
            catch (Exception ex)
            {
                GestionTrace.WriteLog(structureId, "GetMaquettesOfGpMo ('" + structureId + "," + string.Join(",", listGps) + ", " + moId + "):" + ex.Message);
                throw ex;
            }
        }

        /// <summary>
        /// get produit de type "questionnaire", internaute doit repondre à des questions "produit" (type association soutenue parmi un choix, etc)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="ListformulasIds"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        protected List<ProductQuestionaire> GetQuestionnaireProducts(int structureId, int identiteId, List<int> ListformulasIds, string langCode)
        {
            Logger log = new Logger();
            try
            {
                string cacheName = "GetQuestionnaireProducts" + structureId + "_" + string.Join("/", ListformulasIds) + "_" + langCode;
                if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
                {
                    wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                    List<int> listIntVide = new List<int>();
                    List<ProductQuestionaire> listProducts = wcf.LoadQuestionnairesProductsOfFormulas(structureId, langCode, identiteId, ListformulasIds.ToArray()).ToList();
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listProducts, null, DateTime.Now.AddSeconds(60 * 10), TimeSpan.Zero);

                    log.LogMsg(structureId, LogLevel.DEBUG, "GetQuestionnaireProducts from db =>" + listProducts.Count);

                    return listProducts;
                }
                else
                {
                    List<ProductQuestionaire> listProducts = (List<ProductQuestionaire>)System.Web.HttpContext.Current.Cache[cacheName];
                    log.LogMsg(structureId, LogLevel.DEBUG, "GetQuestionnaireProducts from cache =>" + listProducts.Count);
                    return listProducts;
                }
            }
            catch (Exception ex)
            {
                GestionTrace.WriteLog(structureId, "GetQuestionnaireProducts ('" + structureId + ",,,," + langCode + "):" + ex.Message);
                throw ex;
            }
        }

        /// <summary>
        /// get products frais dossier (1 par commande, pas lié au nombre d'abonnements)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="ListformulasIds"></param>
        /// <param name="ListSessionsIds"></param>
        /// <param name="ListtarifsIds"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        protected List<ProductEntity> GetFraisProducts(int structureId, int identiteId, List<int> ListformulasIds, string langCode)
        {
            Logger log = new Logger();
            try
            {
                string cacheName = "GetFraisProducts" + structureId + "_" + string.Join("/", ListformulasIds) + "_" + langCode;

                //log.LogMsg(structureId, LogLevel.DEBUG, "GetFraisProducts, cach=" + cacheName);

                if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
                {
                    wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                    List<int> listIntVide = new List<int>();
                    List<ProductEntity> listProducts = wcf.LoadFraisProductsOfFormulas(structureId, langCode, identiteId, ListformulasIds.ToArray()).ToList();
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listProducts, null, DateTime.Now.AddSeconds(60 * 10), TimeSpan.Zero);

                    log.LogMsg(structureId, LogLevel.DEBUG, "GetFraisProducts from db =>" + listProducts.Count);

                    return listProducts;
                }
                else
                {
                    List<ProductEntity> listProducts = (List<ProductEntity>)System.Web.HttpContext.Current.Cache[cacheName];
                    log.LogMsg(structureId, LogLevel.DEBUG, "GetFraisProducts from cache =>" + listProducts.Count);
                    return listProducts;


                }
            }
            catch (Exception ex)
            {
                GestionTrace.WriteLog(structureId, "GetFraisProducts ('" + structureId + ",,,," + langCode + "):" + ex.Message);
                throw ex;
            }
        }

        protected override void Initialize(RequestContext requestContext)
        {

            base.Initialize(requestContext);

            int structureId = 0;
            bool fileAttIsFull = false;
            bool m_bIsTerminating = false;


            if (Session["YesICanContinue"] == null || Session["YesICanContinue"].ToString() != "true")
            {
                string urlQueuePage = "queuing.htm";


                // check file d'attente
                if (RouteData.Values["structureId"] != null)
                {

                    structureId = int.Parse(RouteData.Values["structureId"].ToString());

                    /*    if (structureId == 0)
                        {
                            Exception ex = new Exception("structureId =0! dans initialize");
                            throw (ex);
                        }*/

                    System.Web.HttpContext.Current.Session["idstructure"] = structureId;

                    myDictionary mySSC = new myDictionary();
                    mySSC = mySSC.GetDictionaryFromCache(structureId);

                    string overwriteFAKey = "overwriteFA";
                    string cacheKeyName = overwriteFAKey + structureId;

                    if (System.Web.HttpContext.Current.Cache[cacheKeyName] != null)
                    {
                        overwriteFAKey = (string)System.Web.HttpContext.Current.Cache[cacheKeyName];
                    }
                    else
                    {
                        if (mySSC.Contains("FILEATTENTEBYPASSKEY") && mySSC["FILEATTENTEBYPASSKEY"] != "")
                        {
                            overwriteFAKey = mySSC["FILEATTENTEBYPASSKEY"];
                        }
                        else
                        {
                            string overwriteFAKeyTmlp = WebConfigurationManager.AppSettings["FILEATTENTEBYPASSKEY"];
                            overwriteFAKey = overwriteFAKeyTmlp ?? overwriteFAKey;
                        }
                        System.Web.HttpContext.Current.Cache.Insert(cacheKeyName, overwriteFAKey, null, DateTime.Now.AddSeconds(60), TimeSpan.Zero);
                    }



                    if (RouteData.Values["paramOptionnel"] != null && RouteData.Values["paramOptionnel"].ToString() == overwriteFAKey)
                    {
                        Session["YesICanContinue"] = "true";
                    }
                    else
                    {

                        if (mySSC.Contains("FILEATTENTEQUEUINGPAGE") && mySSC["FILEATTENTEQUEUINGPAGE"] != null && mySSC["FILEATTENTEQUEUINGPAGE"] != "")
                        {
                            urlQueuePage = mySSC["FILEATTENTEQUEUINGPAGE"]; // une page de file attente pour la structure
                        }
                        if (mySSC.Contains("FILEATTENTEABOQUEUINGPAGE") && mySSC["FILEATTENTEABOQUEUINGPAGE"] != null && mySSC["FILEATTENTEABOQUEUINGPAGE"] != "")
                        {
                            urlQueuePage = mySSC["FILEATTENTEABOQUEUINGPAGE"]; // une page de file attente pour la structure en ABO !
                        }

                        if (RouteData.Values["langCode"] != null)
                            urlQueuePage = urlQueuePage.Replace("[langcode]", RouteData.Values["langCode"].ToString());

                        if (mySSC.Contains("FILEATTENTEVIAQUEUINGSITE") && mySSC["FILEATTENTEVIAQUEUINGSITE"] == "O")
                        {
                            bool checkOk = false;
                            if (RouteData.Values["tnQueuing"] != null && RouteData.Values["hashKQueuing"] != null)
                            {
                                //Session["YesICanContinue"] = "true";


                                int myTicketNumber = 0;
                                if (int.TryParse(RouteData.Values["tnQueuing"].ToString(), out myTicketNumber))
                                {
                                    string myHash = RouteData.Values["hashKQueuing"].ToString();
                                    string myIP = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
                                    if (myIP == null)
                                    {
                                        myIP = System.Web.HttpContext.Current.Request.UserHostAddress.ToString().Replace("'", "''");
                                    }
                                    if (myIP == "127.0.0.1")
                                        myIP = "::1";  // mvc envoie l'adresse ipv6 en local

                                    string hashC = Securities.Queuing.getHashForWebSiteVente(myTicketNumber, myIP);

                                    if (hashC == myHash)
                                    {
                                        Session["queuingTicket"] = myTicketNumber;
                                        Session["YesICanContinue"] = "true";
                                        checkOk = true;
                                    }
                                    else
                                    {
                                        string hashC2 = Securities.Queuing.getHashForWebSiteVente(myTicketNumber, myIP, structureId, true);
                                        if (hashC2 == myHash)
                                        {
                                            Session["queuingTicket"] = myTicketNumber;
                                            Session["YesICanContinue"] = "true";
                                            checkOk = true;
                                        }
                                    }


                                }
                            }


                            if (!checkOk)
                            {

                                //System.Web.HttpContext.Current.Cache.Insert("fileAttIsFull" + structureId.ToString(), "true", null, DateTime.Now.AddSeconds(60), TimeSpan.Zero);
                                string originUrl = "";

                                //RouteData.Route == RouteTable.Routes[""]
                                originUrl = System.Web.HttpContext.Current.Request.Url.AbsoluteUri;
                                if (RouteData.Values["tnQueuing"] != null)
                                {
                                    originUrl = originUrl.Replace("/" + RouteData.Values["tnQueuing"] + "/", "/{tn}/");
                                }
                                if (RouteData.Values["hashKQueuing"] != null)
                                {
                                    originUrl = originUrl.Replace("/" + RouteData.Values["hashKQueuing"] + "", "/{quHash}");
                                }
                                else
                                {
                                    originUrl += "/{tn}/{quHash}";
                                }



                                System.Web.HttpContext.Current.Response.Redirect(urlQueuePage + "/?origin=" + Server.UrlEncode(originUrl), true);
                                // HttpContext.Current.ApplicationInstance.CompleteRequest();
                                m_bIsTerminating = true;
                            }
                        }
                        else
                        {


                            if (mySSC.Contains("FILEATTENTEQUEUINGPAGE") && mySSC["FILEATTENTEQUEUINGPAGE"] != null && mySSC["FILEATTENTEQUEUINGPAGE"] != "")
                            {
                                urlQueuePage = mySSC["FILEATTENTEQUEUINGPAGE"]; // une page de file attente pour la structure
                            }
                            if (mySSC.Contains("FILEATTENTEABOQUEUINGPAGE") && mySSC["FILEATTENTEABOQUEUINGPAGE"] != null && mySSC["FILEATTENTEABOQUEUINGPAGE"] != "")
                            {
                                urlQueuePage = mySSC["FILEATTENTEABOQUEUINGPAGE"]; // une page de file attente pour la structure en ABO !
                            }


                            if ((string)System.Web.HttpContext.Current.Cache["fileAttIsFull" + structureId.ToString()] == "true")
                            {

                                System.Web.HttpContext.Current.Response.Redirect(urlQueuePage, true);
                                // HttpContext.Current.ApplicationInstance.CompleteRequest();
                                m_bIsTerminating = true;
                            }
                            else
                            {
                                string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];
                                FileAttente fileA = new FileAttente();
                                if (mySSC != null)
                                {

                                    int maxU = 0; int delay = 0;
                                    if (mySSC["FILEATTENTEMAXACTIVESUSERS"] != null && mySSC["FILEATTENTEDELAYINSECONDS"] != null && int.TryParse(mySSC["FILEATTENTEMAXACTIVESUSERS"], out maxU) && int.TryParse(mySSC["FILEATTENTEDELAYINSECONDS"], out delay))
                                    {
                                        fileAttIsFull = fileA.IsFull(typeRun, maxU, delay); // dico existe et configuré -> val definie dans config.ini
                                    }
                                    else
                                    {
                                        fileAttIsFull = fileA.IsFull(typeRun); // dico existe mais n'est pas configuré -> val par defaut
                                    }
                                }
                                else
                                {
                                    fileAttIsFull = fileA.IsFull(typeRun); // dico pas encore chargé (?!?)
                                }
                            }
                            if (fileAttIsFull)
                            {
                                // file d'attente pleine !
                                System.Web.HttpContext.Current.Cache.Insert("fileAttIsFull" + structureId.ToString(), "true", null, DateTime.Now.AddSeconds(60), TimeSpan.Zero);
                                System.Web.HttpContext.Current.Response.Redirect(urlQueuePage, true);
                                // HttpContext.Current.ApplicationInstance.CompleteRequest();
                                m_bIsTerminating = true;
                            }
                            else
                            {
                                if (!m_bIsTerminating)
                                {
                                    Session["YesICanContinue"] = "true";
                                    //base.ProcessRequest(context);
                                }
                            }
                        }
                    }
                 
                }
            }
            if (!m_bIsTerminating)
            {
                ViewBag.errorCode = 0;
                ViewBag.errorMessage = "";
                ViewBag.FirstPage = "";

                int eventId = 0, profilAcheteurId = 0, identiteId = 0;
                string langCode = "";
                bool isAMobile = false;




                //reset la session si on change de structureId
                if (RouteData.Values["structureId"] != null && Session["structureId"] != null && int.Parse(RouteData.Values["structureId"].ToString()) != int.Parse(Session["structureId"].ToString()))
                {
                    ViewBag.SessionClear = true;
                    Session.Clear();
                }
                else
                {
                    ViewBag.SessionClear = false;
                }

                if (RouteData.Values["eventId"] != null)
                {
                    eventId = int.Parse(RouteData.Values["eventId"].ToString());

                }
                if (RouteData.Values["profilAcheteurId"] != null)
                {
                    profilAcheteurId = int.Parse(RouteData.Values["profilAcheteurId"].ToString());
                }

                string controllerName = RouteData.Values["controller"].ToString();
                string actionName = RouteData.Values["action"].ToString();
                string fileNameCurrent = actionName;

                if (RouteData.Values["structureId"] != null && RouteData.Values["langCode"] != null)
                {


                    structureId = int.Parse(RouteData.Values["structureId"].ToString());
                    /*  if (structureId == 0)
                      {
                          Exception ex = new Exception("structureId =0! dans initialize");
                          throw (ex);
                      }
                      */

                    langCode = RouteData.Values["langCode"].ToString();
                    isAMobile = Request.Browser.IsMobileDevice;

                    myDictionary mySSC = new myDictionary();
                    mySSC = mySSC.GetDictionaryFromCache(structureId);

                    //    GestionTrace.WriteLog(structureId, "BaseController Initialize ('" + structureId + "," + langCode + "," + isAMobile + ")");

                    if (actionName == "Index" || actionName == "partIndex")  // partIndex pour partialView renvoyé par seatsPlan
                        fileNameCurrent = controllerName;

                    ViewBag.structureId = structureId;
                    ViewBag.langCode = langCode;

                    ViewBag.JavascriptUrlCustomPackage = LoadJavascriptFiles("physicalPathOfJs", structureId, eventId, profilAcheteurId, langCode, "customPackage", isAMobile, false);
                    ViewBag.JavascriptUrl = LoadJavascriptFiles("physicalPathOfJs", structureId, eventId, profilAcheteurId, langCode, fileNameCurrent, isAMobile, false);
                    ViewBag.CssUrl = LoadVisualCssFiles(structureId, eventId, profilAcheteurId, langCode, isAMobile);


                    ViewBag.Banner = LoadVisualBannerTemplate(structureId, eventId, profilAcheteurId, "." + langCode, isAMobile, mySSC, true);
                    //paramètres spécifique du client (json file)
                    SetSettingsOfCustomer(structureId, eventId, profilAcheteurId, langCode);

                    ViewBag.SettingsZapperZESMerge = GetSettingsOfZapperZES(structureId, eventId, profilAcheteurId, langCode);

                    Session["structureId"] = structureId;
                    Session["eventId"] = eventId;
                    Session["profilAcheteurId"] = profilAcheteurId;
                    Session["langCode"] = langCode;


                    SetCustomerVersionViewbag(mySSC);

                    //Mets en Viewbag la liste des langues
                    //  GetLanguagesList(structureId);

                    ViewBag.FirstPage = HttpContext.Request.Url.AbsoluteUri;
                }

                //@ViewBag.IdentityId = identiteId;
                //Sha1 sha = new Sha1(identiteId + "/" + currUserId);
                //@ViewBag.HashKey = sha.getSha1(); = currUserId;
                //Sha1 sha = new Sha1(identiteId + "/" + currUserId);
                // @ViewBag.HashKey = sha.getSha1();


                if (Session["identiteId"] != null && ViewBag.identiteId == null)
                {
                    ViewBag.identiteId = int.Parse(Session["identiteId"].ToString());
                    identiteId = ViewBag.identiteId;
                }
                else
                {
                    ViewBag.identiteId = 0;
                }
                int currUserId = 0;
                if (Session["currIdUser"] != null)
                    currUserId = int.Parse(Session["currIdUser"].ToString());

                if (Session["structureId"] != null && ViewBag.structureId == null)
                {
                    ViewBag.structureId = int.Parse(Session["structureId"].ToString());
                    structureId = ViewBag.structureId;
                }
                else
                {
                    if (Session["structureId"] == null)
                    {
                        ViewBag.errorCode = 1;
                        ViewBag.errorMessage = "Session lost";
                    }
                }

                if (Session["langCode"] != null && ViewBag.langCode == null)
                {
                    ViewBag.langCode = Session["langCode"].ToString();
                    langCode = ViewBag.langCode;
                }

                if (Session["eventId"] != null && ViewBag.langCode == null)
                {
                    eventId = int.Parse(Session["eventId"].ToString());
                }

                if (Session["profilAcheteurId"] != null && ViewBag.langCode == null)
                {
                    profilAcheteurId = int.Parse(Session["profilAcheteurId"].ToString());
                }


                // Sha1 sha = new Sha1(identiteId + "/" + currUserId);
                //  @ViewBag.HashKey = sha.getSha1();

                string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");
                /*
                                if (structureId == 0)
                                {
                                    Exception ex = new Exception("structureId =0! dans initialize");
                                     throw (ex);
                                }*/

                App_Code.crypto.Sha1 sha = new App_Code.crypto.Sha1(String.Format("{0,4:0000}", structureId) + "|" + identiteId + cryptoKey);
                ViewBag.HashKey = sha.getSha1();


                //  ViewBag.structureId = structureId;
                //  ViewBag.langCode = langCode;
                ViewBag.JavascriptUrlCustomPackage = LoadCustomPackageJavascriptFiles("physicalPathOfCustomPackageJs", structureId, eventId, profilAcheteurId, langCode, "customPackage", isAMobile, true);
                ViewBag.JavascriptUrl = LoadJavascriptFiles("physicalPathOfJs", structureId, eventId, profilAcheteurId, langCode, fileNameCurrent, isAMobile, false);
                ViewBag.CssUrl = LoadVisualCssFiles(structureId, eventId, profilAcheteurId, langCode, isAMobile);


                //commentaires des pages (commentairehaut_Home)
                ViewBag.CommentaireHead = GetHeadCommentaire("head", structureId, eventId, langCode);
                ViewBag.CommentaireHaut = GetLiteralCommentaireEnclosed(RouteData.Values["controller"].ToString(), "haut", structureId, eventId, langCode);
                ViewBag.CommentaireBas = GetLiteralCommentaireEnclosed(RouteData.Values["controller"].ToString(), "bas", structureId, eventId, langCode);

                ViewBag.Footer = GetFooterTemplate(structureId, eventId, profilAcheteurId, "." + langCode, true);

                myDictionary mySSCBanner = new myDictionary();
                if (structureId > 0)
                {
                    mySSCBanner = mySSCBanner.GetDictionaryFromCache(structureId);
                    ViewBag.Banner = LoadVisualBannerTemplate(structureId, eventId, profilAcheteurId, "." + langCode, isAMobile, mySSCBanner, true);


                    SetCustomerVersionViewbag(mySSCBanner);

                    //paramètres spécifique du client (json file)
                    SetSettingsOfCustomer(structureId, eventId, profilAcheteurId, langCode);
                    ViewBag.SettingsZapperZESMerge = GetSettingsOfZapperZES(structureId, eventId, profilAcheteurId, langCode);

                    ViewBag.Page = controllerName;
                }


                //Mets en Viewbag la liste des langues
                GetLanguagesList(structureId);



                // ViewBag.SettingsCustomer = LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, "." + langCode, false);
                //  ViewBag.SettingsDefaultCustomer = LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, "." + langCode, true);


                ////    ViewBag.XmlTranslateFiles = LoadXmlTranslateFiles(structureId, eventId, profilAcheteurId, "." + langCode, "translate");


                /*if (Session["structureId"] != null && ViewBag.structureId == null)
                {
                    ViewBag.structureId = int.Parse(Session["structureId"].ToString());

                    ViewBag.JavascriptUrl = LoadJavascriptFiles(structureId, eventId, profilAcheteurId, langCode, fileNameCurrent, isAMobile);
                    ViewBag.CssUrl = LoadVisualCssFiles(structureId, eventId, profilAcheteurId, langCode, isAMobile);
                }

                if (Session["langCode"] != null && ViewBag.langCode == null)
                    ViewBag.langCode = Session["langCode"].ToString();
                    */

                //TODO 
                /*
                 * mettre en session structure id
                 * 
                 * 
                 * */
                /* if (Session["structureId"] != null && Session["langCode"] != null)
                 {

                     int structureId = int.Parse(Session["structureId"].ToString());
                     int eventId = int.Parse(Session["eventId"].ToString());
                     int profilAcheteurId = int.Parse(Session["profilAcheteurId"].ToString());
                     string langCode = Session["langCode"].ToString();
                     bool isAMobile = Request.Browser.IsMobileDevice;


                     string controllerName = RouteData.Values["controller"].ToString();
                     string actionName = RouteData.Values["action"].ToString();
                     string fileNameCurrent = actionName;

                     if (actionName == "Index")
                         fileNameCurrent = controllerName;

                     ViewBag.JavascriptUrl = LoadJavascriptFiles(structureId, eventId, profilAcheteurId, langCode, fileNameCurrent, isAMobile);
                     ViewBag.CssUrl = LoadVisualCssFiles(structureId, eventId, profilAcheteurId, langCode, isAMobile);
                 }*/
            }
        }
        public void SetCustomerVersionViewbag(myDictionary mySSC)
        {
            if (mySSC.Contains("CUSTOMERAREAVERSION") && mySSC["CUSTOMERAREAVERSION"] != null)
            {
                //met en minuscule la valeur (V3)
                Session["customerversion"] = mySSC["CUSTOMERAREAVERSION"].ToLower();
                ViewBag.customerversion = mySSC["CUSTOMERAREAVERSION"].ToLower();
            }
            else
            {
                Session["customerversion"] = "";
                ViewBag.customerversion = "";
            }

        }

        public dynamic GetSettingsOfZapperZES(int structureId, int eventId, int profilAcheteurId, string langCode)
        {

            //  ViewBag.SettingsCustomer = JsonConvert.SerializeObject(LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, "." + langCode, false));
            // ViewBag.SettingsDefaultCustomer = JsonConvert.SerializeObject(LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, "." + langCode, true));
            // logger.Debug("---- GetSettingsOfZapperZES Zapper ZES ----");
            ViewBag.SettingsCustomer = LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, false);
            ViewBag.SettingsDefault = LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, true);

            var listJsonSettingsToMerge = new List<CustomJsonSettings>();

            listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfZapperZESJSON", IsDefault = true });
            listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfZapperZESJSON", IsDefault = false });

            //logger.Debug("SetSettingsOfZapperZES Zapper ZES :" + ViewBag.SettingsCustomer.ToString() + " SettingsDefault " + ViewBag.SettingsDefault.ToString());

            return utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, eventId, profilAcheteurId, "", langCode);

        }


        public void SetSettingsOfCustomer(int structureId, int eventId, int profilAcheteurId, string langCode)
        {

            //  ViewBag.SettingsCustomer = JsonConvert.SerializeObject(LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, "." + langCode, false));
            // ViewBag.SettingsDefaultCustomer = JsonConvert.SerializeObject(LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, "." + langCode, true));
            // logger.Debug("---- SetSettingsOfCustomer ----");
            ViewBag.SettingsCustomer = LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, false);
            ViewBag.SettingsDefault = LoadSettingsOfCustomer(structureId, eventId, profilAcheteurId, true);

            var listJsonSettingsToMerge = new List<CustomJsonSettings>();

            listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true });
            // listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsFutureJSON", IsDefault = true });
            listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false });
            //listJsonSettingsToMerge.Add(new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false });

            //logger.Debug("SettingsCustomer :" + ViewBag.SettingsCustomer.ToString() + " SettingsDefault " + ViewBag.SettingsDefault.ToString());


            // GetApplicationName(structureId);


            /*   GestionTrace.WriteLog(structureId, "SetSettingsOfCustomer  Request.Url.LocalPath " + Request.Url.LocalPath);
               GestionTrace.WriteLog(structureId, "SetSettingsOfCustomer  Request.Url.OriginalString " + Request.Url.OriginalString);
               GestionTrace.WriteLog(structureId, "SetSettingsOfCustomer  Request.Url.PathAndQuery " + Request.Url.PathAndQuery);
               */

            ViewBag.SettingsMerge = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, structureId, eventId, profilAcheteurId, "", langCode);

        }


        public static JObject LoadSettingsOfCustomer(int structureId, int eventId, int profilAcheteurId, bool isDefault)
        {
            string urlJsonFile = string.Empty;
            string physicalPathOfJson = Initialisations.GetKeyAppSettings("physicalPathOfSettingsJSON");

            physicalPathOfJson = physicalPathOfJson.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));
            // physicalPathOfJson = physicalPathOfJson + fileName + ".json";

            List<string> LstOfFilesToFind = Initialisations.GetListFilePathXml(physicalPathOfJson, structureId, eventId, profilAcheteurId, "", isDefault);

            foreach (var item in LstOfFilesToFind)
            {
                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (System.IO.File.Exists(item))
                {
                    using (StreamReader file = new StreamReader(item))
                    using (JsonTextReader reader = new JsonTextReader(file))
                    {
                        JObject o2 = (JObject)JToken.ReadFrom(reader);
                        return o2;
                    }
                }

            }
            return new JObject();
        }

        public BaseController()
        {


        }
        /* public ActionResult Index2(int structureId, string langCode)
         {
             ViewBag.JavascriptUrl = BaseController.LoadJavascriptFiles(structureId, langCode);

             ViewBag.structureId = structureId;
             ViewBag.langCode = langCode;
             // ViewData["structureId"] = Server.UrlEncode(structureId);

             //ViewBag.JavascriptUrl = "/Scripts/app/"+ structureId.ToString("0000")+"/Home.js";
             return View();
         }
         */



        public ActionResult Index()
        {
            /*   int eventId =0, profilAcheteurId = 0;
               if (RouteData.Values["eventId"] != null)
               {
                   eventId = int.Parse(RouteData.Values["eventId"].ToString());

               }
               if (RouteData.Values["profilAcheteurId"] != null)
               {
                   profilAcheteurId = int.Parse(RouteData.Values["profilAcheteurId"].ToString());

               }


               if (RouteData.Values["structureId"] != null && RouteData.Values["langCode"] != null)
               {
                   int structureId = int.Parse(RouteData.Values["structureId"].ToString());

                   string langCode = RouteData.Values["langCode"].ToString(); 

                   ViewBag.structureId = structureId;
                   ViewBag.langCode = langCode;

                   Session["structureId"] = structureId;
                   Session["eventId"] = eventId;
                   Session["profilAcheteurId"] = profilAcheteurId;
                   Session["langCode"] = langCode;

               }

               // ViewBag.structureId = structureId;
               // ViewBag.langCode = langCode;


               // ViewData["structureId"] = Server.UrlEncode(structureId);

               //ViewBag.JavascriptUrl = "/Scripts/app/"+ structureId.ToString("0000")+"/Home.js"; */
            return View();
        }



        public static string LoadVisualCssFiles(int structureId, int eventId, int profilAcheteurId, string langCode, bool isAMobile)
        {


            string physicalPathOfCss = Initialisations.GetKeyAppSettings("physicalPathOfCss");
            physicalPathOfCss = physicalPathOfCss.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));



            // bool hasFindUrl = false;
            List<string> LstOfFilesToFind = Initialisations.GetListFilePathCss(physicalPathOfCss, structureId, eventId, profilAcheteurId, langCode);


            string relativeUrlOfCss = Initialisations.GetKeyAppSettings("relativePathCssOfSite");
            relativeUrlOfCss = relativeUrlOfCss.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));

            foreach (var item in LstOfFilesToFind)
            {
                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (System.IO.File.Exists(item))
                {
                    //logger.Debug(item + " found");
                    //StyleLink.Href = relativeUrlOfCss + Path.GetFileName(item);

                    return relativeUrlOfCss + Path.GetFileName(item);
                    //hasFindUrl = true;
                }
            }

            return "";

            //Todo vérifier les chemins
            //   string url = "files/" + structureId.ToString("0000") +"/ABO/CSS/" + structureId.ToString("0000") + ".css";

            //  return physicalPathOfCss;
        }




        public static string LoadCustomPackageJavascriptFiles(string appSettingKey, int structureId, int eventId, int profilAcheteurId, string langCode, string fileName, bool isAMobile, bool isDefault)
        {

            string urlJsFile = "";


            string physicalPathOfJs = Initialisations.GetKeyAppSettings(appSettingKey);


            if (isAMobile)
                physicalPathOfJs = physicalPathOfJs + fileName + ".mobl.js";
            else
                physicalPathOfJs = physicalPathOfJs + fileName + ".js";

            List<string> LstOfFilesToFind = Initialisations.GetListFilePathJs(physicalPathOfJs, structureId, eventId, profilAcheteurId, langCode, isDefault);

            string relativeUrlOfJs = Initialisations.GetKeyAppSettings("relativePathFilesJsOfSite");
            relativeUrlOfJs = relativeUrlOfJs.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));

            foreach (var item in LstOfFilesToFind)
            {
                if (System.IO.File.Exists(item))
                {
                    var dirName = new DirectoryInfo(Path.GetDirectoryName(item)).Name;

                    if (item.Contains(String.Format("{0,4:0000}", structureId)))
                    {
                        return relativeUrlOfJs + item.Substring(item.IndexOf(String.Format("{0,4:0000}", structureId)));
                    }
                    else if (item.Contains("DEFAULT"))
                    {
                        return relativeUrlOfJs + item.Substring(item.IndexOf("DEFAULT"));
                    }

                    return relativeUrlOfJs + item.Substring(item.IndexOf(String.Format("{0,4:0000}", structureId)));
                }
            }

            return urlJsFile;
        }


        /// <summary>
        /// retourne l'url du fichier javascript à chargé
        /// </summary>
        /// <param name="appSettingKey">clé du web.config</param>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="profilAcheteurId"></param>
        /// <param name="langCode"></param>
        /// <param name="fileName"></param>
        /// <param name="isAMobile"></param>
        /// <returns></returns>
        public static string LoadJavascriptFiles(string appSettingKey, int structureId, int eventId, int profilAcheteurId, string langCode, string fileName, bool isAMobile, bool isDefault)
        {

            string urlJsFile = "";


            string physicalPathOfJs = Initialisations.GetKeyAppSettings(appSettingKey);


            if (isAMobile)
                physicalPathOfJs = physicalPathOfJs + fileName + ".mobl.js";
            else
                physicalPathOfJs = physicalPathOfJs + fileName + ".js";

            List<string> LstOfFilesToFind = Initialisations.GetListFilePathJs(physicalPathOfJs, structureId, eventId, profilAcheteurId, langCode, isDefault);

            string relativeUrlOfJs = Initialisations.GetKeyAppSettings("relativePathJsOfSite");
            relativeUrlOfJs = relativeUrlOfJs.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));

            // string relativeUrlOfJs = "~/Scripts/app/";
            foreach (var item in LstOfFilesToFind)
            {
                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (System.IO.File.Exists(item))
                {
                    // logger.Debug(item + " found");
                    var dirName = new DirectoryInfo(Path.GetDirectoryName(item)).Name;

                    if (String.Format("{0,4:0000}", structureId) == dirName)
                    {
                        //~/
                        return relativeUrlOfJs + String.Format("{0,4:0000}", structureId) + "/" + Path.GetFileName(item);
                        //return "<script type='text/javascript' src='" + relativeUrlOfJs + String.Format("{0,4:0000}", structureId) + "/" + Path.GetFileName(item) + "'></script>";

                    }

                    return relativeUrlOfJs + Path.GetFileName(item);
                    // return "<script type='text/javascript' src='" + relativeUrlOfJs + Path.GetFileName(item) + "'></script>";

                }

            }
            //Todo vérifier les chemins
            // string url = "/Scripts/app/" + structureId.ToString("0000") + "/" + fileName + ".js";

            return urlJsFile;
        }


        /// <summary>
        /// get le chemin (physique) du fichier de translate
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="profilAcheteurId"></param>
        /// <param name="langCode"></param>
        /// <param name="fileName"></param>
        /// <param name="isDefault"></param>
        /// <returns></returns>
        public static string LoadXmlTranslateFiles(int structureId, int eventId, int profilAcheteurId, string langCode, string fileName, bool isDefault)
        {

            string urlXmlFile = "";


            string physicalPathOfJs = Initialisations.GetKeyAppSettings("physicalPathOfTranslateXml");

            physicalPathOfJs = physicalPathOfJs.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));
            physicalPathOfJs = physicalPathOfJs + fileName + ".xml";

            List<string> LstOfFilesToFind = Initialisations.GetListFilePathXml(physicalPathOfJs, structureId, eventId, profilAcheteurId, langCode, isDefault);

            physicalPathOfJs = Initialisations.GetKeyAppSettings("physicalPathOfTranslateXml");
            // string relativeUrlOfJs = "~/Scripts/app/";
            foreach (var item in LstOfFilesToFind)
            {
                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (System.IO.File.Exists(item))
                {
                    if (isDefault)
                    {
                        return physicalPathOfJs.Replace("[idstructureSur4zeros]", "DEFAULT") + "/" + Path.GetFileName(item);
                    }
                    else
                    {
                        return physicalPathOfJs.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId)) + "/" + Path.GetFileName(item);
                    }

                }

            }
            //Todo vérifier les chemins
            // string url = "/Scripts/app/" + structureId.ToString("0000") + "/" + fileName + ".js";

            return urlXmlFile;
        }


        public static string LoadVisualBannerTemplate(int structureId, int eventId, int profilAcheteurId, string langCode, bool isAMobile, myDictionary mySSC, bool useDefaultIfNotExists)
        {

            string physicalPathOfBannerTemplate = Initialisations.GetKeyAppSettings("physicalPathOfBannerTemplate");
            physicalPathOfBannerTemplate = physicalPathOfBannerTemplate.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));

            string physicalPathOfImage = Initialisations.GetKeyAppSettings("physicalPathOfImages");
            physicalPathOfImage = physicalPathOfImage.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));


            string relativePathImagesOfSite = Initialisations.GetKeyAppSettings("relativeFilesPathOfSite");
            relativePathImagesOfSite = relativePathImagesOfSite.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));



            string logoName = string.Empty;
            if (mySSC.Contains("PARAMBANNERNAME") && mySSC["PARAMBANNERNAME"] != null && mySSC["PARAMBANNERNAME"] != "")
            {
                logoName = mySSC["PARAMBANNERNAME"].ToString();
            }


            List<string> lstExtensionsLogo = new List<string>();

            if (mySSC.Contains("PARAMEXTENSIONBANNER") && mySSC["PARAMEXTENSIONBANNER"] != null && mySSC["PARAMEXTENSIONBANNER"] != "")
            {
                lstExtensionsLogo.AddRange(mySSC["PARAMEXTENSIONBANNER"].Split(',').ToList());

                //logger.Debug("lstExtensionsLogo " + string.Join(",", lstExtensionsLogo));

            }



            string extensionfound = string.Empty;
            foreach (var itemExt in lstExtensionsLogo)
            {
                if (System.IO.File.Exists(physicalPathOfImage + logoName + itemExt))
                {
                    extensionfound = itemExt;
                    break;
                }
            }

            //logger.Debug("extensionfound " + extensionfound);


            // bool hasFindUrl = false;
            // List<string> LstOfFilesToFind = Initialisations.GetListFilePathBanner(physicalPathOfImage, logoName, extensionfound, structureId, eventId, profilAcheteurId, langCode, true);
            List<string> LstOfFilesToFind = Initialisations.GetListFilePathBanner(physicalPathOfImage, logoName, extensionfound, structureId, eventId, profilAcheteurId, langCode, useDefaultIfNotExists);

            string urlFilesPath = Initialisations.GetKeyAppSettings("relativeFilesPathOfSite");
            urlFilesPath = urlFilesPath.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));

            foreach (var item in LstOfFilesToFind)
            {
                if (System.IO.File.Exists(item))
                {
                    urlFilesPath = relativePathImagesOfSite + Path.GetFileName(item);
                }
            }


            List<string> LstOfFilesToFindTemplate = Initialisations.GetListFilePath(physicalPathOfBannerTemplate, structureId, eventId, profilAcheteurId, langCode, useDefaultIfNotExists);


            //logger.Debug("LstOfFilesToFindTemplate " + string.Join(",", LstOfFilesToFindTemplate));

            foreach (var item in LstOfFilesToFindTemplate)
            {
                if (System.IO.File.Exists(item))
                {

                    string contentTemplate = Initialisations.ReadTemplate(item);
                    contentTemplate = contentTemplate.Replace("[BannerImage]", urlFilesPath);
                    contentTemplate = contentTemplate.Replace("[StructureId]", structureId.ToString("0000"));
                    contentTemplate = contentTemplate.Replace("[LangCode]", langCode.Split('.')[1]);
                    //return urlFilesPath + Path.GetFileName(item);
                    return contentTemplate;
                }
            }
            return "";

        }
        public static string LoadVisualBannerFiles(int structureId, int eventId, int profilAcheteurId, string langCode, bool isAMobile, myDictionary mySSC)
        {

            string physicalPathOfImage = Initialisations.GetKeyAppSettings("physicalPathOfImages");
            physicalPathOfImage = physicalPathOfImage.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));



            string logoName = string.Empty;
            if (mySSC.Contains("PARAMBANNERNAME") && mySSC["PARAMBANNERNAME"] != null && mySSC["PARAMBANNERNAME"] != "")
            {
                logoName = mySSC["PARAMBANNERNAME"].ToString();
            }


            List<string> lstExtensionsLogo = new List<string>();

            if (mySSC.Contains("PARAMEXTENSIONBANNER") && mySSC["PARAMEXTENSIONBANNER"] != null && mySSC["PARAMEXTENSIONBANNER"] != "")
            {
                lstExtensionsLogo.AddRange(mySSC["PARAMEXTENSIONBANNER"].Split(',').ToList());
            }

            string extensionfound = string.Empty;
            foreach (var itemExt in lstExtensionsLogo)
            {
                if (System.IO.File.Exists(physicalPathOfImage + logoName + itemExt))
                {
                    extensionfound = itemExt;
                    break;
                }
            }

            // bool hasFindUrl = false;
            List<string> LstOfFilesToFind = Initialisations.GetListFilePathBanner(physicalPathOfImage, logoName, extensionfound, structureId, eventId, profilAcheteurId, langCode, true);

            string urlFilesPath = Initialisations.GetKeyAppSettings("relativeFilesPathOfSite");
            urlFilesPath = urlFilesPath.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));

            foreach (var item in LstOfFilesToFind)
            {
                if (System.IO.File.Exists(item))
                {
                    return urlFilesPath + Path.GetFileName(item);
                }
            }

            return "";

        }




        /// <summary>
        /// Charge les options spécifique du client définit dans un fichier JSON
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="profilAcheteurId"></param>
        /// <param name="langCode"></param>
        /// <param name="fileName"></param>
        /// <param name="isDefault"></param>
        /// <returns></returns>
        public static JObject LoadSettingsOfCustomer(int structureId, int eventId, int profilAcheteurId, string langCode, bool isDefault)
        {
            string urlJsonFile = string.Empty;
            string physicalPathOfJson = Initialisations.GetKeyAppSettings("physicalPathOfSettingsJSON");

            physicalPathOfJson = physicalPathOfJson.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));
            // physicalPathOfJson = physicalPathOfJson + fileName + ".json";

            List<string> LstOfFilesToFind = Initialisations.GetListFilePathXml(physicalPathOfJson, structureId, eventId, profilAcheteurId, langCode, isDefault);


            // string relativeUrlOfJs = "~/Scripts/app/";
            foreach (var item in LstOfFilesToFind)
            {
                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (System.IO.File.Exists(item))
                {
                    using (StreamReader file = new StreamReader(item))
                    using (JsonTextReader reader = new JsonTextReader(file))
                    {
                        JObject o2 = (JObject)JToken.ReadFrom(reader);
                        return o2;
                    }
                }

            }
            return new JObject();
        }

        protected string GetLiteralCommentaireEnclosed(string pageName, string pageLieuOfCommentaire, int idStructure, int idEvent, string langCode)
        {
            return "<div id='" + pageName + pageLieuOfCommentaire + "' class='LitForComment" + pageName + pageLieuOfCommentaire + "'>" + GetLiteralCommentaire(pageName, pageLieuOfCommentaire, idStructure, idEvent, langCode) + "</div>";
        }



        private string GetFileCommentPath(string pageName, string pageLieuOfCommentaire, int idStructure, int idEvent, string langCode)
        {
            string fileCommentaire = Initialisations.GetKeyAppSettings("FilesCommentaire" + pageLieuOfCommentaire);
            fileCommentaire = fileCommentaire.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", idStructure));
            fileCommentaire = fileCommentaire.Replace("[page]", pageName);

            //logger.Info("FileCommentaire " + FileCommentaire);

            var LstOfFilesToFind = Initialisations.GetListFilePath(fileCommentaire, idStructure, idEvent, 0, "." + langCode, false);

            // logger.Info("avant  GetUrlByLang" + PhysicalPath + " " + idEvent);

            //a remettre après prod 0537
            //  PhysicalPath = MultiLangue.GetUrlByLang(PhysicalPath, idEvent);
            //logger.Info("PhysicalPath " + PhysicalPath);
            //logger.Info("après  GetUrlByLang" + PhysicalPath + " " + idEvent);
            //  GestionTrace.WriteLog(idStructure, "GetFileCommentPath( pageName " + pageName + " pageLieuOfCommentaire " + pageLieuOfCommentaire + " idStructure " + idStructure);


            foreach (var item in LstOfFilesToFind)
            {
                // GestionTrace.WriteLog(idStructure, "Is exist " + item);

                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (System.IO.File.Exists(item))
                {

                    return item;
                }

            }

            return "";
        }

        protected string GetLiteralCommentaire(string pageName, string pageLieuOfCommentaire, int idStructure, int idEvent, string langCode)
        {
            try
            {
                string fileCommentPath = GetFileCommentPath(pageName, pageLieuOfCommentaire, idStructure, idEvent, langCode);


                if (System.IO.File.Exists(fileCommentPath))
                {
                    System.IO.StreamReader streamRead = new System.IO.StreamReader(fileCommentPath, System.Text.Encoding.UTF8);
                    System.Text.Encoding encoding = streamRead.CurrentEncoding;
                    string line = streamRead.ReadToEnd();

                    if (Session["SVarLangue"] != null)
                        line = line.Replace("[LangueId]", Session["SVarLangue"].ToString());
                    else
                        line = line.Replace("[LangueId]", Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName);


                    streamRead.Close();

                    return Server.HtmlDecode(line);
                }
                else
                    return "";
            }
            catch
            {
                return "";
            }
        }


        public static string GetFooterTemplate(int structureId, int eventId, int profilAcheteurId, string langCode, bool useDefaultIfNotExists)
        {

            string physicalPathOffooterTemplate = Initialisations.GetKeyAppSettings("physicalPathOfFooterTemplate");
            physicalPathOffooterTemplate = physicalPathOffooterTemplate.Replace("[idstructureSur4zeros]", String.Format("{0,4:0000}", structureId));


            List<string> LstOfFilesToFindTemplate = Initialisations.GetListFileWithDefaultPath(physicalPathOffooterTemplate, structureId, eventId, profilAcheteurId, langCode, useDefaultIfNotExists);

            //logger.Debug("LstOfFilesToFindTemplate " + string.Join(",", LstOfFilesToFindTemplate));

            foreach (var item in LstOfFilesToFindTemplate)
            {
                if (System.IO.File.Exists(item))
                {

                    string contentTemplate = Initialisations.ReadTemplate(item);
                    contentTemplate = contentTemplate.Replace("[StructureId]", structureId.ToString("0000"));
                    contentTemplate = contentTemplate.Replace("[LangCode]", langCode);
                    //return urlFilesPath + Path.GetFileName(item);
                    return contentTemplate;
                }
            }
            return "";

        }




        protected string GetHeadCommentaire(string pageLieuOfCommentaire, int idStructure, int idEvent, string langCode)
        {
            try
            {
                string fileCommentPath = GetFileCommentPath("", pageLieuOfCommentaire, idStructure, idEvent, langCode);

                if (System.IO.File.Exists(fileCommentPath))
                {

                    System.IO.StreamReader streamRead = new System.IO.StreamReader(fileCommentPath, System.Text.Encoding.UTF8);
                    System.Text.Encoding encoding = streamRead.CurrentEncoding;
                    string line = streamRead.ReadToEnd();

                    if (Session["SVarLangue"] != null)
                        line = line.Replace("[LangueId]", Session["SVarLangue"].ToString());
                    else
                        line = line.Replace("[LangueId]", Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName);


                    streamRead.Close();

                    return Server.HtmlDecode(line);
                }
                else
                    return "";
            }
            catch
            {
                return "";
            }
        }


        public int SetViewBagStructureId()
        {

            int structureId = 0;
            /*if (Session["structureId"] != null)
            {
                structureId = int.Parse(Session["structureId"].ToString());
            }*/



            if (Session["structureId"] != null)// && ViewBag.structureId == null)
            {
                structureId = int.Parse(Session["structureId"].ToString());
                // structureId = ViewBag.structureId;
                ViewBag.structureId = structureId;
            }
            else
            {
                if (Session["structureId"] == null)
                {
                    ViewBag.errorCode = 1;
                    ViewBag.errorMessage = "Session lost";
                }
            }

            return structureId;
        }

        public int SetViewBagCurrentUserId()
        {
            int currUserId = 0;
            if (Session["currIdUser"] != null)
                currUserId = int.Parse(Session["currIdUser"].ToString());

            ViewBag.WebUserId = currUserId;
            return currUserId;
        }

        public int SetViewBagIdentityId()
        {
            int identiteId = 0;
            if (Session["identiteId"] != null)
                identiteId = int.Parse(Session["identiteId"].ToString());
            ViewBag.IdentityId = identiteId;

            return identiteId;
        }
        protected override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            if (Session["YesICanContinue"] != null && (string)Session["YesICanContinue"] == "true")
            {
                ViewBag.version = System.Configuration.ConfigurationManager.AppSettings["Version"];
                ViewBag.typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];
                /* int identiteId = 0;
                 if (Session["identiteId"] != null)
                     identiteId = int.Parse(Session["identiteId"].ToString());

                 int currUserId = 0;
                 if (Session["currIdUser"] != null)
                     currUserId = int.Parse(Session["currIdUser"].ToString());

                 int structureId = 0;
                 if (Session["structureId"] != null)
                 {
                     structureId = int.Parse(Session["structureId"].ToString());
                 }*/


                //ViewBag.IdentityId = identiteId;
                //ViewBag.WebUserId = currUserId;
                // ViewBag.structureId = structureId;

                int identiteId = SetViewBagIdentityId();
                int currUserId = SetViewBagCurrentUserId();
                int structureId = SetViewBagStructureId();

                //Sha1 sha = new Sha1(identiteId + "/" + currUserId);
                // @ViewBag.HashKey = sha.getSha1();

                string cryptoKey = Initialisations.GetKeyAppSettings("CryptoKey");

                App_Code.crypto.Sha1 sha = new App_Code.crypto.Sha1(String.Format("{0,4:0000}", structureId) + "|" + identiteId + cryptoKey);
                ViewBag.HashKey = sha.getSha1();


                //var IdentityCurrent = @Html.Raw(Json.Encode(@ViewBag.IdentityId));
                //var hkCurrent = @Html.Raw(Json.Encode(@ViewBag.HashKey));


                base.OnActionExecuting(filterContext);
            }
        }



        //Vérifie si l'utilisateur connecté à un panier existant
        public BasketEntity HasBasketForIdentity(int structureId)
        {
            mlogger log = new mlogger();

            int identiteId = 0;
            if (Session["identiteId"] != null)
                identiteId = int.Parse(Session["identiteId"].ToString());

            int currUserId = 0;
            if (Session["currIdUser"] != null)
                currUserId = int.Parse(Session["currIdUser"].ToString());


            try
            {
                wcf_Themis.Iwcf_wsThemisClient wcf = new wcf_Themis.Iwcf_wsThemisClient();
                return wcf.GetCurrentBasketAbonnement(structureId, identiteId, currUserId);

            }
            catch (Exception ex)
            {
                logger.Error("error HasBasketForIdentity  : " + ex.Message + " " + ex.StackTrace);
                GestionTrace.WriteLogError(structureId, "error HasBasketForIdentity : " + ex.Message);
                throw new Exception("dans HasBasketForIdentity: " + ex.Message);
            }

            //  return BasketsManager.GetBasket(typeRun, structureId, identiteId, currUserId);
        }

        public CacheDependency GetUpdateCache(int structureId)
        {

            if (Initialisations.GetKeyAppSettings("UpdateCache") != null)
            {
                // RodWebShop.App_Code.GestionTrace gt = new RodWebShop.App_Code.GestionTrace();

                string fileName = Initialisations.GetKeyAppSettings("UpdateCache").ToString();
                fileName = fileName.Replace("[idstructure]", structureId.ToString("0000"));

                CacheDependency dep = null;
                if (System.IO.File.Exists(fileName))
                {
                    dep = new CacheDependency(fileName);
                }
                return dep;
            }
            else
            {

                Exception ex = new Exception("dans GetUpdateCache: UpdateCache null dans Web.config");

                logger.Error("error GetUpdateCache  : " + ex.Message + " " + ex.StackTrace);
                GestionTrace.WriteLogError(structureId, "error GetUpdateCache : " + ex.Message);

                throw ex;
            }
        }


    }
}