﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace aboV2.App_Code.crypto
{
    public class Sha1
    {
        private String source = "";

        public Sha1(String source) { this.source = source; }

        public String getSha1() { return crypte(""); }

        public String getSha1(String prefixe) { return crypte(prefixe); }

        private String crypte(String prefixe)
        {
            if (this.source.CompareTo("") != 0)
            {
                if (prefixe.CompareTo("") == 0)
                {
                    return BitConverter.ToString(new SHA1CryptoServiceProvider().ComputeHash(Encoding.Default.GetBytes(this.source))).Replace("-", "");
                }
                else
                    return prefixe + BitConverter.ToString(new SHA1CryptoServiceProvider().ComputeHash(Encoding.Default.GetBytes(this.source))).Replace("-", "");
            }
            else
                return "";
        }

        //TO decrypt the local hash algorithm for the passwords to be saved in windows
        //Regisrty by rodrigue serveur appli...!!
        public String DoDecryptString(String strIn)
        {

            String strOut = "", strTmp = "";
            Byte chr0, chr1, chr2;
            Byte ch; // '€' 
            int i;

            strIn.Trim();
            Encoding enCod = Encoding.GetEncoding(0);
            Byte[] bytes = enCod.GetBytes(strIn);

            int nL = bytes.GetLength(0);

            if (nL > 0 && (bytes[0] == '|'))
            {

                for (i = 1; i < nL; i += 3)
                {
                    ch = (Byte)bytes[i];
                    chr0 = (Byte)(ch - 48);
                    ch = (Byte)bytes[i + 1];
                    chr1 = (Byte)(ch - 48 - chr0);
                    ch = (Byte)bytes[i + 2];
                    chr2 = (Byte)(ch - 48 - chr0);

                    if (chr2 < 0)
                        chr2 = (Byte)10;
                    strTmp = String.Format("{0,2:D2}{1,2:D2}", chr2, chr1);
                    ch = (Byte)(Convert.ToInt16(strTmp) / 10);
                    strOut += (Char)ch;
                }

            }

            else
            {
                strOut = strIn;
            }
            return strOut;
        }
    }
}