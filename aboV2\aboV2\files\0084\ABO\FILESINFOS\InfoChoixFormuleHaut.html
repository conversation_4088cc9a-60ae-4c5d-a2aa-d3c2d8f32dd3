<p><strong><u>Si vous souhaitez prendre plusieurs abonnements:</u></strong></p>

<p>- <b>avec un choix d'abonnements identiques</b> (m&ecirc;mes formules, m&ecirc;mes dates): s&eacute;lectionnez la formule et la quantit&eacute; d'abonnements,<br />- <b>avec un choix d'abonnement diff&eacute;rents</b> (formules diff&eacute;rentes et/ou dates diff&eacute;rentes): s&eacute;lectionnez et remplissez les formules les unes apr&egrave;s les autres en cliquant sur "Abonnement (s)&nbsp;suppl&eacute;mentaire (s)" &agrave; la fin de l'&eacute;tape 2.</p>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-13']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    </script>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-2']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();      &lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html></script>