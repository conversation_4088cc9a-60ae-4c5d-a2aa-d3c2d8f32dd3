﻿using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Web;
using utilitaires2010;

namespace aboV2.App_Code
{
    public class MultiLangue
    {
        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static string GetUrlByLang(string OriginalUrl, int idEvent)
        {
            int ProfilAcheteur = 0;
            string strSuffixeLangue = string.Empty;
            int IdStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            if (System.Web.HttpContext.Current.Session["ProfilAcheteurId"] != null)
                ProfilAcheteur = int.Parse(System.Web.HttpContext.Current.Session["ProfilAcheteurId"].ToString());


            if (System.Web.HttpContext.Current.Session["SVarLangue"] != null)
            {
                strSuffixeLangue = "." + System.Web.HttpContext.Current.Session["SVarLangue"].ToString();
            }
            else
                strSuffixeLangue = "." + Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName;


            List<string> LstOfFilesToFind = Initialisations.GetListFilePath(OriginalUrl, IdStructure, idEvent, ProfilAcheteur, strSuffixeLangue, true);

            foreach (var item in LstOfFilesToFind)
            {
                //FileToFind = Path.GetDirectoryName(FileToFind_temp) + "\\" + item;
                if (File.Exists(item))
                {
                    logger.Debug(item + " found");
                    return item;
                }
            }

            return OriginalUrl;

        }
    }
}