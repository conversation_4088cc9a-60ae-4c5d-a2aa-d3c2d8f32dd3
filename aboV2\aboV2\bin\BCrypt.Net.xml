<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BCrypt.Net</name>
    </assembly>
    <members>
        <member name="T:BCrypt.Net.SaltParseException">
            <summary>Exception for signalling parse errors. </summary>
        </member>
        <member name="M:BCrypt.Net.SaltParseException.#ctor">
            <summary>Default constructor. </summary>
        </member>
        <member name="M:BCrypt.Net.SaltParseException.#ctor(System.String)">
            <summary>Initializes a new instance of <see cref="T:BCrypt.Net.SaltParseException"/>.</summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:BCrypt.Net.SaltParseException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of <see cref="T:BCrypt.Net.SaltParseException"/>.</summary>
            <param name="message">       The message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:BCrypt.Net.SaltParseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>Initializes a new instance of <see cref="T:BCrypt.Net.SaltParseException"/>.</summary>
            <param name="info">   The information.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:BCrypt.Net.BCrypt">
            <summary>BCrypt implementation.</summary>
            <remarks>
             <para>
                   BCrypt implements OpenBSD-style Blowfish password hashing using the scheme described in
                   <a href="http://www.usenix.org/event/usenix99/provos/provos_html/index.html">"A Future-
                   Adaptable Password Scheme"</a> by Niels Provos and David Mazieres.
             </para>
             <para>
                   This password hashing system tries to thwart off-line password cracking using a
                   computationally-intensive hashing algorithm, based on Bruce Schneier's Blowfish cipher.
                   The work factor of the algorithm is parameterised, so it can be increased as computers
                   get faster.
             </para>
             <para>
                   Usage is really simple. To hash a password for the first time, call the <see cref="M:BCrypt.Net.BCrypt.HashPassword(System.String)"/> method with a random salt, like this:
             </para>
             <code>string pw_hash = BCrypt.HashPassword(plain_password);</code>
             <para>
                   To check whether a plaintext password matches one that has been hashed previously,
                   use the <see cref="M:BCrypt.Net.BCrypt.Verify(System.String,System.String)"/> method:
             </para>
             <code>
                if (BCrypt.Verify(candidate_password, stored_hash))
                    Console.WriteLine("It matches");
                else
                    Console.WriteLine("It does not match");
              </code>
              <para>
                    The <see cref="M:BCrypt.Net.BCrypt.GenerateSalt"/> method takes an optional parameter (workFactor) that
                    determines the computational complexity of the hashing:
              </para>
              <code>
                string strong_salt = BCrypt.GenerateSalt(10);
                string stronger_salt = BCrypt.GenerateSalt(12);
              </code>
              <para>
                    The amount of work increases exponentially (2^workFactor), so each increment is twice
                    as much work. The default workFactor is 10, and the valid range is 4 to 31.
              </para>
            </remarks>
        </member>
        <member name="M:BCrypt.Net.BCrypt.HashString(System.String)">
            <summary>
             Hash a string using the OpenBSD bcrypt scheme and a salt generated by <see cref="M:BCrypt.Net.BCrypt.GenerateSalt"/>.
            </summary>
            <remarks>Just an alias for HashPassword.</remarks>
            <param name="source">The string to hash.</param>
            <returns>The hashed string.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.HashString(System.String,System.Int32)">
            <summary>
             Hash a string using the OpenBSD bcrypt scheme and a salt generated by <see cref="M:BCrypt.Net.BCrypt.GenerateSalt"/>.
            </summary>
            <remarks>Just an alias for HashPassword.</remarks>
            <param name="source">  The string to hash.</param>
            <param name="workFactor">The log2 of the number of rounds of hashing to apply - the work
                                     factor therefore increases as 2^workFactor.</param>
            <returns>The hashed string.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.HashPassword(System.String)">
            <summary>
             Hash a password using the OpenBSD bcrypt scheme and a salt generated by <see cref="M:BCrypt.Net.BCrypt.GenerateSalt"/>.
            </summary>
            <param name="input">The password to hash.</param>
            <returns>The hashed password.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.HashPassword(System.String,System.Int32)">
            <summary>
             Hash a password using the OpenBSD bcrypt scheme and a salt generated by <see cref="M:BCrypt.Net.BCrypt.GenerateSalt(System.Int32)"/> using the given <paramref name="workFactor"/>.
            </summary>
            <param name="input">     The password to hash.</param>
            <param name="workFactor">The log2 of the number of rounds of hashing to apply - the work
                                     factor therefore increases as 2^workFactor.</param>
            <returns>The hashed password.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.HashPassword(System.String,System.String)">
            <summary>Hash a password using the OpenBSD bcrypt scheme.</summary>
            <exception cref="T:System.ArgumentException">Thrown when one or more arguments have unsupported or
                                                illegal values.</exception>
            <param name="input">The password to hash.</param>
            <param name="salt">    the salt to hash with (perhaps generated using BCrypt.gensalt).</param>
            <returns>The hashed password</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.GenerateSalt(System.Int32)">
            <summary>
             Generate a salt for use with the <see cref="M:BCrypt.Net.BCrypt.HashPassword(System.String,System.String)"/> method.
            </summary>
            <param name="workFactor">The log2 of the number of rounds of hashing to apply - the work
                                     factor therefore increases as 2**workFactor.</param>
            <returns>A base64 encoded salt value.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.GenerateSalt">
            <summary>
             Generate a salt for use with the <see cref="M:BCrypt.Net.BCrypt.HashPassword(System.String,System.String)"/> method
             selecting a reasonable default for the number of hashing rounds to apply.
            </summary>
            <returns>A base64 encoded salt value.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.Verify(System.String,System.String)">
            <summary>
             Verifies that the hash of the given <paramref name="text"/> matches the provided
             <paramref name="hash"/>
            </summary>
            <param name="text">The text to verify.</param>
            <param name="hash"> The previously-hashed password.</param>
            <returns>true if the passwords match, false otherwise.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.EncodeBase64(System.Byte[],System.Int32)">
            <summary>
             Encode a byte array using bcrypt's slightly-modified base64 encoding scheme. Note that this
             is *not* compatible with the standard MIME-base64 encoding.
            </summary>
            <exception cref="T:System.ArgumentException">Thrown when one or more arguments have unsupported or
                                                illegal values.</exception>
            <param name="byteArray">The byte array to encode.</param>
            <param name="length">   The number of bytes to encode.</param>
            <returns>Base64-encoded string.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.DecodeBase64(System.String,System.Int32)">
            <summary>
             Decode a string encoded using bcrypt's base64 scheme to a byte array. Note that this is *not*
             compatible with the standard MIME-base64 encoding.
            </summary>
            <exception cref="T:System.ArgumentException">Thrown when one or more arguments have unsupported or
                                                illegal values.</exception>
            <param name="encodedstring">The string to decode.</param>
            <param name="maximumBytes"> The maximum bytes to decode.</param>
            <returns>The decoded byte array.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.Char64(System.Char)">
            <summary>
             Look up the 3 bits base64-encoded by the specified character, range-checking against
             conversion table.
            </summary>
            <param name="character">The base64-encoded value.</param>
            <returns>The decoded value of x.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.Encipher(System.UInt32[],System.Int32)">
            <summary>Blowfish encipher a single 64-bit block encoded as two 32-bit halves.</summary>
            <param name="blockArray">An array containing the two 32-bit half blocks.</param>
            <param name="offset">    The position in the array of the blocks.</param>
        </member>
        <member name="M:BCrypt.Net.BCrypt.StreamToWord(System.Byte[],System.Int32@)">
            <summary>Cycically extract a word of key material.</summary>
            <param name="data">The string to extract the data from.</param>
            <param name="offset"> [in,out] The current offset.</param>
            <returns>The next word of material from data.</returns>
        </member>
        <member name="M:BCrypt.Net.BCrypt.InitializeKey">
            <summary>Initializes the Blowfish key schedule.</summary>
        </member>
        <member name="M:BCrypt.Net.BCrypt.Key(System.Byte[])">
            <summary>Key the Blowfish cipher.</summary>
            <param name="keyBytes">The key byte array.</param>
        </member>
        <member name="M:BCrypt.Net.BCrypt.EKSKey(System.Byte[],System.Byte[])">
            <summary>
             Perform the "enhanced key schedule" step described by Provos and Mazieres in "A Future-
             Adaptable Password Scheme" http://www.openbsd.org/papers/bcrypt-paper.ps.
            </summary>
            <param name="saltBytes"> Salt byte array.</param>
            <param name="inputBytes">Input byte array.</param>
        </member>
        <member name="M:BCrypt.Net.BCrypt.CryptRaw(System.Byte[],System.Byte[],System.Int32)">
            <summary>Perform the central hashing step in the bcrypt scheme.</summary>
            <exception cref="T:System.ArgumentException">Thrown when one or more arguments have unsupported or
                                                illegal values.</exception>
            <param name="inputBytes">The input byte array to hash.</param>
            <param name="saltBytes"> The salt byte array to hash with.</param>
            <param name="logRounds"> The binary logarithm of the number of rounds of hashing to apply.</param>
            <returns>A byte array containing the hashed result.</returns>
        </member>
    </members>
</doc>
