<p class="commentaireetape"><font color="#ff0000">ATTENTION !!! L'abonnement en ligne n'est pas ouvert aux&nbsp;personnes &agrave; mobilit&eacute; r&eacute;duite, ainsi qu'aux&nbsp;abonn&eacute;s blagnacais ou appartenant &agrave; une collectivit&eacute;, dans un de ces cas, merci de souscrire votre abonnement au guichet (aux horaires d'ouverture).</font></p>

<p class="commentaireetape"><font color="#ff0000"><u>Le paiement s'effectue par Carte Visa ou Mastercard en euros.La transaction est s&eacute;curis&eacute;e et r&egrave;glera en une seule fois la totalit&eacute; des abonnements.</u></font></p>

<p class="commentaireetape">Nous avons bascul&eacute; dans un <u>nouveau </u><a title="Protocole de communication" href="http://fr.wikipedia.org/wiki/Protocole_de_communication"></a><u> s&eacute;curis&eacute; de </u><a title="Paiement sur Internet" href="http://fr.wikipedia.org/wiki/Paiement_sur_Internet"></a><u>paiement sur Internet, le 3-D Secure</u> afin de limiter les risques de fraude sur Internet, li&eacute;s aux tentatives d&rsquo;usurpation d&rsquo;identit&eacute;. </p>

Une &eacute;tape suppl&eacute;mentaire a lieu au moment du paiement. En plus du num&eacute;ro de carte bancaire, de la date d'expiration de la carte et des trois chiffres du code de s&eacute;curit&eacute; (imprim&eacute;s au dos de la carte), vous devez saisir<u> un mot de passe</u> qui vous sera imm&eacute;diatement communiqu&eacute; sur votre <u>t&eacute;l&eacute;phone portable</u>.<br /></p>

<p class="commentaireetape"><strong>Vous disposez d&eacute;j&agrave; d'un compte client, saisissez votre e-mail et votre mot de passe puis cliquez sur "Connexion"</strong></p>

<p class="commentaireetape"><strong>C'est votre premi&egrave;re commande sur notre site, cliquez sur "Cr&eacute;er un compte".</strong></p>

<p class="commentaireetape"><strong>&nbsp;Vous disposez d&eacute;j&agrave; d'un compte client mais&nbsp;vos donn&eacute;es personnelles sont &agrave; modifier, cliquez sur "Modifier profil".&nbsp; </strong></p>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-13']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    </script>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-2']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    &lt;/script&gt;</body></html></body></html></body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html></body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html></script>