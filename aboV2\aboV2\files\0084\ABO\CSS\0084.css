html {
	margin: 0px; 
	padding: 0px; 
	color: rgb(0, 0, 0); 
	font-family: Arial, Helvetica, sans-serif; 
	font-size: 14px;
	}
	
body {
	/*padding-top: 60px;*/
	padding-bottom: 40px;
	color: #000;
	margin:0px auto;
	font-family: <PERSON><PERSON>, Lucida, sans-serif;
    background: url(/files/0084/abo/images/fond_2015.jpg) repeat-y center top #fff; 
}

#ctl00_ContentPlaceHolder1_divIdentification {display:initial !important}

body.popPlanSalle {
	background:#fff;
}

#lbConfigName{display:none}

.navbar {
	margin-top: 15px;
	min-height: 30px;
	color: #FFF;
	background-color: #4b4b4b;
	filter: alpha(opacity=80);
	opacity: 0.8;
	font-size: 12px;
	padding-top: 7px;
	height: 20px;
	padding-bottom: 7px;
	margin-bottom: 0px;
	border-radius: 0px;
	clear: both;
}
#divSteps {
	color: #FFF;
	background-color:#007F8B;
	font-size: 1.2em;
	text-align:center;
}
#ctl00_TableEtape {
	margin:0px auto;
	width:80%;
}
#ctl00_lblTitreEtape{
color: white;
font-size: 20px;
}

.navbar{display:none}

#divLogoClient{
	text-align:center;
	padding:0px 0px;
}

#main-content {
	background-color :#eee;
	padding-top:0px;
	padding-left:15px;
	padding-right:15px;
	padding-bottom:0px;
	-moz-box-shadow: 0 0 10px rgba( 0, 0, 0, 0.2);
	-webkit-box-shadow: 0 0 10px 10px rgba( 0, 0, 0, 0.2);
	box-shadow: 0 0 10px rgba( 0, 0, 0, 0.2);
}


#footer {
	margin-top: 50px;
	color: #000;
	font-weight: bold;
	background-color:#000F1F;
	font-size: 12px;
	padding-top: 7px;
	padding-right: 14px;
	padding-left: 14px;
	padding-bottom: 7px;
	margin-bottom: 0px;
	border-radius: 0px;
	clear: both;
	-moz-box-shadow: 0 0 10px rgba( 0, 0, 0, 0.2);
	-webkit-box-shadow: 0 0 10px 10px rgba( 0, 0, 0, 0.2);
	box-shadow: 0 0 10px rgba( 0, 0, 0, 0.2);
}
#ctl00_hyperlinkrodrigue {
	color:#000;
}

img#ctl00_ImgRodrigue{
	width:70%;
}

img#ctl00_LogoRodrigue {
	display:none;
}

.MenuBottom li {
	display:inline;
	padding-left:10px;
	color:#fff;
}
.MenuBottom a {
	color:#fff;
}

div#divCommentaireEtape, div#divMenu{
	padding-top:20px;
}

div#divTitreEtape{
	background:#092256;
}

ul#ctl00_bullistnavigationnavig.MenuBottom  {
	-webkit-padding-start: 0px;
}

#ctl00_TableEtape .idAttributselected span:after{
	content: "●";
	display:block;
}

/*tr>.produitdescription{display:none}*/
/************/
/*** MENU ***/
/************/
.AspNet-Menu {
	background-color: #E7305E;
	padding: 5px;
	/*margin:15px;*/
	/*padding-top:35px;*/
	color:#fff;
	/*background-image: url('https://www.themisweb.fr/rodwebshop/images/TopPanier.jpg') top no-repeat;*/
	}
.AspNet-Menu-Vertical, h3.head, .AspNet-Menu-NonLink, a.AspNet-Menu-NonLink:link, a.AspNet-Menu-NonLink:hover {
	color :#ffffff;
	/*font-weight:bold;*/
	padding : 7px 0px 4px 0px;
	font-size : 1.05em;
	text-decoration : none;
	/*text-align:center;*/
	margin: 0px;
}
ul.AspNet-Menu {
	width:100%;
}
a.AspNet-Menu-Link {
	color:#FFF;
	/*color:#58585A;*/
	/*list-style-type:circle;*/
	/*font-size:0.98em;*/
	padding-left : 10px;
}
li.AspNet-Menu-WithChildren, li.AspNet-Menu-Leaf {
	width : 100%;
		color:#eee;
}

a.AspNet-Menu-exit {
	margin-top: 20px;
	/*background : #E9D09A;*/
	color:#E9D09A;
}

li.AspNet-Menu-Leaf :hover {
	/*background-color:#58585A;*/
	width : 100%;
}

span#ctl00_lblTitreMenu, span#ctl00_idTitreLogin, span#ctl00_idLogin {
	display:none;
}

span#ctl00_idTitreNom {
	padding-right:10px;
}

.cssmessageerror{
font-weight : bold;
color : red;
padding-left:50px;
}
.btn-warning {
    border:#E7305E !important; 
}
.invisible{display:none}
.useless,
#ctl00_ContentPlaceHolder1_cbIsIdentified,
#ctl00_ContentPlaceHolder1_linknextpage,
#ctl00_ContentPlaceHolder1_tableLogin
{display:none !important}

.btBuy,
.btAction,
#ctl00_ContentPlaceHolder1_linkdisplayLogin{
	display: inline-block;
	-moz-border-radius: 0px;
	border-radius: 2px;
	font-size: 13px;
	padding: 10px 25px 10px 25px;
	text-align: center;
	color: #fff;
	background: #E7305E;
	white-space:nowrap;
	margin:0px;
	font-weight:700;
	font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;
}

.btBuy:hover,	
#ctl00_ContentPlaceHolder1_linkdisplayLogin:hover{
	background:#102B41;
	color: #fff;
}

.btBuyComplet,
.btBuyComplet:hover{
	display: inline-block;
	-moz-border-radius: 0px;
	border-radius: 2px;
	font-size: 1em;
	padding: 5px 25px 5px 25px;
	text-align: center;
	color: #fff;
	background: #e10d3a;
	white-space:nowrap;
	margin:0px;
	font-weight:400;
	font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;
}

/**** END MENU *****/


/***** CHOIX CATEG *****/

#ctl00_ContentPlaceHolder1_btFiltre {
display : none;}
#ctl00_ContentPlaceHolder1_gvListManif {
	width:80%;
    margin-bottom: 20px;
}
.formuledescription {
	font-size:1.2em;
	font-weight:bolder;
}
.directives {
	color:#E7305E;
	font-weight:bold;
	width:80%;
	font-size :18px;
	
}

.directives td a{
	visibility : hidden;
}

.cssTableTest {width:100%}

.cbPaiementXFois{
      color: #12ACC6;
}

tr.csslignetitre{
	background-color : #45B27F;
	color:#fff;
	padding:5px;
}
tr.csslignetitre th{
	padding:5px;
}

tr.csslignesimple td {
	padding:5px;
}

tr.csslignepaire td, tr.cssligneimpaire td {
	padding:5px;
}

select.cssddlManif {
	padding:2px;
	min-width: 200px;
}

.cssligneimpaire
{
	font-size: 15px;
	background-color: #f8f8f8;
}
.csslignenondispo 
{
	font-size: 15px;
	background-color: #ffcc66;
}

#ctl00_ContentPlaceHolder1_tblEntrees  td.maniftitre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.seancetitre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.categtitre, 
 #ctl00_ContentPlaceHolder1_tblEntrees td.dispotitre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.envoititre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.tariftitre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.coefftitre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.montanttitre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.produittitre,
 #ctl00_ContentPlaceHolder1_tblEntrees td.formuletitre, 
 #ctl00_ContentPlaceHolder1_tblEntrees td.placetitre,
  #ctl00_ContentPlaceHolder1_tblEntrees td.titre
{
	background-color: #12ACC6;
	color: white;
	text-align: center;
	font-size: 13px;
	font-weight: bold;
}
a.btSeeMyPlacement.popUpGrand  {
margin-left: 25px;}


    /* positionner le siege */
    .seatimage, .textrang, .poteau
    {
    margin-top:50px;
    margin-left:-100px;  
    }
    .seatimage.perspect
    {
    margin-top:-5px;
    margin-left:-8px;   
    }	
    .seatimage.legend
    {
      margin-top:0px;
      margin-left:0px;       
    }
    .ps.seatimage.free
    {
    cursor: pointer; cursor: hand
    } 
    .poteau {
    background: url(https://www.themisweb.fr/rodwebshoptest//images/DEFAULT/column1515.gif) no-repeat; 
    }
    /* mise en valeur du siege selectionn? */
    .hightlightImg
    {
    background: url(https://www.themisweb.fr/images/DEFAULT/seats/seat_is_here.gif) no-repeat; 
    }
	/* categ par defaut */
		/* libre */	
		.ps.orientS.free 
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatok_s.gif) no-repeat; 
		}
		.ps.orientS.free.categNo1,.seatimage.legend.categNo1
		{
		background: url(https://www.themisweb.fr/images/billes/pastille_orange.gif) no-repeat; 
		}		
		.ps.orientS.free.categNo2,.seatimage.legend.categNo2
		{
		background: url(https://www.themisweb.fr/images/billes/pastille_rose.gif) no-repeat; 
		}
		.ps.orientS.free.categNo3,.seatimage.legend.categNo3
		{
		background: url(https://www.themisweb.fr/images/billes/pastille_marron.gif) no-repeat; 
		}
		.ps.orientS.free.categNo4,.seatimage.legend.categNo4
		{
		background: url(https://www.themisweb.fr/images/billes/pastille_verte.gif) no-repeat; 
		}
		.ps.orientS.free.categNo5,.seatimage.legend.categNo5
		{
		background: url(https://www.themisweb.fr/images/billes/pastille_bleue.gif) no-repeat; 
		}
		.ps.orientS.free.categNo6,.seatimage.legend.categNo6
		{
		background: url(https://www.themisweb.fr/images/billes/pastille_grise.gif) no-repeat; 
		}				
		.ps.orientS.free.categNo7,.seatimage.legend.categNo7
		{
		background: url(https://www.themisweb.fr/images/billes/pastille_cyan.gif) no-repeat; 
		}
		.ps.orientN.free 
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatok_n.gif) no-repeat; 
		}
		.ps.orientO.free 
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatok_o.gif) no-repeat; 
		}
		.ps.orientSE.free 
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatok_se.gif) no-repeat; 
		}
		.ps.orientNE.free 
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatok_ne.gif) no-repeat; 
		}
		.ps.orientNO.free 
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatok_no.gif) no-repeat; 
		}
		.ps.orientSO.free 
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatok_so.gif) no-repeat; 
		}		
		.ps.nofree 
		{
      opacity: 0.5;
      -moz-opacity:0.5;
      filter:alpha(opacity=50);		
  		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatnotfree.gif) no-repeat; 
		}	
	.seatimage.nofree.perspect { display:none;}

		
    .categNo1    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo2    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo3    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo4    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo5    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo6    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo7    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo8    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo9    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre1.gif) no-repeat;  }
    .categNo10    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre10.gif) no-repeat;  }
    .categNo11    { background: url(https://www.themisweb.fr/images/DEFAULT/seats/carre11.gif) no-repeat;  }
		
		.trucquiexistepas.free /* ruse IE6 qui ne comprend pas les doubles classes */
		{
		background: url(https://www.themisweb.fr/images/DEFAULT/seats/seatfree_generic.gif) no-repeat; 
		}	
.pris 
{
background: url(https://www.themisweb.fr/images/DEFAULT/seats/pictoUsers/fleche_rome_anim.gif) no-repeat; }
		
			
/*@media (min-width: 1200px){
	.container {
		width:960px;
	}
}
@media (min-width: 992px) and (max-width: 1199px){
	.container {
		width:980px;
	}
}
@media (max-width: 991px) {*/

	.ui-dialog .ui-dialog-titlebar {
	font-size: 1.1em;
	font-weight: bold;
	padding: 5px;
	color: #fff;
	background: #3A3A3A;
}
.ui-dialog-titlebar-close {
	display:none;
}

.ui-corner-all {
-moz-border-radius: 0px;
-webkit-border-radius: 0px;
}
.ui-dialog .ui-dialog-buttonpane button{
	display: inline-block;
	-moz-border-radius: 0px;
	border-radius: 0px;
	font-size: 12px;
	padding: 5px 25px 5px 25px;
	text-align: center;
	color: #ffffff;
	background: #0678b4;
	white-space:nowrap;
	margin:0px;
	font-weight:400;
	font-family:Arial;
}

