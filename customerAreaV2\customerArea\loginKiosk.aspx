﻿<%@ Page Title="" Language="C#" MasterPageFile="~/pageMasterKiosk.Master" AutoEventWireup="true" CodeBehind="loginKiosk.aspx.cs" Inherits="customerArea.loginKiosk" %>
<%@ Register Src="wctrlLoginConnect.ascx" TagName="wctrlLoginConnect" TagPrefix="uc1" %>
<%@ Register Src="wctrLoginCreation.ascx" TagName="wctrLoginCreation" TagPrefix="uc2" %>
<%@ Register Src="wctrLoginCreationLight.ascx" TagName="wctrLoginCreationLight" TagPrefix="ucLight" %>
<%@ Register Src="wctrlGuestLogin.ascx" TagName="wctrlGuestLogin" TagPrefix="ucGuestLogin" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ctPlaceHold" runat="server">
    <!-- Nav tabs -->
    <ul class="nav nav-tabs d-none" id="myTab" role="tablist">
        <li class="nav-item">
            <a class="nav-link active show" href="#loginTab" role="tab" data-toggle="tab" runat="server" id="aDejaUnCompte" clientidmode="Static">J'ai déjà un compte</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="#forgottenPwTab" role="tab" data-toggle="tab" runat="server" id="aOublierCompte" clientidmode="Static">Mot de passe oublié</a>
        </li>
    </ul>
    <div class="tab-content">
        <div class="tab-pane fade in active show " id="loginTab">
            <div class="row">
                <!-- commentaires haut -->
                <div id="commentsPageHaut">
                    <asp:Literal ID="litForCommentaireHaut" runat="server"></asp:Literal>
                </div>
                <!-- LOGIN FORM -->
                <div  class="col-12 offset-lg-2 col-lg-4">
                    <div id="connectForm">

                    <h2 data-trad="title_login">Login</h2>
                    <uc1:wctrlLoginConnect ID="WctrlLoginConnect1" runat="server" />
                    </div>
                </div>
                <!-- CREATE LOGIN FORM -->
                <div class="col-12 col-lg-4">
                    <div id="createaccountForm">
                        <h2 data-trad="msg_dont_have_account">Do not have an account yet ?</h2>
                    <uc2:wctrLoginCreation ID="WctrLoginCreation1" runat="server" />
                    <ucLight:wctrLoginCreationLight ID="WctrLoginCreationLight" runat="server" />
                    </div>
                    
                </div>
            </div>
            <!-- GUEST LOGIN -->
            <div class="row" id="divguestloginWrapper" runat="server">
                <div class="col-12 offset-lg-2 col-lg-8">
                    <hr>
                    <div id="loginNoAccount">
                        <h2 class="text-center" data-trad="title_guest_login">Achat sans compte</h2>
                        <ucGuestLogin:wctrlGuestLogin ID="wctrlGuestLogin" runat="server" />
                    </div>
                    
                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="forgottenPwTab">
            <div class="row">
                <div class="col-12 offset-lg-2 col-lg-8">
                    <div id="divforgotten">
                    <h2 class="text-center" data-trad="title_forgot_password">Forgot your password?</h2>
                    <div id="explicationForgotPassword" runat="server" data-trad="msg_entente_forgot_password">
                        Enter your email address with which you registered. We will send you an email with a link to reset your password.
                    </div>
                    <div id="forgottenForm" role="form">
                        <div class="col-12">
                            <div class="row form-group">
                                <label class="sr-only">E-mail</label>
                                <input type="email" name="forgottenemail" class="form-control" placeholder="E-mail" data-tradplaceholder="placeholder_email" required>
                                <div class="invalid-feedback" data-trad="msg_error_email">Please enter a valid email</div>
                            </div>
                            <div class="row form-group">
                                <button class="btn btn-primary btn-block" data-trad="btn_send" id="btnSendForgetPass">Send</button>
                            </div>
                        </div>
                        <hr>
                        <p class="text-center font-weight-bold"><span data-trad="msg_remember_my_account">Finally, I remember it ...</span> <a href="#" data-tabtarget="#loginTab" class="" id="loginAccount" data-trad="btn_login">Connect</a></p>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- commentaires bas -->
    <div id="divCommentaireBas" class="small col-12 offset-lg-2 col-lg-8 mt-3">
        <asp:Literal ID="litForCommentaireBas" runat="server"></asp:Literal>
    </div>


    
    <!-- FACEBOOK *************** -->
    <script>
    window.fbAsyncInit = function() {
        FB.init({
            appId: '****************',
            cookie: true, // enable cookies to allow the server to access 
            // the session
            autoLogAppEvents: true,
            xfbml: true, // parse social plugins on this page
            version: 'v3.1' // use graph api version 2.8
        });
        FB.AppEvents.logPageView();
    };


    var urlJsLang = "//connect.facebook.net/en_US/sdk.js";
    switch ($('html').attr('lang')) {
        case "de":
            urlJsLang = "//connect.facebook.net/de_DE/sdk.js";
            break;

        case "fr":
            urlJsLang = "//connect.facebook.net/fr_FR/sdk.js";
            break;

        default:
            urlJsLang = "//connect.facebook.net/en_US/sdk.js";
            break;
    }

    // Load the SDK asynchronously
    (function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s);
        js.id = id;

        //js.src = "//connect.facebook.net/en_US/sdk.js";
        //js.src = "//connect.facebook.net/de_DE/sdk.js";
        js.src = urlJsLang;
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));




    function LoginAPI() {
        FB.login(function(response) {

            if (response.authResponse) {
                console.log('Welcome!  Fetching your information.... ');
                //console.log(response); // dump complete info
                access_token = response.authResponse.accessToken; //get access token
                user_id = response.authResponse.userID; //get FB UID

                FB.api('/me', { fields: 'email' }, function(response) {
                    var email = response.email;
                    var name = response.name;

                    // window.location = 'http://localhost:61720/login.aspx?IdStructure=0994';
                    // window.location = 'http://localhost:12962/Account/FacebookLogin/' + email + '/' + name;
                    // used in my mvc3 controller for //AuthenticationFormsAuthentication.SetAuthCookie(email, true);     

                    //call webservice
                    ConnectUserFB(user_id, access_token, email);
                });

            } else {
                //user hit cancel button
                console.log('User cancelled login or did not fully authorize.');

            }
        }, {
            scope: 'email'
        });

    }
    
    </script>
    <!-- END FACEBOOK *************** -->

</asp:Content>