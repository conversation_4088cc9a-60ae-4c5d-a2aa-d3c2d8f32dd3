<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Security.Twitter</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Owin.Security.Twitter.Messages.AccessToken">
            <summary>
            Twitter access token
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.Messages.RequestToken">
            <summary>
            Twitter request token
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Messages.RequestToken.Token">
            <summary>
            Gets or sets the Twitter token
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Messages.RequestToken.TokenSecret">
            <summary>
            Gets or sets the Twitter token secret
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Messages.RequestToken.Properties">
            <summary>
            Gets or sets a property bag for common authentication properties
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Messages.AccessToken.UserId">
            <summary>
            Gets or sets the Twitter User ID
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Messages.AccessToken.ScreenName">
            <summary>
            Gets or sets the Twitter screen name
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer">
            <summary>
            Serializes and deserializes Twitter request and access tokens so that they can be used by other application components.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Serialize(Microsoft.Owin.Security.Twitter.Messages.RequestToken)">
            <summary>
            Serialize a request token
            </summary>
            <param name="model">The token to serialize</param>
            <returns>A byte array containing the serialized token</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Deserialize(System.Byte[])">
            <summary>
            Deserializes a request token
            </summary>
            <param name="data">A byte array containing the serialized token</param>
            <returns>The Twitter request token</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Write(System.IO.BinaryWriter,Microsoft.Owin.Security.Twitter.Messages.RequestToken)">
            <summary>
            Writes a Twitter request token as a series of bytes. Used by the <see cref="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Serialize(Microsoft.Owin.Security.Twitter.Messages.RequestToken)"/> method.
            </summary>
            <param name="writer">The writer to use in writing the token</param>
            <param name="token">The token to write</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Read(System.IO.BinaryReader)">
            <summary>
            Reads a Twitter request token from a series of bytes. Used by the <see cref="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Deserialize(System.Byte[])"/> method.
            </summary>
            <param name="reader">The reader to use in reading the token bytes</param>
            <returns>The token</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.Messages.Serializers">
            <summary>
            Provides access to a request token serializer
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Messages.Serializers.RequestToken">
            <summary>
            Gets or sets a statically-avaliable serializer object. The value for this property will be <see cref="T:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer"/> by default.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider">
            <summary>
            Specifies callback methods which the <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware"></see> invokes to enable developer control over the authentication process. /&gt;
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext)">
            <summary>
            Invoked whenever Twitter succesfully authenticates a user
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext)">
            <summary>
            Invoked prior to the <see cref="T:System.Security.Claims.ClaimsIdentity"/> being saved in a local cookie and the browser being redirected to the originally requested URL.
            </summary>
            <param name="context"></param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext)">
            <summary>
            Called when a Challenge causes a redirect to authorize endpoint in the Twitter middleware
            </summary>
            <param name="context">Contains redirect URI and <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> of the challenge </param>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext">
            <summary>
            Context passed when a Challenge causes a redirect to authorize endpoint in the Twitter middleware
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions,Microsoft.Owin.Security.AuthenticationProperties,System.String)">
            <summary>
            Creates a new context object.
            </summary>
            <param name="context">The OWIN request context</param>
            <param name="options">The Facebook middleware options</param>
            <param name="properties">The authenticaiton properties of the challenge</param>
            <param name="redirectUri">The initial redirect URI</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext.RedirectUri">
            <summary>
            Gets the URI used for the redirect operation.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext.Properties">
            <summary>
            Gets the authenticaiton properties of the challenge
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext">
            <summary>
            Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.#ctor(Microsoft.Owin.IOwinContext,System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext"/>
            </summary>
            <param name="context">The OWIN environment</param>
            <param name="userId">Twitter user ID</param>
            <param name="screenName">Twitter screen name</param>
            <param name="accessToken">Twitter access token</param>
            <param name="accessTokenSecret">Twitter access token secret</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.UserId">
            <summary>
            Gets the Twitter user ID
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.ScreenName">
            <summary>
            Gets the Twitter screen name
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.AccessToken">
            <summary>
            Gets the Twitter access token
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.AccessTokenSecret">
            <summary>
            Gets the Twitter access token secret
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.Identity">
            <summary>
            Gets the <see cref="T:System.Security.Claims.ClaimsIdentity"/> representing the user
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.Properties">
            <summary>
            Gets or sets a property bag for common authentication properties
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider">
            <summary>
            Default <see cref="T:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider"/> implementation.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.#ctor">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider"/>
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext)">
            <summary>
            Invoked whenever Twitter succesfully authenticates a user
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext)">
            <summary>
            Invoked prior to the <see cref="T:System.Security.Claims.ClaimsIdentity"/> being saved in a local cookie and the browser being redirected to the originally requested URL.
            </summary>
            <param name="context"></param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext)">
            <summary>
            Called when a Challenge causes a redirect to authorize endpoint in the Twitter middleware
            </summary>
            <param name="context">Contains redirect URI and <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> of the challenge </param>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.OnAuthenticated">
            <summary>
            Gets or sets the function that is invoked when the Authenticated method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.OnReturnEndpoint">
            <summary>
            Gets or sets the function that is invoked when the ReturnEndpoint method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.OnApplyRedirect">
            <summary>
            Gets or sets the delegate that is invoked when the ApplyRedirect method is invoked.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext">
            <summary>
            Provides context information to middleware providers.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            Initializes a new <see cref="T:Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext"/>.
            </summary>
            <param name="context">OWIN environment</param>
            <param name="ticket">The authentication ticket</param>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Resources.Exception_OptionMustBeProvided">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; option must be provided..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.Resources.Exception_ValidatorHandlerMismatch">
            <summary>
              Looks up a localized string similar to An ICertificateValidator cannot be specified at the same time as an HttpMessageHandler unless it is a WebRequestHandler..
            </summary>
        </member>
        <member name="T:Owin.TwitterAuthenticationExtensions">
            <summary>
            Extension methods for using <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware"/>
            </summary>
        </member>
        <member name="M:Owin.TwitterAuthenticationExtensions.UseTwitterAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions)">
            <summary>
            Authenticate users using Twitter
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="options">Middleware configuration options</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
        <member name="M:Owin.TwitterAuthenticationExtensions.UseTwitterAuthentication(Owin.IAppBuilder,System.String,System.String)">
            <summary>
            Authenticate users using Twitter
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="consumerKey">The Twitter-issued consumer key</param>
            <param name="consumerSecret">The Twitter-issued consumer secret</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware">
            <summary>
            OWIN middleware for authenticating users using Twitter
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware"/>
            </summary>
            <param name="next">The next middleware in the OWIN pipeline to invoke</param>
            <param name="app">The OWIN application</param>
            <param name="options">Configuration options for the middleware</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware.CreateHandler">
            <summary>
            Provides the <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> object for processing authentication-related requests.
            </summary>
            <returns>An <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> configured with the <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions"/> supplied to the constructor.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions">
            <summary>
            Options for the Twitter authentication middleware.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.ConsumerKey">
            <summary>
            Gets or sets the consumer key used to communicate with Twitter.
            </summary>
            <value>The consumer key used to communicate with Twitter.</value>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.ConsumerSecret">
            <summary>
            Gets or sets the consumer secret used to sign requests to Twitter.
            </summary>
            <value>The consumer secret used to sign requests to Twitter.</value>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.BackchannelTimeout">
            <summary>
            Gets or sets timeout value in milliseconds for back channel communications with Twitter.
            </summary>
            <value>
            The back channel timeout.
            </value>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.BackchannelCertificateValidator">
            <summary>
            Gets or sets the a pinned certificate validator to use to validate the endpoints used
            in back channel communications belong to Twitter.
            </summary>
            <value>
            The pinned certificate validator.
            </value>
            <remarks>If this property is null then the default certificate checks are performed,
            validating the subject name and if the signing chain is a trusted party.</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.BackchannelHttpHandler">
            <summary>
            The HttpMessageHandler used to communicate with Twitter.
            This cannot be set at the same time as BackchannelCertificateValidator unless the value 
            can be downcast to a WebRequestHandler.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.Caption">
            <summary>
            Get or sets the text that the user can display on a sign in user interface.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.CallbackPath">
            <summary>
            The request path within the application's base path where the user-agent will be returned.
            The middleware will process this request when it arrives.
            Default value is "/signin-twitter".
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.SignInAsAuthenticationType">
            <summary>
            Gets or sets the name of another authentication middleware which will be responsible for actually issuing a user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.StateDataFormat">
            <summary>
            Gets or sets the type used to secure data handled by the middleware.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.Provider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider"/> used to handle authentication events.
            </summary>
        </member>
    </members>
</doc>
