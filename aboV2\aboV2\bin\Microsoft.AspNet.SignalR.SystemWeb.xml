<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNet.SignalR.SystemWeb</name>
    </assembly>
    <members>
        <member name="M:Microsoft.AspNet.SignalR.RequestExtensions.GetHttpContext(Microsoft.AspNet.SignalR.IRequest)">
            <summary>
            Returns the <see cref="T:System.Web.HttpContextBase"/> for this <see cref="T:Microsoft.AspNet.SignalR.IRequest"/>.
            </summary>
            <param name="request">The request</param>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapConnection``1(System.Web.Routing.RouteCollection,System.String,System.String)">
            <summary>
            Maps a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/> with the default dependency resolver to the specified path.
            </summary>
            <param name="routes">The route table.</param>
            <typeparam name="T">The type of <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/>.</typeparam>
            <param name="name">The name of the route.</param>
            <param name="url">path of the route.</param>
            <returns>The registered route.</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapConnection``1(System.Web.Routing.RouteCollection,System.String,System.String,Microsoft.AspNet.SignalR.ConnectionConfiguration)">
            <summary>
            Maps a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/> with the default dependency resolver to the specified path.
            </summary>
            <param name="routes">The route table.</param>
            <typeparam name="T">The type of <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/>.</typeparam>
            <param name="name">The name of the route.</param>
            <param name="url">path of the route.</param>
            <param name="configuration">Configuration options.</param>
            <returns>The registered route.</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapConnection``1(System.Web.Routing.RouteCollection,System.String,System.String,Microsoft.AspNet.SignalR.ConnectionConfiguration,System.Action{Owin.IAppBuilder})">
            <summary>
            Maps a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/> with the default dependency resolver to the specified path.
            </summary>
            <param name="routes">The route table.</param>
            <typeparam name="T">The type of <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/>.</typeparam>
            <param name="name">The name of the route.</param>
            <param name="url">path of the route.</param>
            <param name="configuration">Configuration options.</param>
            <param name="build">An action to further configure the OWIN pipeline.</param>
            <returns>The registered route</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapConnection(System.Web.Routing.RouteCollection,System.String,System.String,System.Type,Microsoft.AspNet.SignalR.ConnectionConfiguration)">
            <summary>
            Maps a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/> with the default dependency resolver to the specified path.
            </summary>
            <param name="routes">The route table.</param>
            <param name="name">The name of the route.</param>
            <param name="url">path of the route.</param>
            <param name="type">The type of <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/>.</param>
            <param name="configuration">Configuration options.</param>
            <returns>The registered route</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapConnection(System.Web.Routing.RouteCollection,System.String,System.String,System.Type,Microsoft.AspNet.SignalR.ConnectionConfiguration,System.Action{Owin.IAppBuilder})">
            <summary>
            Maps a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/> with the default dependency resolver to the specified path.
            </summary>
            <param name="routes">The route table.</param>
            <param name="name">The name of the route.</param>
            <param name="url">path of the route.</param>
            <param name="type">The type of <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection"/>.</param>
            <param name="configuration">Configuration options.</param>
            <param name="build">An action to further configure the OWIN pipeline.</param>
            <returns>The registered route</returns>.
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapHubs(System.Web.Routing.RouteCollection)">
            <summary>
            Initializes the default hub route (/signalr).
            </summary>
            <param name="routes">The route table.</param>
            <returns>The registered route.</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapHubs(System.Web.Routing.RouteCollection,Microsoft.AspNet.SignalR.HubConfiguration)">
            <summary>
            Initializes the default hub route (/signalr).
            </summary>
            <param name="routes">The route table.</param>
            <param name="configuration">Configuration options.</param>
            <returns>The registered route.</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapHubs(System.Web.Routing.RouteCollection,System.String,Microsoft.AspNet.SignalR.HubConfiguration)">
            <summary>
            Initializes the hub route using specified configuration.
            </summary>
            <param name="routes">The route table.</param>
            <param name="path">The path of the hubs route.</param>
            <param name="configuration">Configuration options.</param>
            <returns>The registered route.</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapHubs(System.Web.Routing.RouteCollection,System.String,Microsoft.AspNet.SignalR.HubConfiguration,System.Action{Owin.IAppBuilder})">
            <summary>
            Initializes the hub route using specified configuration.
            </summary>
            <param name="routes">The route table.</param>
            <param name="path">The path of the hubs route.</param>
            <param name="configuration">Configuration options.</param>
            <param name="build">An action to further configure the OWIN pipeline.</param>
            <returns>The registered route.</returns>
        </member>
        <member name="M:System.Web.Routing.SignalRRouteExtensions.MapHubs(System.Web.Routing.RouteCollection,System.String,System.String,Microsoft.AspNet.SignalR.HubConfiguration,System.Action{Owin.IAppBuilder})">
            <summary>
            Initializes the hub route using specified configuration.
            </summary>
            <param name="routes">The route table.</param>
            <param name="name">The name of the route.</param>
            <param name="path">The path of the hubs route.</param>
            <param name="configuration">Configuration options.</param>
            <param name="build"></param>
            <returns>The registered route.</returns>
        </member>
    </members>
</doc>
