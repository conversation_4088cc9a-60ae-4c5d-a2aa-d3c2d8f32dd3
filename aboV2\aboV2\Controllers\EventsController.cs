﻿using aboV2.App_Code;
using aboV2.Models.Enums;
using log4net;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using utilitaires2010;
using utilitaires2010.Exceptions;
using WebTracing2010;
using ws_bll;
using ws_DTO;
using ws_DTO.objets_liaisons;

namespace aboV2.Controllers
{

    //public class keyNbr_FormulasTarif
    //{
    //    public int nbr { get; set; }

    //    public string key { get; set; }
    //}
    public class EventFlage
    {
        public int event_id { get; set; }
        public int session_id { get; set; }
        public List<int> seatsFlagges { get; set; }
    }



    public class EventsController : BaseController
    {
        private static readonly ILog logger4net = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);



        public static int constPrisePlaceMemePlace = 2;




        private void CheckJaugesLimits(int structureId, int eventId, int sessionId, Dictionary<int, int> tarifPlacesDict)
        {
            string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];
            // Check jauges séance
            List<int> jaugesGroupIdsForSession = JaugesManager.GetJaugesGroupIdsForSession(typeRun, structureId, sessionId);
            if (jaugesGroupIdsForSession.Any())
            {
                ValidateJaugesForGroup(structureId, eventId, sessionId,
                   jaugesGroupIdsForSession[0], tarifPlacesDict,
                   (tarifId) => SeatManager.GetEntriesCountForSessionAndTarif(typeRun, structureId, eventId, sessionId, tarifId), typeRun);
                return; // Si jauges séance OK, on ne check pas manifestation
            }

            // Check jauges manifestation (seulement si pas de jauges séance)
            List<int> jaugesGroupIdsForEvents = JaugesManager.GetJaugesGroupIdsForEvent(typeRun, structureId, eventId);
            if (jaugesGroupIdsForEvents.Any())
            {
                ValidateJaugesForGroup(structureId, eventId, 0,
                   jaugesGroupIdsForEvents[0], tarifPlacesDict,
                   (tarifId) => SeatManager.GetEntriesCountForManifestationAndTarif(typeRun, structureId, eventId, tarifId), typeRun);
            }


        }


        private void ValidateJaugesForGroup(int structureId, int eventId, int sessionId, int groupId, Dictionary<int, int> tarifPlacesDict,
           Func<int, int> getEntriesCount, string typeRun)
        {
            try
            {

                int jaugeCapacity = JaugesManager.GetJaugeValueForGroup(typeRun, structureId, eventId, sessionId, groupId);
                int totalExistingEntries = 0;
                int requestedPlacesWithJauge = 0;


                foreach (var kvp in tarifPlacesDict)
                {
                    int tarifId = kvp.Key;
                    int placesCount = kvp.Value;

                    bool tarifInGroup = JaugesManager.IsTarifInGroupJauges(typeRun, structureId, tarifId, groupId);
                    if (tarifInGroup)
                    {
                        int entriesForTarif = getEntriesCount(tarifId);
                        totalExistingEntries += entriesForTarif;
                        requestedPlacesWithJauge += placesCount;
                    }
                }

                if (totalExistingEntries + requestedPlacesWithJauge >= jaugeCapacity)
                {
                    throw new JaugesLimitExceededException(
                        totalExistingEntries, requestedPlacesWithJauge, jaugeCapacity, sessionId);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
            //catch (JaugesLimitExceededException ex)
            //{
            //    throw;
            //    //throw new HttpResponseException(Problem(detail: "Jauges", statusCode: StatusCodes.Status401Unauthorized, title: "jauges:NotEnoughSeatAvailable"));
            //}
        }

        [Throttle(Name = "Flag", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        public ActionResult Flag(int structureId, int identiteId, customFormulas[] objChoices, customChoiceHA_recu objChoicesHA)
        {
            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "Flag start ('" + structureId + " " + string.Join(",", objChoices.Select(abo => abo.formulaid)));
            GestionTrace.WriteLog(structureId, "Flag ('" + structureId + "," + identiteId + "," + objChoices.Length + "," + objChoicesHA + ")");

            bool isAlgoMemePlace = false;

            try
            {
                bool flagAllOk = true;
                List<EventFlage> listEventsFlages = new List<EventFlage>();

                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();

                // TODO recheck coté serveur les contraintes
                if (objChoices != null && objChoices.Length > 0 && objChoices[0] != null)
                {
                    if (objChoices.Length == 1 && objChoicesHA.listEventsHA == null)
                    {
                        int thisformulaId = objChoices[0].formulaid;
                        wcf_Themis.Iwcf_wsThemisClient wcfThemisPP = new wcf_Themis.Iwcf_wsThemisClient();
                        int typePP = wcfThemisPP.GetTypePrisePlaceOfFormula(structureId, "", identiteId, thisformulaId);
                        isAlgoMemePlace = (typePP & constPrisePlaceMemePlace) != 0;
                    }

                    int userId = 0; // TODO
                    if (System.Web.HttpContext.Current.Session["currIdUser"] == null)
                    {
                        // SessionPerdue
                        var result = new
                        {
                            Code = (int)ErrorsCode.NotInitialized,
                            StrCode = Initialisations.GetDescription(ErrorsCode.NotInitialized),
                            Comment = "Not initialized"
                        };

                        log.LogMsg(structureId, LogLevel.ERROR, "Flag session perdue");

                        return Json(result, JsonRequestBehavior.AllowGet);

                    }
                    else
                    {
                        userId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());
                    }

                    customChoiceHA_recu custCxHAReturn = new customChoiceHA_recu();

                    customBasket customBasketReturn = new customBasket
                    {
                        messageError = new List<CustomErrorsFlags>(),

                        IsCreateBasket = true,
                        StrDateCreateBasket = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                    };


                    custCxHAReturn.listEventsHA = new List<customEventHA>();

                    List<customFormulas> lChoices = objChoices.ToList();
                    customFormulas[] objChoicesFlag = new customFormulas[objChoices.Length];
                    List<customEvent> listDistinctSessions = new List<customEvent>();

                    /// seances hors abo, à prendre à part


                    //var listDistinctsSessions = lChoices.SelectMany(e => e.listAbos).SelectMany(d => d.listEvents).Select(s => { return s; }).GroupBy(ss => new { ss.sessionid, ss.eventid, ss.categid }).ToList();
                    var listDistinctsSessionsAbo = lChoices.SelectMany(d => d.listTarifs).SelectMany(r => r.listAbos).SelectMany(y => y.listEvents).Select(s => { return s; }).GroupBy(ss =>
                        new
                        {
                            ss.sessionid,
                            ss.eventid,
                            ss.categid,
                            ss.zoneId,
                            ss.zoneName,
                            ss.floorId,
                            ss.floorName,
                            ss.sectionId,
                            ss.sectionName,
                            ss.eventName,
                            ss.categoryName,
                            ss.sessionDescription,
                            ss.SessionStartDate,
                            ss.GestionPlaceId
                        }).ToList();

                    List<int> lSessionsInt = lChoices.SelectMany(d => d.listTarifs).SelectMany(r => r.listAbos).SelectMany(y => y.listEvents).Select(s => { return s.sessionid; }).ToList();

                    List<customEvent> listDistinctSessionsHA = new List<customEvent>();
                    // liste des sessions id à prendre à part
                    List<int> listDistinctsSessionHA = new List<int>();

                    #region retravaille objChoicesHA
                    List<customEventHA> customHAGroup = new List<customEventHA>();
                    //var ttt = objChoicesHA.listEventsHA.SelectMany(g => g.listTarifs).GroupBy(gg => gg.gpid).Select(s => { return s; }).ToList();

                    //var groupedChoicesHAList = objChoicesHA.listEventsHA.SelectMany(g => g.listTarifs).GroupBy(gp => gp.gpid).Select(grp => grp.ToList()).ToList();
                    //var groupedChoicesHAList = objChoicesHA.listEventsHA.SelectMany(g => g.listTarifs).GroupBy(gp => gp.gpid).Select(grp => new { key =grp.Key, nb =grp.Sum(nb => nb.nb) }).ToList();


                    /*
                     * ici
                     * var groupedChoicesHAList = objChoicesHA.listEventsHA.SelectMany(g => g.listTarifs).GroupBy(gp => gp.gpid).Select(s => { return s; }).Select(grp => new { key = grp.Key, nb = grp.Sum(nb => nb.nb), ha = grp.Where(g => g.gpid == grp.Key).FirstOrDefault() }).ToList();

                    foreach (var item in groupedChoicesHAList)
                    {
                        item.ha.nb = item.nb;
                       // customHAGroup.Add(item.ha);

                        //objChoicesHA.listEventsHA.SelectMany(g => g.listTarifs).Where(gp => gp.gpid == 915).FirstOrDefault()
                        //item.v
                        //customHAGroup.Add()
                    }
                    */


                    if (objChoicesHA != null && objChoicesHA.listEventsHA != null)
                    { // retravaille objChoicesHA recu pour supp les eventid=0 (=demandes ha overwritée + obtenir la liste des seances / categ demandées)
                        customChoiceHA_recu objChoicesHAworked = new customChoiceHA_recu
                        {
                            listEventsHA = new List<customEventHA>()
                        };
                        foreach (customEventHA otarif in objChoicesHA.listEventsHA)
                        {
                            customEventHA notarif = otarif;
                            if (otarif.eventid != 0)
                            {
                                objChoicesHAworked.listEventsHA.Add(notarif);
                                // if (listDistinctsSessions.Select(s => s.Key.sessionid == otarif.sessionid).Count()==0)
                                if (listDistinctsSessionsAbo.Where(x => x.Key.sessionid == otarif.sessionid && x.Key.categid == otarif.categid &&
                                    (x.Key.zoneId == otarif.zoneid || x.Key.zoneId == 0) &&
                                    (x.Key.floorId == otarif.floorid || x.Key.floorId == 0) &&
                                    (x.Key.sectionId == otarif.sectionid || x.Key.sectionId == 0)).ToList().Count() == 0)
                                {
                                    customEvent custS = new customEvent
                                    {
                                        sessionid = otarif.sessionid,
                                        eventid = otarif.eventid,
                                        categid = otarif.categid,
                                        lieuName = otarif.lieuName,
                                        sessionDescription = otarif.sessionDescription,
                                        categoryName = otarif.categoryName,
                                        eventName = otarif.eventName,
                                        zoneId = otarif.zoneid,
                                        floorId = otarif.floorid,
                                        floorName = otarif.floorName,
                                        sectionId = otarif.sectionid,
                                        sectionName = otarif.sectionName,
                                        TypePrise = otarif.type,
                                        TypePriseName = otarif.typeName,
                                        SessionStartDate = otarif.SessionStartDate
                                    };

                                    listDistinctSessionsHA.Add(custS);
                                    //listDistinctsSessionHA.Add(otarif.sessionid);
                                }
                            }

                            var tarifPlacesDict = otarif.listTarifs
                                    .GroupBy(t => t.priceId)
                                    .ToDictionary(g => g.Key, g => g.Sum(t => t.nb));

                            CheckJaugesLimits(structureId, otarif.eventid, otarif.sessionid, tarifPlacesDict);
                        }
                        objChoicesHA = objChoicesHAworked;
                    }
                    #endregion






                    #region flag des places completement HA (session et categ qui n'ont pas de formules correspondantes)
                    foreach (customEvent osession in listDistinctSessionsHA)
                    {
                        // TODO flag les places manifs non prises dans un abo
                        int thiseventid = osession.eventid;
                        int thissessionid = osession.sessionid;
                        int thiszoneid = osession.zoneId;
                        int thisfloorid = osession.floorId;
                        int thissectionid = osession.sectionId;
                        int thiscategid = osession.categid;

                        /// clés formules : formuleId/tarif/categ | nombre
                        Dictionary<string, int> keyNbrTarifFormulas = new Dictionary<string, int>();
                        // gpid | nombre
                        Dictionary<string, int> keyGpId_Nbr_HA = new Dictionary<string, int>();


                        customEventHA objThisSession = objChoicesHA.listEventsHA.Where(x => x.eventid == thiseventid && x.sessionid == thissessionid && x.categid == thiscategid && x.zoneid == thiszoneid && x.floorid == thisfloorid && x.sectionid == thissectionid).FirstOrDefault();

                        int nbrTotal = 0;
                        foreach (customTarifHA tarif in objThisSession.listTarifs)
                        {
                            nbrTotal += tarif.nb;
                            keyNbrTarifFormulas.Add("0/" + tarif.priceId + "/" + objThisSession.categid, tarif.nb);
                            keyGpId_Nbr_HA.Add(tarif.gpid.ToString(), tarif.nb);
                        }

                        List<SeatEntity> listSeats = wcfThemis.FlagAuto(structureId, thiseventid, thissessionid, keyNbrTarifFormulas, keyGpId_Nbr_HA, nbrTotal, userId).ToList();

                        if (listSeats.Count == nbrTotal) // ********* flag ok
                        {
                            EventFlage oEventFlage = new EventFlage
                            {
                                event_id = thiseventid,
                                session_id = thissessionid,
                                seatsFlagges = new List<int>()
                            };
                            foreach (SeatEntity seat in listSeats)
                            {
                                oEventFlage.seatsFlagges.Add(seat.Seat_id);
                            }
                            listEventsFlages.Add(oEventFlage);

                            customEventHA customHAReturn = new customEventHA
                            {
                                eventid = thiseventid,
                                sessionid = thissessionid,
                                eventName = objThisSession.eventName,
                                categid = thiscategid,
                                categoryName = objThisSession.categoryName,
                                floorid = objThisSession.floorid,
                                lieuName = objThisSession.lieuName,
                                sessionDescription = objThisSession.sessionDescription,
                                SessionStartDate = objThisSession.SessionStartDate,
                                type = objThisSession.type,
                                typeName = objThisSession.typeName
                            };
                            //if (objChoicesHA != null && objChoicesHA.listEventsHA != null)
                            //{
                            //    if (objChoicesHA.listEventsHA.Where(x => x.eventid == thiseventid).ToArray().Length > 0)
                            //    {
                            //        customHAReturn.eventName = objChoicesHA.listEventsHA.Where(x => x.eventid == thiseventid).ToArray()[0].eventName;
                            //    }
                            //}
                            List<int> listSeatsAttribues = new List<int>();
                            customHAReturn.listTarifs = new List<customTarifHA>();

                            /// attribution des places HORS ABO sur les bons tarifs

                            //foreach (ws_DTO.SeatEntity seat in listSeats)
                            //{
                            //    bool thisoneisdone = false;
                            //    foreach (customTarifHA tarif in objThisSession.listTarifs)
                            //    {
                            //        if (!thisoneisdone)
                            //        {
                            //            if (tarif.listSeats == null)
                            //                tarif.listSeats = new List<customFlaggedSeat>();
                            //            if (tarif.listSeats.Count < tarif.nb)
                            //            {
                            //                customFlaggedSeat flagSeat = new customFlaggedSeat
                            //                {
                            //                    seatid = seat.Seat_id,
                            //                    rank = seat.Rank,
                            //                    seat = seat.Seat,
                            //                    hashKey = getHash(seat.Seat_id + "/" + thissessionid, thisSalt)
                            //                };
                            //                thisoneisdone = true;
                            //                tarif.listSeats.Add(flagSeat);
                            //            }
                            //        }
                            //        customHAReturn.listTarifs.Add(tarif);
                            //    }                                
                            //}

                            List<string> listSeatsHA_attribues = new List<string>();
                            foreach (customTarifHA tarif in objThisSession.listTarifs)
                            {
                                tarif.listSeats = new List<customFlaggedSeat>();
                                for (int i = 0; i < tarif.nb; i++)
                                {
                                    foreach (ws_DTO.SeatEntity seat in listSeats)
                                    {
                                        string keyThisSeat = seat.Seat_id + "." + seat.Seance_id;
                                        if (!listSeatsHA_attribues.Contains(keyThisSeat))
                                        {



                                            if (tarif.isPlacementLibre)
                                            {
                                                customFlaggedSeat flagSeat = new customFlaggedSeat
                                                {
                                                    seatid = seat.Seat_id,
                                                    rank = "LIBRE",
                                                    seat = "LIBRE",
                                                    hashKey = getHash(seat.Seat_id + "/" + thissessionid, thisSalt)
                                                };
                                                tarif.listSeats.Add(flagSeat);
                                            }
                                            else
                                            {
                                                customFlaggedSeat flagSeat = new customFlaggedSeat
                                                {
                                                    seatid = seat.Seat_id,
                                                    rank = seat.Rank,
                                                    seat = seat.Seat,
                                                    hashKey = getHash(seat.Seat_id + "/" + thissessionid, thisSalt)
                                                };
                                                tarif.listSeats.Add(flagSeat);

                                            }
                                            listSeatsHA_attribues.Add(keyThisSeat);
                                            break;
                                        }
                                    }
                                }
                                customHAReturn.listTarifs.Add(tarif);
                            }



                            //    //tarif.listSeats = new List<customFlaggedSeat>();
                            //    foreach (customTarifHA tarif in objThisSession.listTarifs)
                            //{
                            //    //customTarifHA custT = new customTarifHA();
                            //    int nbrToDo = tarif.nb;
                            //    tarif.listSeats = new List<customFlaggedSeat>();
                            //    foreach (ws_DTO.SeatEntity seat in listSeats)
                            //    {
                            //        if (!listSeatsAttribues.Contains(seat.Seat_id))
                            //        {
                            //            customFlaggedSeat flagSeat = new customFlaggedSeat
                            //            {
                            //                seatid = seat.Seat_id,
                            //                rank = seat.Rank,
                            //                seat = seat.Seat,
                            //                hashKey = getHash(seat.Seat_id + "/" + thissessionid, thisSalt)
                            //            };                                                                        
                            //            tarif.listSeats.Add(flagSeat);
                            //            listSeatsAttribues.Add(flagSeat.seatid);
                            //            idxSeat++;
                            //        }
                            //    }
                            //    customHAReturn.listTarifs.Add(tarif);



                            //}



                            if (customBasketReturn.listEventsHA == null)
                            {
                                customBasketReturn.listEventsHA = new List<customEventHA>();
                            }

                            if (customHAReturn.listTarifs.Count > 0)
                            {
                                customBasketReturn.listEventsHA.Add(customHAReturn);
                            }

                        }
                        else
                        {
                            // n'arrive pas à flagger !!!
                            customBasketReturn.messageError.Add(new CustomErrorsFlags() { EventName = osession.eventName, SessionDescription = osession.sessionDescription, EventId = osession.eventid, CategoryName = osession.categoryName, SessionId = osession.sessionid, CategoryId = osession.categid });
                            flagAllOk = false;
                        }

                    }


                    #endregion

                    #region flag des places abo + hors abo de la même seance

                    bool flagMassifAboMemePlaceIsDone = false;
                    List<ws_DTO.SeatEntity> listSeatsmemePlace = new List<ws_DTO.SeatEntity>();

                    //  wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();

                    foreach (customFormulas lc in lChoices)
                    {
                        foreach (customTarif tt in lc.listTarifs)
                        {
                            foreach (customAbo ab in tt.listAbos)
                            {

                                foreach (var eventCustom in ab.listEvents)
                                {
                                    var tarifPlacesDict = new Dictionary<int, int> { { tt.tarifid, tt.nb } };

                                    CheckJaugesLimits(
                                       structureId,
                                       eventCustom.eventid,
                                       eventCustom.sessionid,
                                       tarifPlacesDict);
                                }

                            }
                        }
                    }



                    if (isAlgoMemePlace)
                    {


                        //var listAboThisSession = lChoices.SelectMany(d => d.listTarifs).SelectMany(r => r.listAbos).Where(x => x.listEvents.Any(s => s.sessionid == thissessionid && s.categid == thiscategid && s.zoneId == thiszoneid
                        //  && s.floorId == thisfloorid && s.sectionId == thissectionid)).ToList();

                        var choixPhysiqueIds = lChoices.SelectMany(d => d.listTarifs).SelectMany(r => r.listAbos).SelectMany(e => e.listEvents).GroupBy(gc => new { gc.categid, gc.zoneId, gc.floorId, gc.sectionId }).ToList();
                        foreach (var choixpshysique in choixPhysiqueIds)
                        {

                            // chercher le nombre de places max voulu pour ce choix "physique" (ie categ, zone, etage, section)
                            Dictionary<int, int> nbrDePlacesParSeances = new Dictionary<int, int>();

                            int thiscategid = choixpshysique.Key.categid;
                            int thiszoneid = choixpshysique.Key.zoneId;
                            int thisfloorid = choixpshysique.Key.floorId;
                            int thissectionid = choixpshysique.Key.sectionId;

                            Dictionary<string, int> keyNbrTarifFormulas = new Dictionary<string, int>();

                            List<customAbo> listAboThisChoixPhysique = lChoices.SelectMany(d => d.listTarifs).SelectMany(r => r.listAbos).Where(x => x.listEvents.Any(s => s.categid == thiscategid && s.zoneId == thiszoneid
                                && s.floorId == thisfloorid && s.sectionId == thissectionid)).ToList();

                            Dictionary<int, string> dicTarifsParSeance = new Dictionary<int, string>();

                            foreach (customFormulas lc in lChoices)
                            {
                                foreach (customTarif tt in lc.listTarifs)
                                {
                                    foreach (customAbo ab in tt.listAbos)
                                    {

                                        foreach (customEvent e in ab.listEvents.Where(e => e.categid == thiscategid && e.zoneId == thiszoneid && e.floorId == thisfloorid && e.sectionId == thissectionid))
                                        {
                                            if (!nbrDePlacesParSeances.Keys.Contains(e.sessionid))
                                            {
                                                dicTarifsParSeance.Add(e.sessionid, tt.tarifid.ToString());
                                                nbrDePlacesParSeances.Add(e.sessionid, 0);

                                                //string thisKey = thisformulaId.ToString() + "/" + thistarif.ToString() + "/" + thiscategid.ToString() + "/" + thiszoneid.ToString() + "/" + thisfloorid.ToString() + "/" + thissectionid.ToString();

                                            }
                                            else
                                            {
                                                dicTarifsParSeance[e.sessionid] += "," + tt.tarifid.ToString();
                                            }
                                            nbrDePlacesParSeances[e.sessionid] += 1; // une place de plus pour cette séance
                                        }
                                    }
                                }
                            }
                            //Dictionary<int, string> dicTarifsParSeance = new Dictionary<int, string>();

                            int maxPlacesThisChoixPhysique = 0;
                            foreach (int sessid in nbrDePlacesParSeances.Keys)
                            {
                                if (maxPlacesThisChoixPhysique < nbrDePlacesParSeances[sessid])
                                {
                                    maxPlacesThisChoixPhysique = nbrDePlacesParSeances[sessid];
                                }
                            }
                            // il y a nbrDePlacesParSeances[sessid] à flagger pour ce choix physique
                            int thisformulaId = objChoices[0].formulaid;



                            listSeatsmemePlace = wcfThemis.FlagAutoAboMemePlace(structureId, thisformulaId, thiszoneid, thisfloorid, thissectionid, thiscategid, dicTarifsParSeance, null, userId).ToList();

                            Dictionary<string, int> keyNbrTarifFormulas2 = new Dictionary<string, int>();
                            /// clés formules : formuleId/tarif/categ/zone/etage/section | nombre
                            /// 

                            foreach (var dsess in listDistinctsSessionsAbo)
                            {
                                int thiseventid = dsess.Key.eventid;
                                int thissessionid = dsess.Key.sessionid;


                                List<customAbo> listAboThisSession = lChoices.SelectMany(d => d.listTarifs).SelectMany(r => r.listAbos).Where(x => x.listEvents.Any(s => s.sessionid == thissessionid && s.categid == thiscategid && s.zoneId == thiszoneid
                                                      && s.floorId == thisfloorid && s.sectionId == thissectionid)).ToList();

                                List<ws_DTO.SeatEntity> listSeats = new List<ws_DTO.SeatEntity>();
                                foreach (customAbo abo in listAboThisSession)
                                {
                                    listSeats = listSeatsmemePlace.Where(s => s.Manif_id == thiseventid && s.Seance_id == thissessionid).ToList();
                                }
                                int seatIdx = 0;

                                EventFlage oEventFlage = new EventFlage
                                {
                                    event_id = thiseventid,
                                    session_id = thissessionid,
                                    seatsFlagges = new List<int>()
                                };
                                foreach (ws_DTO.SeatEntity seat in listSeats)
                                {
                                    oEventFlage.seatsFlagges.Add(seat.Seat_id);

                                }
                                listEventsFlages.Add(oEventFlage);


                                foreach (ws_DTO.SeatEntity seat in listSeats)
                                {
                                    customFlaggedSeat flagSeat = new customFlaggedSeat
                                    {
                                        seatid = seat.Seat_id,
                                        rank = seat.Rank,
                                        seat = seat.Seat,
                                        hashKey = getHash(seat.Seat_id + "/" + thissessionid, thisSalt)

                                    };

                                    Debug.WriteLine(flagSeat.seatid + " " + flagSeat.rank + " " + flagSeat.seat);

                                    if (listAboThisSession.Count > seatIdx)
                                    {

                                        // une place par abo : on suit le compteur
                                        customEvent thisSession = listAboThisSession[seatIdx].listEvents.Where(p => p.sessionid == thissessionid && p.categid == thiscategid).FirstOrDefault();

                                        //thisSession.unitTTCamount = 

                                        thisSession.listSeats = new List<customFlaggedSeat>
                                        {
                                            flagSeat
                                        };
                                    }
                                    else
                                    {


                                    }
                                    seatIdx++;
                                }


                                //string thisKey = thisformulaId.ToString() + "/" + thistarif.ToString() + "/" + thiscategid.ToString() + "/" + thiszoneid.ToString() + "/" + thisfloorid.ToString() + "/" + thissectionid.ToString();
                                //if (keyNbrTarifFormulas.Keys.Contains(thisKey))
                                //{
                                //    keyNbrTarifFormulas[thisKey] += 1;
                                //}
                                //else
                                //{
                                //    keyNbrTarifFormulas.Add(thisKey, 1);
                                //}

                                // listSeatsmemePlace = wcfThemis.FlagAutoAboMemePlace(structureId, thisformulaId, thiseventid, lSessionsInt.ToArray(), keyNbrTarifFormulas, keyGpId_Nbr_HA, nbrTotal, userId).ToList();


                                // if (listSeatsmemePlace.Count == 0)
                                //  {
                                //         logger.Error(@"/!\ algo même place remonte vide pour " + structureId + "," + thisformulaId + "," + thiseventid + "," + thissessionid + "," + thiscategid);
                                //   }



                            }
                        }
                        customEventHA customHAReturn = new customEventHA();
                        if (customBasketReturn.listEventsHA == null)
                        {
                            customBasketReturn.listEventsHA = new List<customEventHA>();
                        }

                        if (customHAReturn.listTarifs == null)
                        {
                            customHAReturn.listTarifs = new List<customTarifHA>();
                        }

                        if (customHAReturn.listTarifs.Count > 0)
                        {
                            customBasketReturn.listEventsHA.Add(customHAReturn);
                        }
                    }
                    else
                    {

                        foreach (var dsess in listDistinctsSessionsAbo)
                        {
                            if (flagAllOk)
                            {
                                customEventHA customHAReturn = new customEventHA();

                                //int nbrToFlag = 0;
                                int thiseventid = dsess.Key.eventid;
                                int thissessionid = dsess.Key.sessionid;
                                int thiscategid = dsess.Key.categid;
                                int thiszoneid = dsess.Key.zoneId;
                                int thisfloorid = dsess.Key.floorId;
                                int thissectionid = dsess.Key.sectionId;

                                customHAReturn.eventid = thiseventid;
                                customHAReturn.sessionid = thissessionid;
                                if (objChoicesHA != null && objChoicesHA.listEventsHA != null)
                                {
                                    if (objChoicesHA.listEventsHA.Where(x => x.eventid == thiseventid && x.sessionid == thissessionid && x.categid == thiscategid).ToArray().Length > 0)
                                    {
                                        customEventHA recu = objChoicesHA.listEventsHA.Where(x => x.eventid == thiseventid && x.sessionid == thissessionid && x.categid == thiscategid).ToArray()[0];
                                        customHAReturn.eventName = recu.eventName;
                                        customHAReturn.lieuName = recu.lieuName;
                                        customHAReturn.sessionDescription = recu.sessionDescription;
                                        customHAReturn.SessionStartDate = recu.SessionStartDate;
                                        //customHAReturn.categid = thiscategid;
                                        customHAReturn.zoneid = thiszoneid;
                                        customHAReturn.floorid = thisfloorid;
                                        customHAReturn.sectionid = thissectionid;


                                    }
                                }

                                customHAReturn.categid = thiscategid;

                                //var listAboThisSession = lChoices.SelectMany(a => a.listAbos.Where(x => x.listEvents.Any(s => s.sessionid == thissessionid && s.categid == thiscategid))).ToList();
                                List<customAbo> listAboThisSession = lChoices.SelectMany(d => d.listTarifs).SelectMany(r => r.listAbos).Where(x => x.listEvents.Any(s => s.sessionid == thissessionid && s.categid == thiscategid && s.zoneId == thiszoneid
                                                            && s.floorId == thisfloorid && s.sectionId == thissectionid)).ToList();
                                int nbrTotalEnAbo = listAboThisSession.Count; // total this categ/this session/this event

                                // places hors Abo
                                int nbrTotalHorsAbo = 0;
                                Dictionary<string, int> keyGpId_Nbr_HA = new Dictionary<string, int>();
                                List<customEventHA> listHA_thisSession = new List<customEventHA>();

                                if (objChoicesHA != null && objChoicesHA.listEventsHA != null)
                                {
                                    foreach (customEventHA choiceHA in objChoicesHA.listEventsHA)
                                    { // todo linqs ??
                                        if (choiceHA.sessionid == thissessionid && choiceHA.categid == thiscategid)
                                        {
                                            listHA_thisSession.Add(choiceHA);
                                            foreach (customTarifHA ot in choiceHA.listTarifs)
                                            {
                                                nbrTotalHorsAbo += ot.nb;
                                                string thisKey = ot.gpid.ToString();
                                                keyGpId_Nbr_HA.Add(thisKey, ot.nb);
                                            }
                                        }
                                    }

                                }
                                int nbrTotal = nbrTotalEnAbo + nbrTotalHorsAbo;


                                if (customBasketReturn.listEventsHA == null)
                                {
                                    customBasketReturn.listEventsHA = new List<customEventHA>();
                                }

                                if (customHAReturn.listTarifs == null)
                                {
                                    customHAReturn.listTarifs = new List<customTarifHA>();
                                }

                                if (nbrTotal > 0)
                                {
                                    Dictionary<string, int> keyNbrTarifFormulas = new Dictionary<string, int>();
                                    /// clés formules : formuleId/tarif/categ/zone/etage/section | nombre
                                    foreach (customAbo abo in listAboThisSession)
                                    {
                                        abo.identiteId = 0;
                                        abo.hk = "";
                                        // customBasketReturn.listFormulas.Add(abo);

                                        int thisformula = abo.formulaid;  //abo.Select(f => f.formulaid).First();
                                        int thistarif = abo.tarifid; //abo.Select(f => f.tarifid).First();
                                        string thisKey = thisformula.ToString() + "/" + thistarif.ToString() + "/" + thiscategid.ToString() + "/" + thiszoneid.ToString() + "/" + thisfloorid.ToString() + "/" + thissectionid.ToString();
                                        if (keyNbrTarifFormulas.Keys.Contains(thisKey))
                                        {
                                            keyNbrTarifFormulas[thisKey] += 1;
                                        }
                                        else
                                        {
                                            keyNbrTarifFormulas.Add(thisKey, 1);
                                        }
                                    }


                                    // ***************************** appel au flag des places **********************



                                    if (isAlgoMemePlace)
                                    {
                                        if (!flagMassifAboMemePlaceIsDone)
                                        {
                                            // flag les places sur toutes les séances de l'abo fermé.
                                            int thisformulaId = objChoices[0].formulaid;
                                            listSeatsmemePlace = wcfThemis.FlagAutoAboFermeMemePlace(structureId, thisformulaId, thiseventid, thissessionid, keyNbrTarifFormulas, keyGpId_Nbr_HA, nbrTotal, userId).ToList();

                                            if (listSeatsmemePlace.Count == 0)
                                            {
                                                logger.Error(@"/!\ algo même place remonte vide pour " + structureId + "," + thisformulaId + "," + thiseventid + "," + thissessionid + "," + thiscategid);
                                            }

                                            flagMassifAboMemePlaceIsDone = true;
                                        }
                                    }

                                    List<ws_DTO.SeatEntity> listSeats = new List<ws_DTO.SeatEntity>();
                                    if (isAlgoMemePlace && listSeatsmemePlace != null && listSeatsmemePlace.Count > 0 &&
                                        listSeatsmemePlace.Where(s => s.Manif_id == thiseventid && s.Seance_id == thissessionid).ToList().Count == nbrTotal
                                        )
                                    {
                                        listSeats = listSeatsmemePlace.Where(s => s.Manif_id == thiseventid && s.Seance_id == thissessionid).ToList();
                                    }
                                    else
                                    {
                                        // passe aussi ici si le flag "même place" a echouée => à voir
                                        listSeats = wcfThemis.FlagAuto(structureId, thiseventid, thissessionid, keyNbrTarifFormulas, keyGpId_Nbr_HA, nbrTotal, userId).ToList();
                                    }
                                    // ***************************** end appel au flag des places **********************

                                    if (listSeats.Count == nbrTotal) // ********* flag ok
                                    {
                                        int seatIdx = 0;

                                        EventFlage oEventFlage = new EventFlage
                                        {
                                            event_id = thiseventid,
                                            session_id = thissessionid,
                                            seatsFlagges = new List<int>()
                                        };
                                        foreach (ws_DTO.SeatEntity seat in listSeats)
                                        {
                                            oEventFlage.seatsFlagges.Add(seat.Seat_id);

                                        }
                                        listEventsFlages.Add(oEventFlage);


                                        foreach (ws_DTO.SeatEntity seat in listSeats)
                                        {
                                            customFlaggedSeat flagSeat = new customFlaggedSeat
                                            {
                                                seatid = seat.Seat_id,
                                                rank = seat.Rank,
                                                seat = seat.Seat,
                                                hashKey = getHash(seat.Seat_id + "/" + thissessionid, thisSalt)

                                            };

                                            Debug.WriteLine(flagSeat.seatid + " " + flagSeat.rank + " " + flagSeat.seat);

                                            if (listAboThisSession.Count > seatIdx)
                                            {   // une place par abo : on suit le compteur
                                                customEvent thisSession = listAboThisSession[seatIdx].listEvents.Where(p => p.sessionid == thissessionid && p.categid == thiscategid).FirstOrDefault();

                                                //thisSession.unitTTCamount = 

                                                thisSession.listSeats = new List<customFlaggedSeat>
                                                {
                                                    flagSeat
                                                };
                                            }
                                            else
                                            {
                                                foreach (customTarifHA cusTarifHA in listHA_thisSession[0].listTarifs)
                                                {
                                                    //customTarifHA cusTarifHA = choiceHA.listTarifs[0];
                                                    if (cusTarifHA.listSeats == null)
                                                    {
                                                        cusTarifHA.listSeats = new List<customFlaggedSeat>();
                                                    }
                                                    if (cusTarifHA.nb > cusTarifHA.listSeats.Count)
                                                    {

                                                        if (cusTarifHA.isPlacementLibre)
                                                        {
                                                            flagSeat.rank = "LIBRE";
                                                            flagSeat.seat = "LIBRE";
                                                            cusTarifHA.listSeats.Add(flagSeat);

                                                        }
                                                        else
                                                        {
                                                            cusTarifHA.listSeats.Add(flagSeat);

                                                        }

                                                        break;
                                                    }
                                                    //choiceHA
                                                }

                                            }
                                            seatIdx++;


                                            // se positionner sur le bon abo
                                        }
                                        //custCxHAReturn.ListHA.Add(cus)
                                        customHAReturn.listTarifs = new List<customTarifHA>();
                                        if (listHA_thisSession.Count > 0)
                                        {
                                            foreach (customTarifHA cusTarifHA in listHA_thisSession[0].listTarifs)
                                            {

                                                var sessionInBasketReturn = customBasketReturn.listEventsHA.Where(ha => ha.sessionid == listHA_thisSession[0].sessionid).FirstOrDefault();
                                                var tarifInBasketReturn = customBasketReturn.listEventsHA.SelectMany(ha => ha.listTarifs).ToList().Where(hat => hat.priceId == cusTarifHA.priceId).FirstOrDefault();

                                                //On ajout si c'est un tarif différent sur une séance différente
                                                if (sessionInBasketReturn == null && tarifInBasketReturn == null)
                                                {
                                                    customHAReturn.listTarifs.Add(cusTarifHA);
                                                }

                                                //si la séance est la même mais avec un tarif différent
                                                if (sessionInBasketReturn != null && tarifInBasketReturn == null)
                                                {
                                                    customHAReturn.listTarifs.Add(cusTarifHA);
                                                }

                                                //Si la séance est différente mais avec le même tarif
                                                if (sessionInBasketReturn == null && tarifInBasketReturn != null)
                                                {
                                                    customHAReturn.listTarifs.Add(cusTarifHA);
                                                }

                                            }
                                        }


                                    }
                                    else
                                    {
                                        // n'arrive pas à flagger !!!
                                        flagAllOk = false;

                                        customBasketReturn.messageError.Add(new CustomErrorsFlags() { EventName = dsess.Key.eventName, SessionDescription = dsess.Key.sessionDescription, CategoryName = dsess.Key.categoryName, EventId = dsess.Key.eventid, SessionId = dsess.Key.sessionid, CategoryId = dsess.Key.categid });
                                    }

                                }


                                if (customHAReturn.listTarifs.Count > 0)
                                {
                                    customBasketReturn.listEventsHA.Add(customHAReturn);
                                }
                                //custCxHAReturn.listEventsHA_recu.Add(customHAReturn);
                                //objChoices[0].ChoiceHA.ListHA.Add(listHA_thisSession);
                            } // session 
                        } // end foreach dsess in listDistinctsSessionsAbo
                    }
                    #endregion

                    customBasketReturn.listFormulas = lChoices;

                    if (!flagAllOk && listEventsFlages.Count > 0)
                    {   // flag echoué => deflag tout
                        foreach (EventFlage evt in listEventsFlages)
                        {
                            // wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                            List<ws_DTO.SeatEntity> listUnflaged = wcfThemis.UnFlagSeatsManuel_tempo(structureId, evt.event_id, evt.session_id, evt.seatsFlagges.ToArray(), userId).ToList();
                        }
                        // unFlag toutes les places flaggées

                    }

                    //objChoices[0].ChoiceHA = custCxHAReturn;

                    //customBasketReturn.listEventsHA = custCxHAReturn;

                    return Json(customBasketReturn, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    // todo error
                    return null;
                }
            }
            catch (JaugesLimitExceededException ex)
            {
                GestionTrace.WriteLogError(structureId, "error dans les jauges" + ex.Message);
                logger.Error("dans le check des jauges(" + structureId + "," + identiteId + ")" + ex.Message + " " + ex.StackTrace);
                throw;
            }
            catch (Exception ex)
            {
                //Flag(int structureId, int identiteId, customFormulas[] objChoices, customChoiceHA_recu objChoicesHA)
                GestionTrace.WriteLogError(structureId, "error dans Flag()!");
                logger.Error("dans Flag(" + structureId + "," + identiteId + ", onjChoices, objChoiceHA)" + ex.Message + " " + ex.StackTrace);
                throw ex;
            }



        }

        /// <summary>
        /// unflag les places prises sur tel evenement / session (click sur delete dans la liste des manifs)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [Throttle(Name = "UnFlag", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 5000)]
        public ActionResult unFlag(int structureId, int identiteId, int eventId, int sessionId)
        {
            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.DEBUG, "UNFlag start  " + identiteId + ", " + eventId + ", " + sessionId);

            GestionTrace.WriteLog(structureId, "UNFlag ('" + structureId + "," + identiteId + "," + eventId + "," + sessionId + ")");

            try
            {
                if (System.Web.HttpContext.Current.Session["currIdUser"] != null)
                {
                    int userId = int.Parse(System.Web.HttpContext.Current.Session["currIdUser"].ToString());
                    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                    bool resultat = wcfThemis.UnFlagSeats(structureId, eventId, sessionId, userId);

                    log.LogMsg(structureId, LogLevel.DEBUG, "UNFlag " + resultat);

                }

                return Json("ok", JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                GestionTrace.WriteLogError(structureId, "error dans UNFlag()! " + structureId + ", " + identiteId + ", " + eventId + ", " + ex.Message);
                logger.Error("dans UNFlag('" + structureId + "," + identiteId + "," + eventId + "," + sessionId + ")" + ex.Message + " " + ex.StackTrace);
                return null;
            }
        }

        private int cacheDelayS = 60 * 5;

        [Throttle(Name = "Load events", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        /// <summary>
        /// liste des manifs de la formule (avec les ids contrainte (=1 manif peut être dans plusieurs contraintes)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langCode"></param>
        /// <param name="formulaId"></param>
        /// <returns></returns>

        public ActionResult Load(int structureId, int identiteId, string langCode, string formulaId)
        {

            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "Load events start ('" + structureId + " " + langCode + " " + formulaId + ")");


            // formule:tarif1:tarif2:tarif3 etc...
            string[] arrformEtTarif = formulaId.Split(':');
            int iformulaId = int.Parse(arrformEtTarif[0]);
            List<int> listTarifs = new List<int>();
            for (int i = 1; i < arrformEtTarif.Length; i++)
            {
                listTarifs.Add(int.Parse(arrformEtTarif[i]));
            }
            if (listTarifs.Count == 0)
            {
                log.LogMsg(structureId, LogLevel.ERROR, "liste tarifs count=0 ?!? pour EventsController.Load(" + structureId + "," + identiteId + "," + langCode + "," + formulaId + ")");
            }

            string cacheName = "LoadEventsOfFormulas" + structureId + "." + identiteId + "_" + langCode + "_" + formulaId;
            List<ws_DTO.EventEntityOfFormula> listEvents = new List<ws_DTO.EventEntityOfFormula>();



            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                listEvents = wcfThemis.LoadEventsOfFormulas(structureId, langCode, identiteId, iformulaId, listTarifs.ToArray()).ToList();

                log.LogMsg(structureId, LogLevel.DEBUG, "listEvents  " + identiteId + ", " + listEvents.Count);
                GestionTrace.WriteLog(structureId, "listEvents  " + identiteId + ", " + listEvents.Count);

                if (listEvents.Count > 0)
                {
                    // calcul dispo totale sur l'evenements
                    foreach (ws_DTO.EventEntityOfFormula oevent in listEvents)
                    {

                        // int dispoEvent = oevent.ListSessionsOfFormula.Sum(sess => sess.dispo);
                        // oevent.dispo = dispoEvent;

                        foreach (SessionEntityOfFormula sess in oevent.ListSessionsOfFormula)
                        {
                            int dispoBySession = 0;

                            foreach (ws_DTO.ZoneEntity zone in sess.listZones)
                            {
                                int dispoByZone = 0;
                                foreach (ws_DTO.FloorEntity floor in zone.listFloors)
                                {

                                    foreach (ws_DTO.SectionEntity section in floor.listSections)
                                    {
                                        int dispoCateg = section.listCoupleTarifCateg.Sum(categ => categ.dispo);
                                        dispoByZone += dispoCateg;
                                        dispoBySession += dispoCateg;
                                    }
                                }
                                zone.Dispo = dispoByZone;
                            }
                            sess.dispo = dispoBySession;
                            // int dispoCateg = sess.listCoupleTarifCateg.Sum(categ => categ.dispo);

                            //sess.dispo = dispoCateg;
                        }
                    }
                    if (listEvents.Count == 0)
                    {
                        cacheDelayS = 20;
                    }

                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listEvents, GetUpdateCache(structureId), DateTime.Now.AddSeconds(cacheDelayS), TimeSpan.Zero);

                }
            }
            else
            {
                listEvents = (List<ws_DTO.EventEntityOfFormula>)System.Web.HttpContext.Current.Cache[cacheName];


                log.LogMsg(structureId, LogLevel.DEBUG, "listEvents cache " + identiteId + ", " + listEvents.Count);
                GestionTrace.WriteLog(structureId, "listEvents  cache" + identiteId + ", " + listEvents.Count);
            }

            FormulaWithEvents formulaWithEvent = new FormulaWithEvents
            {
                formulaId = iformulaId,
                listEvents = listEvents
            };

            return Json(formulaWithEvent, JsonRequestBehavior.AllowGet);
        }


        /// <summary>
        /// load tarifs evenement par evenement
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventid"></param>
        /// <param name="listTarifs"></param>
        /// <returns></returns>
        public ActionResult LoadAmounts(int structureId, int identiteId, string langCode, int eventid, int sessionid, string listTarifs)
        {
            mlogger log = new mlogger();
            log.LogMsg(structureId, LogLevel.DEBUG, "LoadAmounts start " + identiteId + ", " + eventid + " " + sessionid + " " + listTarifs);
            GestionTrace.WriteLog(structureId, "LoadAmounts start " + identiteId + ", " + eventid + " " + sessionid + " " + listTarifs);

            myDictionary mySSC = new myDictionary();
            mySSC = mySSC.GetDictionaryFromCache(structureId);

            // formue:tarif1:tarif2:tarif3 etc...
            if (listTarifs.EndsWith(":"))
            {
                listTarifs = listTarifs.Substring(0, listTarifs.Length - 1);
            }

            List<int> listTarifsId = new List<int>();

            if (!string.IsNullOrEmpty(listTarifs))
            {
                listTarifsId = listTarifs.Split(':').Select(int.Parse).ToList();
            }

            //string[] arrformEtTarif = listTarifs.Split(':');
            //int iformulaId = int.Parse(arrformEtTarif[0]);
            //List<int> listTarifs = new List<int>();
            //for (int i = 1; i < arrformEtTarif.Length; i++)
            //  {
            // listTarifs.Add(int.Parse(arrformEtTarif[i]));
            //   }


            List<int> listCateg = new List<int>();

            List<int> listSeances = new List<int>();
            if (sessionid > 0)
            {
                listSeances.Add(sessionid);
            }

            Debug.WriteLine("avant LoadAmounts " + eventid + " - " + DateTime.Now.ToString("hh:mm:ss ffff"));
            string cacheName = "LoadAmounts" + structureId + "." + identiteId + "_" + langCode + "_" + eventid + "/" + sessionid + "/" + listTarifs;

            //  List<ws_DTO.GestionPlaceEntity> listGps = new List<ws_DTO.GestionPlaceEntity>();

            List<ws_DTO.LigneGrilleTarifEntity> listGTs = new List<ws_DTO.LigneGrilleTarifEntity>();
            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                listGTs = wcfThemis.LoadAmounts(structureId, langCode, eventid, listSeances.ToArray(), listCateg.ToArray(), listTarifsId.ToArray()).ToList();
                //                listGTs = wcfThemis.LoadAllInternetGrilleTarifs(structureId, langCode, 0, 0, eventid, 0, listCateg.ToArray(), listTarifsId.ToArray(), offreIdHorsAbo).ToList();

                log.LogMsg(structureId, LogLevel.DEBUG, "LoadAmounts " + identiteId + ", " + listGTs.Count);
                GestionTrace.WriteLog(structureId, "LoadAmounts " + identiteId + ", " + listGTs.Count);

                if (listGTs != null)
                {
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listGTs, GetUpdateCache(structureId), DateTime.Now.AddSeconds(cacheDelayS), TimeSpan.Zero);
                }
            }
            else
            {
                listGTs = (List<ws_DTO.LigneGrilleTarifEntity>)System.Web.HttpContext.Current.Cache[cacheName];

                log.LogMsg(structureId, LogLevel.DEBUG, "LoadAmounts cache " + identiteId + ", " + listGTs.Count);
                GestionTrace.WriteLog(structureId, "LoadAmounts cache " + identiteId + ", " + listGTs.Count);

            }

            List<ws_DTO.LigneGrilleTarifEntity> listReturn = new List<ws_DTO.LigneGrilleTarifEntity>();
            foreach (ws_DTO.LigneGrilleTarifEntity gpE in listGTs)
            {
                if (listTarifsId.Contains(gpE.Price.PriceId))
                {
                    listReturn.Add(gpE);
                }
            }

            Debug.WriteLine("après LoadAmounts " + eventid + " - " + DateTime.Now.ToString("hh:mm:ss ffff"));

            log.LogMsg(structureId, LogLevel.DEBUG, "LoadAmounts listReturn" + identiteId + ", " + listReturn.Count);
            GestionTrace.WriteLog(structureId, "LoadAmounts listReturn" + identiteId + ", " + listReturn.Count);

            //FormulaWithEvents formulaWithEvent = new FormulaWithEvents();
            //formulaWithEvent.formulaId = iformulaId;
            //formulaWithEvent.listEvents = listEvents;

            return Json(listReturn, JsonRequestBehavior.AllowGet);

        }

        [Throttle(Name = "Flag", Message = "You must wait {n} seconds before accessing this url again.", milliSeconds = 500)]
        /// <summary>
        /// groupes de contraintes sur la formule
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identiteId"></param>
        /// <param name="langCode"></param>
        /// <param name="formulaId"></param>
        /// <returns></returns>
        public ActionResult LoadConstraintes(int structureId, int identiteId, string langCode, string formulaId)
        {

            Logger log = new Logger();
            log.LogMsg(structureId, LogLevel.INFO, "LoadConstraintes start ('" + structureId + " " + langCode + ")");
            GestionTrace.WriteLog(structureId, "LoadConstraintes start ('" + structureId + " " + langCode + ")");


            string[] arrformEtTarif = formulaId.Split(':');
            int iformulaId = int.Parse(arrformEtTarif[0]);

            string cacheName = "LoadConstraintes." + structureId + ".i" + identiteId + "_l" + langCode + "_f" + formulaId;

            List<ws_DTO.FormulaContraintesGroupeOpenEntity> listConstraints = new List<ws_DTO.FormulaContraintesGroupeOpenEntity>();
            if (System.Web.HttpContext.Current == null || System.Web.HttpContext.Current.Cache[cacheName] == null)
            {
                wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
                listConstraints = wcfThemis.LoadContraintesOfFormulas(structureId, langCode, identiteId, iformulaId).ToList();

                log.LogMsg(structureId, LogLevel.INFO, "listConstraints " + listConstraints.Count);
                GestionTrace.WriteLog(structureId, "Load listConstraints " + listConstraints.Count);

                if (listConstraints.Count > 0)
                {
                    System.Web.HttpContext.Current.Cache.Insert(cacheName, listConstraints, GetUpdateCache(structureId), DateTime.Now.AddSeconds(cacheDelayS), TimeSpan.Zero);
                }
            }
            else
            {
                listConstraints = (List<ws_DTO.FormulaContraintesGroupeOpenEntity>)System.Web.HttpContext.Current.Cache[cacheName];
                log.LogMsg(structureId, LogLevel.INFO, "listConstraints cache " + listConstraints.Count);
                GestionTrace.WriteLog(structureId, "listConstraints cache" + listConstraints.Count);
            }

            FormulaWithConstraints formWithConstraints = new FormulaWithConstraints
            {
                formulaId = iformulaId,
                listConstraints = listConstraints
            };

            return Json(formWithConstraints, JsonRequestBehavior.AllowGet);
        }

        //public ActionResult LoadPricesGridHA(int structureId, int identiteId, string langCode, int eventid, int sessionid, string listformulaId)
        //{
        //    //wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();
        //    //List<FormulaContraintesGroupeOpenEntity> listConstraints = wcfThemis.LoadContraintesOfFormulas(structureId, langCode, identiteId, iformulaId).ToList();

        //    //FormulaWithConstraints formWithConstraints = new FormulaWithConstraints();
        //    //formWithConstraints.formulaId = eventid;
        //    //formWithConstraints.listConstraints = listConstraints;

        //    //return Json(formWithConstraints, JsonRequestBehavior.AllowGet);

        //    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();



        //    return View();
        //}


        public ActionResult GetCurrentBasket(int structureId)
        {
            mlogger log = new mlogger();

            int identiteId = 0;
            if (Session["identiteId"] != null)
            {
                identiteId = int.Parse(Session["identiteId"].ToString());
            }

            BasketEntity thisCurrentBasket = HasBasketForIdentity(structureId);

            if (thisCurrentBasket.User != null)
            {
                Session["currIdUser"] = thisCurrentBasket.User.WebUserId;
            }



            //vérifier si le temps est inférieur a 20minutes
            //vérifier si les places sont tjrs flaggées


            CustomBasket custBasket = new CustomBasket();
            if (thisCurrentBasket.BasketId > 0)
            {
                try
                {
                    wcf_Themis.Iwcf_wsThemisClient wcfThemis = new wcf_Themis.Iwcf_wsThemisClient();

                    CultureInfo culture = new CultureInfo("en-US");
                    //string format = "MM/dd/yyyy hh:mm:sszzz";
                    //string format = "yyyy/MM/dd hh:mm:ss";
                    custBasket = wcfThemis.GetCurrentCustomBasketAbonnement(structureId, identiteId, thisCurrentBasket.BasketId);

                    custBasket.ReloadPage = true;
                    // custBasket.StrDateCreateBasket = thisCurrentBasket.Date_operation.ToString("G");
                    // custBasket.StrDateCreateBasket = thisCurrentBasket.Date_operation.ToString("u");
                    // custBasket.StrDateCreateBasket = thisCurrentBasket.Date_operation.ToString("yyyy-MM-dd HH:mm:ss");
                    custBasket.StrDateCreateBasket = thisCurrentBasket.Date_operation.ToString("yyyy-MM-ddTHH:mm:ss");

                    log.Debug(structureId, custBasket.StrDateCreateBasket);


                    //Mets le Haskey sur le siege
                    if (custBasket != null)
                    {
                        // getHash(tarif.priceId + "_" + tarif.vtsId + "_" + session.categid + "/" + session.sessionid, thisSalt)
                        custBasket.listFormulas.ForEach(fa => fa.listTarifs.ForEach(tarif => tarif.listAbos.ForEach(abo => abo.listEvents.ForEach(evt => evt.listSeats.ForEach(seat => seat.hashKey = getHash(seat.seatid + "/" + evt.sessionid, thisSalt))))));

                        custBasket.listEventsHA.ForEach(ha => ha.listTarifs.ForEach(tarif => tarif.hashKey = getHash(tarif.priceId + "_" + tarif.vtsId + "_" + ha.categid + "/" + ha.sessionid, thisSalt)));
                        custBasket.listEventsHA.ForEach(ha => ha.listTarifs.ForEach(tarif => tarif.listSeats.ForEach(seat => seat.hashKey = getHash(seat.seatid + "/" + ha.sessionid, thisSalt))));


                        //                        custBasket.listFormulas.ForEach(fa => fa.listTarifs.ForEach(tarif => tarif.listAbos.ForEach(abo => abo.listEvents.Select(evtt => evtt))));

                        //custBasket.listFormulas.ForEach(fa => fa.listTarifs.ForEach(tarif => tarif.listAbos.ForEach(abo => abo.listEvents.Where(evt => ev)
                        // custBasket.listEventsHA.Where(ha =>  custBasket.listFormulas.ForEach(fa => fa.listTarifs.ForEach(tarif => tarif.listAbos.ForEach(abo => abo.listEvents.Contains())));


                        //  custBasket.listFormulas.Where(xx => xx.listTarifs.ForEach(aa => aa.listAbos.ForEach(bb => bb.listEvents.Select(cc => cc.eventid).ToList().Contains(2))));
                        /*    custBasket.listFormulas.ForEach(x => x.listTarifs.ForEach(tarif => tarif.listAbos.ForEach(abo => abo.listEvents.Where(y => y.eventid == 1))));
                            var lstEvtsF = (from lf in custBasket.listFormulas
                                     from lt in lf.listTarifs
                                     from la in lt.listAbos
                                     from le in la.listEvents
                                     select le.eventid).ToList<int>();

                           var lstEvtsHA =  custBasket.listEventsHA.Select(ee => ee.eventid).ToList();
                           */

                        //  var test = custBasket.listEventsHA.Intersect(string.Join(",", gg.Select(x => x.ToString()))).Any()

                    }
                }
                catch (Exception ex)
                {
                    log.LogMsg(structureId, LogLevel.ERROR, "une erreur s'est produite pendant la récupération du panier" + ex.StackTrace);
                    GestionTrace.WriteLog(structureId, "une erreur s'est produite pendant la récupération du panier" + ex.Message);

                    return Json("danger:msg_error_get_basket", JsonRequestBehavior.AllowGet);
                }
            }

            // return Json(new { panier = thisCurrentBasket, formules = formulas, customBasket = custBasket }, JsonRequestBehavior.AllowGet);
            return Json(new { panier = thisCurrentBasket, customBasket = custBasket }, JsonRequestBehavior.AllowGet);
        }

    }
}