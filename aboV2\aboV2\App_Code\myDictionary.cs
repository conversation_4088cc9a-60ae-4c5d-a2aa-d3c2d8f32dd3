﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Xml;

namespace aboV2.App_Code
{
    public class myDictionary : DictionaryBase
    {
        # region Constantes pour logs dans WebTracing
        const string LogGenerique = "LOGGENERIQUE";
        const string LogLoadPage = "LOADPAGE";
        # endregion

        public String this[String key]
        {
            get
            {
                return ((String)Dictionary[key]);
            }
            set
            {
                Dictionary[key] = value;
            }
        }

        public ICollection Keys
        {
            get
            {
                return (Dictionary.Keys);
            }
        }

        public ICollection Values
        {
            get
            {
                return (Dictionary.Values);
            }
        }
        public void Add(String key, String value)
        {
            Dictionary.Add(key.ToUpper(), value);
        }

        public bool Contains(String key)
        {
            return (Dictionary.Contains(key));
        }

        public myDictionary GetDictionaryFromCache(int IdStructure)
        {
            if (HttpContext.Current.Cache["SVarThemisIniFile" + IdStructure.ToString()] != null)
            {
                return (myDictionary)HttpContext.Current.Cache["SVarThemisIniFile" + IdStructure.ToString()];
            }
            else
            {
                string IniFilePath = "";
                myDictionary mySSC = new myDictionary();
                string appPath = HttpContext.Current.Request.ApplicationPath;
                System.Configuration.Configuration WebThemis = System.Web.Configuration.WebConfigurationManager.OpenWebConfiguration(appPath);
                //  Debug.WriteLine("appConfig: " + WebThemis.FilePath);          // WJ 28.03.2013 

                if (0 < WebThemis.AppSettings.Settings.Count)
                {
                    System.Configuration.KeyValueConfigurationElement KeyThemisIniPath = WebThemis.AppSettings.Settings["ThemisIniPath"];
                    if (null != KeyThemisIniPath)
                        IniFilePath = KeyThemisIniPath.Value;
                    else
                    {
                        // WriteLog(Resources.Resource.MyKey14.ToString());
                    }
                }
                IniFilePath = IniFilePath.Replace("[idstructureSur4zeros]", IdStructure.ToString("0000"));
                //Debug.WriteLine("IniFilePath: " + IniFilePath);          // WJ 28.03.2013 

                System.Web.HttpContext.Current.Session["SvarIniFilePath"] = IniFilePath;
                string ConfigIniFilePath = IniFilePath;
                //WriteLog("ConfigIni: " + ConfigIniFilePath);
                XmlDocument xmlDocIni = new XmlDocument();
                xmlDocIni.Load(ConfigIniFilePath);

                foreach (XmlNode xmlNodeSection in xmlDocIni.SelectNodes("configIni/Section"))
                {
                    foreach (XmlNode xmlCle in xmlNodeSection.ChildNodes)
                    {
                        string cle = "";
                        cle = xmlNodeSection.Attributes["Name"].Value.ToString().ToUpper().Trim() + xmlCle.Name.ToUpper().Trim();
                        string valeur = "";
                        if (xmlCle.HasChildNodes)
                            valeur = xmlCle.FirstChild.Value;
                        mySSC.Add(cle, valeur);
                    }
                }
                if (mySSC.Count > 1)
                {
                    HttpContext.Current.Cache.Insert("SVarThemisIniFile" + IdStructure.ToString(), mySSC, null, DateTime.Now.AddSeconds(60), TimeSpan.Zero);
                    return mySSC;
                }
                else
                {
                    //WriteLog(Resources.Resource.MyKey15.ToString());
                    return null;
                }
            }

            //(MyDictionary)Session["SVarThemisIniFile"];
        }

        public myDictionary GetDictionary(int IdStructure)
        {
            #region Dictionnaire
            string IniFilePath = "";
            myDictionary mySSC = new myDictionary();
            if (System.Web.HttpContext.Current.Session["SVarThemisIniFile"] == null)
            { // Dictionnaire n'est pas rempli -> le remplir
                string appPath = HttpContext.Current.Request.ApplicationPath;
                Configuration WebThemis = System.Web.Configuration.WebConfigurationManager.OpenWebConfiguration(appPath);
                if (0 < WebThemis.AppSettings.Settings.Count)
                {
                    KeyValueConfigurationElement KeyThemisIniPath = WebThemis.AppSettings.Settings["ThemisIniPath"];
                    if (null != KeyThemisIniPath)
                        IniFilePath = KeyThemisIniPath.Value;
                    else
                    {
                        //WriteLog(Resource.ThemisiniPathexistepas);
                    }
                }
                IniFilePath = IniFilePath.Replace("[idstructureSur4zeros]", IdStructure.ToString("0000"));
                HttpContext.Current.Session["SvarIniFilePath"] = IniFilePath;
                string ConfigIniFilePath = IniFilePath;
                WriteLog("ConfigIni : " + ConfigIniFilePath);
                XmlDocument xmlDocIni = new XmlDocument();
                xmlDocIni.Load(ConfigIniFilePath);

                foreach (XmlNode xmlNodeSection in xmlDocIni.SelectNodes("configIni/Section"))
                {
                    foreach (XmlNode xmlCle in xmlNodeSection.ChildNodes)
                    {
                        string cle = "";
                        cle = xmlNodeSection.Attributes["Name"].Value.ToString().ToUpper().Trim() + xmlCle.Name.ToUpper().Trim();
                        string valeur = "";
                        if (xmlCle.HasChildNodes)
                            valeur = xmlCle.FirstChild.Value;
                        mySSC.Add(cle, valeur);
                    }
                }
                if (mySSC.Count > 1)
                    System.Web.HttpContext.Current.Session["SVarThemisIniFile"] = mySSC;
                //else
                   // WriteLog(Resource.ErreurLectureConfigIni);
            }
            else
            {
                mySSC = (myDictionary)System.Web.HttpContext.Current.Session["SVarThemisIniFile"];
            }
            #endregion
            return mySSC;
        }
        protected void WriteLog(string msg)
        {
            //GestionTrace gest = new GestionTrace();
            //gest.WriteLog("", msg, LogGenerique);
        }
    }
    
}