<?xml version="1.0" encoding="utf-8"?>
<page>
  <tableau>
    <Naming>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Civilité :</LABEL>
      <FORMAT>LISTNAMING</FORMAT>
      <TYPE>1,2</TYPE>
      <commentaire_forconsole>texte civilité + choix civilité</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte civilité + choix civilité (de)</commentaire_forconsole>
    </Naming>
    <Sex>
      <VISIBLE>false</VISIBLE>
      <OBLIGATOIRE>false</OBLIGATOIRE>
      <LABEL>Sexe :</LABEL>
      <FORMAT>TEXT1</FORMAT>
      <commentaire_forconsole>texte sexe </commentaire_forconsole>
      <commentaire_forconsole lg="de">texte sexe  (de)</commentaire_forconsole>
    </Sex>
    <FirstName>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Prénom (option):</LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
	  <CONTEXTUALHELP>Jean Eudes</CONTEXTUALHELP>
      <commentaire_forconsole>texte prenom </commentaire_forconsole>
      <commentaire_forconsole lg="de">texte prenom  (de)</commentaire_forconsole>
    </FirstName>
    <SurName>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Nom :</LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
	  <CONTEXTUALHELP>de la Tralalère</CONTEXTUALHELP>
      <commentaire_forconsole>texte nom </commentaire_forconsole>
      <commentaire_forconsole lg="de">texte nom  (de)</commentaire_forconsole>
    </SurName>
    <Pwd>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Mot de passe :</LABEL>
      <FORMAT>PWD</FORMAT>
      <commentaire_forconsole>texte mot de passe</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte mot de passe (de)</commentaire_forconsole>
    </Pwd>
    <PwdConfirme>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Confirmer le mot de passe :</LABEL>
      <FORMAT>PWD</FORMAT>
      <commentaire_forconsole>texte confirmation mot de passe</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte confirmation mot de passe (de)</commentaire_forconsole>
    </PwdConfirme>
    <DOB>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Date de naissance :</LABEL>
      <FORMAT>DATE</FORMAT>
      <commentaire_forconsole>texte date de naissance</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte texte date de naissance (de)</commentaire_forconsole>
    </DOB>
	<AddressGeo>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>false</OBLIGATOIRE>
      <LABEL>Adresse </LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
	  <CONTEXTUALHELP>Adress Géo</CONTEXTUALHELP>
      <commentaire_forconsole>texte adresse géo</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte adresse geo</commentaire_forconsole>
    </AddressGeo>
    <Address1>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Adresse 1&lt;span style='color:red'&gt; * &lt;&#47;span&gt; :</LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
      <commentaire_forconsole>texte adresse 1</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte adresse 1 (de)</commentaire_forconsole>
    </Address1>
    <Address2>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>false</OBLIGATOIRE>
      <LABEL>Adresse complément :</LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
      <commentaire_forconsole>texte adresse 2</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte adresse 2 (de)</commentaire_forconsole>
    </Address2>
    <PostalCode>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Code postal&lt;span style='color:red'&gt; * &lt;&#47;span&gt; :</LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
      <commentaire_forconsole>texte code postal</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte code postal(de)</commentaire_forconsole>
    </PostalCode>
    <City>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Ville&lt;span style='color:red'&gt; * &lt;&#47;span&gt; :</LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
      <commentaire_forconsole>texte ville</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte ville(de)</commentaire_forconsole>
    </City>
    <Country>
      <VISIBLE>false</VISIBLE>
      <OBLIGATOIRE>false</OBLIGATOIRE>
      <LABEL>Pays&lt;span style='color:red'&gt; * &lt;&#47;span&gt; :</LABEL>
      <FORMAT>TEXT50|UPPER</FORMAT>
      <commentaire_forconsole>texte pays</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte pays(de)</commentaire_forconsole>
    </Country>
    <Telephone>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>false</OBLIGATOIRE>
      <LABEL>Téléphone</LABEL>
      <FORMAT>TELEPHONE</FORMAT>
      <ATTRIBUT>PhoneNumber1</ATTRIBUT>
      <TYPE>03930001</TYPE>
      <commentaire_forconsole>texte telephone 1 + champ</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte telephone 1 (de)</commentaire_forconsole>
    </Telephone>
    <Fax>
      <VISIBLE>false</VISIBLE>
      <OBLIGATOIRE>false</OBLIGATOIRE>
      <LABEL>Fax</LABEL>
      <FORMAT>TELEPHONE</FORMAT>
      <ATTRIBUT>PhoneNumber2</ATTRIBUT>
      <TYPE>03930004</TYPE>
      <commentaire_forconsole>texte telephone 2 + champ</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte telephone 2 (de)</commentaire_forconsole>
    </Fax>
    <Portable>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Téléphone  &lt;span style='color:red'&gt; * &lt;&#47;span&gt; :</LABEL>
      <FORMAT>TELEPHONE</FORMAT>
      <ATTRIBUT>PhoneNumber3</ATTRIBUT>
      <TYPE>03930003</TYPE>
      <commentaire_forconsole>texte telephone 3 + champ</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte telephone 3 (de)</commentaire_forconsole>
    </Portable>
    <Email>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>E-mail &lt;span style='color:red'&gt; * &lt;&#47;span&gt; :</LABEL>
      <FORMAT>MAIL</FORMAT>
      <ATTRIBUT>PhoneNumber5</ATTRIBUT>
      <TYPE>03930005</TYPE>
      <commentaire_forconsole>texte telephone 5 + champ</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte telephone 5 (de)</commentaire_forconsole>
    </Email>
    <EmailConfirme>
      <VISIBLE>true</VISIBLE>
      <OBLIGATOIRE>true</OBLIGATOIRE>
      <LABEL>Confirmer e-mail &lt;span style='color:red'&gt; * &lt;&#47;span&gt; :</LABEL>
      <FORMAT>MAIL</FORMAT>
      <commentaire_forconsole>texte confirmer email</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte confirmer (de)</commentaire_forconsole>
    </EmailConfirme>
    <Comment>
      <VISIBLE>false</VISIBLE>
      <OBLIGATOIRE>false</OBLIGATOIRE>
      <LABEL>un petit mot ?</LABEL>
      <FORMAT>TEXT1000</FORMAT>
      <commentaire_forconsole>texte commentaire</commentaire_forconsole>
      <commentaire_forconsole lg="de">texte commentaire (de)</commentaire_forconsole>
    </Comment>
	<PhraseEntete>
      <LABEL>(&lt;span style='color:red'&gt; &lt;span style='color:red'&gt; * &lt;&#47;span&gt; &lt;&#47;span&gt;) Champs obligatoires</LABEL>
    </PhraseEntete>
	<InfoComps>

      <InfoComp>
        <VISIBLE>true</VISIBLE>
        <ENABLE>false</ENABLE>
        <OBLIGATOIRE>false</OBLIGATOIRE>
        <LABEL>JE SOUHAITE être informé des actualités et BONS PLANS</LABEL>
        <FORMAT>checkbox</FORMAT>
        <PRECHECK>false</PRECHECK>
		<TYPE>PART</TYPE>
        <ID>13</ID>
      </InfoComp>
        <InfoComp>
        <VISIBLE>true</VISIBLE>
        <ENABLE>true</ENABLE>
        <OBLIGATOIRE>true</OBLIGATOIRE>
        <LABEL>JE SOUHAITE être informé des actualités et BONS PLANS</LABEL>
        <FORMAT>checkbox</FORMAT>
        <PRECHECK>false</PRECHECK>
		<TYPE>PART</TYPE>
        <ID>136</ID>
      </InfoComp>
     
	  
    </InfoComps>	
	<cnil>		
        <VISIBLE>true</VISIBLE>
        <ENABLE>false</ENABLE>
        <OBLIGATOIRE>false</OBLIGATOIRE>
        <LABEL>Ces informations recueillies sont nécessaires pour vous adresser les documents qui faciliterons votre visite. Conformément à la loi « Informatique et Libertés » du 6 janvier 1978 modifiée en 2004, vous disposez d’un droit d’accès et de rectification aux informations vous concernant. Vous pouvez également, pour des motifs légitimes, vous opposer au traitement des données vous concernant.</LABEL>
        <FORMAT>label</FORMAT>
        <PRECHECK>true</PRECHECK>
		<TYPE>CNIL</TYPE>
        <ID></ID>      
	</cnil>
	<cgvs>		
		<cgv>
			<VISIBLE>true</VISIBLE>
			<ENABLE>false</ENABLE>
			<OBLIGATOIRE>true</OBLIGATOIRE>
			<LABEL>Conditions d'utilisation #1</LABEL>
			<FORMAT>CHECKBOX</FORMAT>
			<PRECHECK>true</PRECHECK>			
			
		</cgv>
		<cgv>
			<VISIBLE>true</VISIBLE>
			<ENABLE>false</ENABLE>
			<OBLIGATOIRE>true</OBLIGATOIRE>
			<LABEL>Conditions d'utilisation #2</LABEL>
			<FORMAT>label</FORMAT>
						
		</cgv>

	</cgvs>
	<captcha>		
     <VISIBLE>true</VISIBLE>
        <ENABLE>false</ENABLE>
        <OBLIGATOIRE>true</OBLIGATOIRE>
        <LABEL>je ne suis pas un robot</LABEL>
        <FORMAT>CHECKBOX</FORMAT>
        <PRECHECK>true</PRECHECK>
		<TYPE>CNIL</TYPE>
        <ID></ID>      
	</captcha>	

	
	<lbErreurEmailExisteDeja>
      <LABEL>Cet e-mail existe déjà. <br/>Cliquez sur "mot de passe oublié" pour recevoir votre mot de passe</LABEL>
    </lbErreurEmailExisteDeja>
	
    <BtnSave>
      <VISIBLE>true</VISIBLE>
      <LABEL>Sauvegarder</LABEL>
      <IMAGE></IMAGE>
      <IMAGE_ON></IMAGE_ON>
    </BtnSave>
	<BtnCancel>
      <VISIBLE>true</VISIBLE>
      <LABEL>Annuler</LABEL>
      <IMAGE></IMAGE>
      <IMAGE_ON></IMAGE_ON>
    </BtnCancel>
  </tableau>
</page>