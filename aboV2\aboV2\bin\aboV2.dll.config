﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
  </configSections>
  <connectionStrings>
	  <add name="WSAdminConnectionString" connectionString="Data Source=172.30.200.51;Initial Catalog=WSAdmin_test;User ID=SphereWebTest;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />

	  <add name="WebTracingConnectionString" connectionString="Data Source=************;Initial Catalog=WebTracing;User ID=SphereWeb;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />
	  <add name="WSAdminConnectionStringPROD" connectionString="Data Source=************;Initial Catalog=WSAdmin;User ID=SphereWeb;Password=************************************************************************************************************" providerName="System.Data.SqlClient" />

  </connectionStrings>
  <appSettings>
    <add key="vs:EnabledBrowserLink" value="false" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="TypeRun" value="TEST" />
    <add key="UpdateCache" value="\\Srv-paiement64\CUSTOMERFILES\TEST\updatecache.txt" />
    <add key="listWTsConns" value="\\**************\webservices\TEST\listConnexionsWTFlag\flagWTConnexions.flg" />
    <add key="Version" value="2.2.5" />
    <add key="ThemisIniPath" value="\\*************\customerfiles\TEST\[idstructureSur4zeros]\CONFIGSERVER\config.ini.xml" />
    <!-- TEST
      <add key="physicalPathOfJs" value="\\**************\sites\TEST\Customer\current\javascriptfiles\[idstructureSur4zeros]\"/>
    <add key="physicalPathOfImages" value="\\**************\customerfiles\TEST\[idstructureSur4zeros]\Customer\IMAGES\"/>
    <add key="physicalPathOfCss" value="\\**************\customerfiles\TEST\[idstructureSur4zeros]\Customer\CSS\[idstructureSur4zeros].css"/>
    
    -->
    <add key="physicalPathOfJs" value="D:\WORK\SITES\ABO\aboV2\Scripts\[idstructureSur4zeros]\" />
    <add key="physicalPathOfCustomPackageJs" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\CUSTOMPACKAGE\" />
    <add key="relativePathFilesJsOfSite" value="http://localhost:8602/files/" />
    <add key="relativePathJsOfSite" value="http://localhost:8602/Scripts/" />
    <!--<add key="relativePathJsOfSiteProd" value="https://abo.themisweb.fr/[idstructureSur4zeros]/Scripts/" />-->
    <add key="relativeFilesPathOfSite" value="http://localhost:8602/files/[idstructureSur4zeros]/ABO/images/" />
    <add key="physicalPathOfImages" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\IMAGES\" />
    <add key="physicalPathOfCss" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\CSS\[idstructureSur4zeros].css" />
    <add key="relativePathCssOfSite" value="http://localhost:8602/files/[idstructureSur4zeros]/ABO/CSS/" />
    <add key="physicalPathOfTranslateXml" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\RESOURCES\" />
    <!--<add key="relativePathOfTranslateXml" value="http://localhost:8602/files\[idstructureSur4zeros]\abo/resources/" />-->
    <add key="physicalPathOfBannerTemplate" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\TEMPLATES\banner.htm" />
    <add key="physicalPathOfSettingsJSON" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\APPSETINGS\appsettings.json" />
    <add key="physicalPathOfZapperZESJSON" value="D:\customerfiles\DEV\[idstructureSur4zeros]\Abo\APPSETINGS\zapperZES.json" />
    <add key="SitePaiement" value="https://payment.themisweb.fr/test/PaymentBis.aspx" />
    <add key="CryptoKey" value="RodWebShop95" />
    <add key="FileAttenteMaxActivesUsers" value="100000" />
    <add key="FileAttenteDelayInSeconds" value="300" />
    <add key="FileAttenteDelayInSecondsForOneStructDefaultValue" value="200" />
    <add key="FileAttenteMaxActivesUsersForOneStructDefaultValue" value="100" />
    <add key="FilesCommentairehead" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\filesinfos\commentairehead_[page].html" />
    <add key="FilesCommentairehaut" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\filesinfos\commentairehaut_[page].html" />
    <add key="FilesCommentairebas" value="D:\customerfiles\DEVT\[idstructureSur4zeros]\ABO\filesinfos\commentairebas_[page].html" />
    <add key="FilesCommentairehautDEV" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\filesinfos\commentairehaut_[page].html" />
    <add key="FilesCommentairebasDEV" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\filesinfos\commentairebas_[page].html" />
    <add key="physicalPathOfFooterTemplate" value="D:\customerfiles\DEV\[idstructureSur4zeros]\ABO\templates\footer.html" />
    <add key="path_script_sql" value="D:\WORK\SITES\ABO\aboV2\scriptsSql\[directory\][filename][.structureid].sql" />
	<add key="FILEATTENTEBYPASSKEY" value="overwriteFA" />
  </appSettings>
  <!--
    Pour obtenir une description des modifications de web.config, voir http://go.microsoft.com/fwlink/?LinkId=235367.

    Les attributs suivants peuvent être définis dans la balise <httpRuntime>.
      <system.Web>
        <httpRuntime targetFramework="4.7.1" />
      </system.Web>
  -->
  <system.web>
    <authentication mode="None" />
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.5.2" />
    <httpModules>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" />
    </httpModules>
  </system.web>
  <system.webServer>
    <modules>
      <remove name="FormsAuthentication" />
      <remove name="ApplicationInsightsWebTracking" />
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" />
    </modules>
    <validation validateIntegratedModeConfiguration="false" />
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.15.0" newVersion="2.0.15.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.2" newVersion="7.0.0.2" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:6 /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:14 /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <log4net debug="true">
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file type="log4net.Util.PatternString" value="D:\LOGS\Sites\ABO\aboV2.%property{structureId}.log" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="2000KB" />
      <staticLogFileName value="true" />
      <countDirection value="1" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss:fff}] - (%property{IP}) [%-5p] – %m%n" />
      </layout>
    </appender>
    <appender name="RollingFileAppenderComm" type="log4net.Appender.RollingFileAppender">
      <file type="log4net.Util.PatternString" value="D:\LOGS\Sites\ABO\aboV2.common.log" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="2000KB" />
      <staticLogFileName value="true" />
      <countDirection value="1" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss:fff}] - (%property{structureId}) (%property{ip}) [%-5p] – %m%n" />
      </layout>
    </appender>
    <root>
      <level value="ALL" />
      <appender-ref ref="RollingFileAppender" />
      <appender-ref ref="RollingFileAppenderComm" />
    </root>
  </log4net>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_Iwcf_wsThemis" sendTimeout="00:05:00" maxReceivedMessageSize="655360000" />
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://localhost:55660/wcf-wsThemis.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Iwcf_wsThemis" contract="wcf_Themis.Iwcf_wsThemis" name="BasicHttpBinding_Iwcf_wsThemis" />
      <!--<endpoint address="http://back-themis-ws/WS/wcfThemisV/2.0.66/wcf-wsThemis.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Iwcf_wsThemis" contract="wcf_Themis.Iwcf_wsThemis" name="BasicHttpBinding_Iwcf_wsThemis" />-->
      <!--<endpoint address="http://localhost:55660/wcf-wsThemis.svc" binding="basicHttpBinding"
    bindingConfiguration="BasicHttpBinding_Iwcf_wsThemis" contract="wcf_Themis.Iwcf_wsThemis"
    name="BasicHttpBinding_Iwcf_wsThemis" />-->
    </client>
  </system.serviceModel>
</configuration>