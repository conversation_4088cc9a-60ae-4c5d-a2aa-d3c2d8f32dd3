<p class="commentaireetape"><font color="#ff0000">ATTENTION !!! L'abonnement en ligne n'est pas ouvert aux&nbsp;personnes &agrave; mobilit&eacute; r&eacute;duite, aux Demandeurs d'emploi, ainsi qu'aux&nbsp;abonn&eacute;s blagnacais ou appartenant &agrave; une collectivit&eacute;, dans un de ces cas, merci de contacter le service aux horaires d'ouverture</font></p>

<p class="commentaireetape"><font color="#ff0000"><u>Le paiement s'effectue par Carte Visa ou Mastercard en euros.La transaction est s&eacute;curis&eacute;e. A partir d'un montant de&nbsp;150euros, le paiement en 3 fois sans frais est possible.</p>
</u></font>
<p>Afin de limiter les risques de fraude sur internet, li&eacute;s aux tentatives d'usurpation d'identit&eacute;, un nouveau protocole s&eacute;curis&eacute; de paiement par internet, le 3D-Secure, est mis en oeuvre lors de votre paiement.</p>

<p></p>

<p class="commentaireetape">Ainsi, apr&egrave;s avoir entr&eacute; votre num&eacute;ro de carte bancaire, une connexion sera &eacute;tablie avec le fournisseur de la carte pour que vous puissiez confirmer votre identit&eacute; en entrant un code (soit d&eacute;j&agrave; en votre possession, soit communiqu&eacute; imm&eacute;diatement par SMS via votre t&eacute;l&eacute;phone portable). Si l'authentification est r&eacute;ussie, alors le paiement par carte de cr&eacute;dit sera ex&eacute;cut&eacute;. Si vous rencontrez des probl&egrave;mes, contactez le fournisseur de votre carte de cr&eacute;dit ou votre banque.</p>

<p class="commentaireetape"><strong>Vous disposez d&eacute;j&agrave; d'un compte client, saisissez votre e-mail et votre mot de passe puis cliquez sur "Connexion"</strong></p>

<p class="commentaireetape"><strong>C'est votre premi&egrave;re commande sur notre site, cliquez sur "Cr&eacute;er un compte".</strong></p>

<p class="commentaireetape"><strong>&nbsp;Vous disposez d&eacute;j&agrave; d'un compte client mais&nbsp;vos donn&eacute;es personnelles sont &agrave; modifier, cliquez sur "Modifier profil".&nbsp; </strong></p>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-13']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    </script>

<script type="text/javascript">      var _gaq = _gaq || [];    _gaq.push(['_setAccount', 'UA-********-2']);    _gaq.push(['_trackPageview']);      (function() {      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);    })();    &lt;/script&gt;</body></html></body></html></body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html></body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html>&lt;/script&gt;</body></html></script>