/******* THEMIS V2 CSS *******/


/***  © 2016 Rodrigue S.A. ***/


/*****************************/


/****************************/


/********* GENERAL **********/


/****************************/

html {
    margin: 0px;
    padding: 0px;
    color: #000;
    min-height: 100%;
    position: relative;
}

body {
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 13px;
    line-height: 1.54;
    background: url(/files/0084/INDIV/images/fond_2015.jpg) repeat-y center top #fff;
    margin: 0 0 100px;
}

body.bodyPopIdentif {
    background-color: #fff;
}


/****************************/


/********** LAYOUT **********/


/****************************/

.nav-logo {
    width: 100%;
    max-width: 954px;
    margin: auto;
}

.navbar-toggle {
    border-radius: 0;
    border: 0;
    /*background-image: url("../images/menu_ico.png");*/
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
    float: left;
    margin: 10px 0px 0px 8px;
    display: block;
}

.navbar .lang {
    display: block;
    margin-top: -30px;
    float: right;
    background: transparent;
}

.navbar .lang ul {
    margin-bottom: 0px;
}

.navbar .lang li {
    display: inline;
}

.navbar .lang li a {
    padding: 12px;
    color: #ffffff;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 700;
    font-size: 14px;
}

.navbar .lang li:hover a,
.navbar .lang li a.active {
    background-color: #000000;
    text-decoration: none;
}

@media (min-width: 991px) {
    .menuLangDesktop {
        display: none;
    }
}

@media (max-width: 991px) {
    .lang {
        display: none !important;
    }
}

@media (min-width: 1028px) {
    .container {
        width: 970px;
    }
}

#ctl00_ContentPlaceHolder1_UpdatePanelChoixSeance {
    background: #fff;
}

#ctl00_imgMapSite {
    display: none;
}

#ctl00_imgBanner {
    display: none;
}

#ctl00_ContentPlaceHolder1_lkChoixPlacement {
    color: #fff;
    background: #e7305e;
}

@media (max-width: 340px) {
    .nav-logo {
        margin-left: 10px;
    }
    .navbar .lang li a {
        padding: 7px;
    }
}

@media (min-width: 992px) {
    #colMenu {
        float: left;
    }
    #colIndex {
        float: right;
    }
    #tableChoixSeance select {
        min-width: 120px;
        max-width: 230px;
        margin-left: 60px;
    }
}

#tableChoixSeance {
    margin-bottom: 15px;
}

#colIndex {
    /*background:#fff;*/
    /*max-width:750px;*/
    padding: 20px;
}


/*div.col-md-3 {
	padding:0px;
}*/

select.input-sm {
    height: 24px !important;
    line-height: 10px;
}

.form-group {
    margin-bottom: 5px;
}


/*********** END LAYOUT ********/


/*****************************/


/****** BANDEAU ETAPES *******/


/*****************************/

.crumbs {
    /*centering*/
    display: inline-block;
    overflow: hidden;
    border-radius: 0px;
    /*Lets add the numbers for each link using CSS counters. flag is the name of the counter. to be defined using counter-reset in the parent element of the links*/
    counter-reset: flag;
    width: 100%;
    background: #45b27f;
}

.crumbs a {
    text-decoration: none;
    outline: none;
    display: block;
    float: left;
    font-size: 1.1em;
    line-height: 36px;
    color: #fff;
    /*need more margin on the left of links to accomodate the numbers*/
    padding: 0 10px 0 60px;
    background: #666;
    background: linear-gradient(#666, #333);
    position: relative;
}


/*since the first link does not have a triangle before it we can reduce the left padding to make it look consistent with other links*/

.crumbs a:first-child {
    padding-left: 46px;
    border-radius: 0px;
    /*to match with the parent's radius*/
}

.crumbs a:first-child:before {
    left: 14px;
}

.crumbs a:last-child {
    border-radius: 0px;
    /*this was to prevent glitches on hover*/
    padding-right: 20px;
}


/*adding the arrows for the breadcrumbs using rotated pseudo elements*/

.crumbs a:after {
    content: '';
    position: absolute;
    top: 0;
    right: -18px;
    /*half of square's length*/
    /*same dimension as the line-height of .crumbs a */
    width: 36px;
    height: 36px;
    /*as you see the rotated square takes a larger height. which makes it tough to position it properly. So we are going to scale it down so that the diagonals become equal to the line-height of the link. We scale it to 70.7% because if square's: 
	length = 1; diagonal = (1^2 + 1^2)^0.5 = 1.414 (pythagoras theorem)
	if diagonal required = 1; length = 1/1.414 = 0.707*/
    transform: scale(0.707) rotate(45deg);
    /*we need to prevent the arrows from getting buried under the next link*/
    z-index: 1;
    /*background same as links but the gradient will be rotated to compensate with the transform applied*/
    background: #666;
    background: linear-gradient(135deg, #666, #333);
    /*stylish arrow design using box shadow*/
    box-shadow: 2px -2px 0 2px rgba(0, 0, 0, 0.4), 3px -3px 0 2px rgba(255, 255, 255, 0.1);
    /*
		5px - for rounded arrows and 
		50px - to prevent hover glitches on the border created using shadows*/
    border-radius: 0 5px 0 50px;
}


/*we dont need an arrow after the last link*/

.crumbs a:last-child:after {
    content: none;
}


/*we will use the :before element to show numbers*/

.crumbs a:before {
    content: counter(flag);
    counter-increment: flag;
    /*some styles now*/
    border-radius: 100%;
    width: 20px;
    height: 20px;
    line-height: 20px;
    margin: 8px 0;
    padding-left: 7px;
    position: absolute;
    top: 0;
    left: 30px;
    background: #444;
    background: linear-gradient(#444, #222);
    font-weight: bold;
    color: #000;
}

.flat a,
.flat a:after {
    background: #45b27f;
}

.flat a:before {
    background: #fff;
    box-shadow: 0 0 0 1px #fff;
}

.flat a.active,
.flat a.active:after {
    background: #e7305e;
}


/****** FIN BANDEAU ETAPES *******/


/**********************************/


/*** MENU BAS / FOOTER TEMPLATE ***/


/**********************************/

#footer {
    background-color: #000F1F;
    position: absolute;
    bottom: 0;
    width: 100%;
}

#footer .container {
    /*background-color: #2D2D2D;
	padding-right: 0px;
	padding-top: 0px;
	padding-left:0px;*/
}

#PlatformFooter {
    padding-bottom: 10px;
}

.MenuBottom {
    color: #fff;
    font-size: 12px;
    height: 45px;
    margin: 0px;
    text-align: center;
}

.MenuBottom a {
    color: #fff;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 1em;
}

.MenuBottom a:hover {
    color: #fff;
    text-decoration: none;
}

#ctl00_bullistnavigationnavig {
    padding-left: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
}

#poweredBy {
    display: inline-block;
    float: right;
}

#ctl00_templateFooter {
    display: inline-block;
    float: right;
    margin-top: -32px;
    padding-right: 20px;
}

#visa,
#disclaimer {
    text-align: center;
}

form {
    margin-bottom: 0px;
}

@media (max-width: 470px) {
    #PlatformFooter a {
        text-align: left;
        /*padding-top:50px;*/
    }
}


/*** FIN MENU BAS / FOOTER TEMPLATE ***/


/*****************************/


/********** BOUTONS **********/


/*****************************/

.invisible {
    display: none
}

.useless {
    display: none !important
}

.btBuy,
.btAction,
#ctl00_ContentPlaceHolder1_lkRetourCalendrier,
#ctl00_ContentPlaceHolder1_lkAjouterPanier,
#hlToPanier,
#ctl00_ContentPlaceHolder1_lkAjoutPlace,
#ctl00_ContentPlaceHolder1_lkEtapeSuivante,
#ctl00_ContentPlaceHolder1_lkRetourChoixSeance,
#ctl00_ContentPlaceHolder1_lkLogin,
#ctl00_ContentPlaceHolder1_lkNewAccount,
#ctl00_ContentPlaceHolder1_BtFiltre,
#ctl00_ContentPlaceHolder1_linkdisplayLogin,
#WctrlLoginConnect_BtnLogin,
#WctrlLoginConnect1_BtnLogin,
#WctrlLoginConnect_LinkBtnPassForget,
#WctrlLoginConnect1_LinkBtnPassForget,
#WctrLoginCreation_BtnCreate,
#WctrLoginCreation1_BtnCreate,
#WctrLoginCreation_BtnCancel,
#WctrLoginCreation1_BtnCancel,
#ctl00_ContentPlaceHolder1_ibRetourCalendrier,
#ctl00_ContentPlaceHolder1_ibAjouterPanier,
#WctrlLoginConnectPA1_BtnLogin,
#ctl00_ContentPlaceHolder1_lkRetourManifs,
.ui-dialog .ui-dialog-buttonpane button {
    display: inline-block;
    -moz-border-radius: 0px;
    border-radius: 2px;
    font-size: 14px;
    padding: 10px 25px 10px 25px;
    text-align: center;
    color: #fff;
    background: #e7305e;
    white-space: nowrap;
    margin: 0px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

.btBuy:hover,
#ctl00_ContentPlaceHolder1_hlToPlanSalle:hover,
#ctl00_ContentPlaceHolder1_lkRetourCalendrier:hover,
#ctl00_ContentPlaceHolder1_lkAjouterPanier:hover,
#ctl00_ContentPlaceHolder1_lkChoixPlacement:hover,
#imgBtBack:hover,
#hlToPanier:hover,
#ctl00_ContentPlaceHolder1_lkAjoutPlace:hover,
#ctl00_ContentPlaceHolder1_lkEtapeSuivante:hover,
#ctl00_ContentPlaceHolder1_lkRetourChoixSeance:hover,
#ctl00_ContentPlaceHolder1_lkLogin:hover,
#ctl00_ContentPlaceHolder1_lkNewAccount:hover,
#ctl00_ContentPlaceHolder1_BtFiltre:hover,
#ctl00_ContentPlaceHolder1_linkdisplayLogin:hover,
#WctrlLoginConnect_BtnLogin:hover,
#WctrlLoginConnect1_BtnLogin:hover,
#WctrlLoginConnect_LinkBtnPassForget:hover,
#WctrlLoginConnect1_LinkBtnPassForget:hover,
#WctrLoginCreation_BtnCreate:hover,
#WctrLoginCreation1_BtnCreate:hover,
#WctrLoginCreation_BtnCancel:hover,
#WctrLoginCreation1_BtnCancel:hover,
#ctl00_ContentPlaceHolder1_ibRetourCalendrier:hover,
#ctl00_ContentPlaceHolder1_ibAjouterPanier:hover,
#WctrlLoginConnectPA1_BtnLogin:hover,
#ctl00_ContentPlaceHolder1_lkRetourManifs:hover,
.ui-dialog .ui-dialog-buttonpane button:hover .btBuy:active,
#ctl00_ContentPlaceHolder1_hlToPlanSalle:active,
#ctl00_ContentPlaceHolder1_lkRetourCalendrier:active,
#ctl00_ContentPlaceHolder1_lkAjouterPanier:active,
#ctl00_ContentPlaceHolder1_lkChoixPlacement:active,
#imgBtBack:active,
#hlToPanier:active,
#ctl00_ContentPlaceHolder1_lkAjoutPlace:active,
#ctl00_ContentPlaceHolder1_lkEtapeSuivante:active,
#ctl00_ContentPlaceHolder1_lkRetourChoixSeance:active,
#ctl00_ContentPlaceHolder1_lkLogin:active,
#ctl00_ContentPlaceHolder1_lkNewAccount:active,
#ctl00_ContentPlaceHolder1_BtFiltre:active,
#ctl00_ContentPlaceHolder1_linkdisplayLogin:active,
#WctrlLoginConnect_BtnLogin:active,
#WctrlLoginConnect1_BtnLogin:active,
#WctrlLoginConnect_LinkBtnPassForget:active,
#WctrlLoginConnect1_LinkBtnPassForget:active,
#WctrLoginCreation_BtnCreate:active,
#WctrLoginCreation1_BtnCreate:active,
#WctrLoginCreation_BtnCancel:active,
#WctrLoginCreation1_BtnCancel:active,
#ctl00_ContentPlaceHolder1_ibRetourCalendrier:active,
#ctl00_ContentPlaceHolder1_ibAjouterPanier:active,
#WctrlLoginConnectPA1_BtnLogin:active,
#ctl00_ContentPlaceHolder1_lkRetourManifs:active,
.ui-dialog .ui-dialog-buttonpane button:active {
    background: #F5406D;
    color: #fff;
}

.btBuyComplet,
.btBuyComplet:hover {
    display: inline-block;
    -moz-border-radius: 0px;
    border-radius: 2px;
    font-size: 14px;
    padding: 10px 25px 10px 25px;
    text-align: center;
    color: #fff;
    background: #626262;
    white-space: nowrap;
    margin: 0px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

.eventFull {
    display: inline-block;
    -moz-border-radius: 0px;
    border-radius: 2px;
    font-size: 13px;
    padding: 10px 25px 10px 25px;
    text-align: center;
    color: #fff;
    background: #626262;
    white-space: nowrap;
    margin: 0px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    cursor: not-allowed;
}

#ctl00_ContentPlaceHolder1_hlToPlanSalle,
#ctl00_ContentPlaceHolder1_lkChoixPlacement {
    display: inline-block;
    -moz-border-radius: 0px;
    border-radius: 2px;
    font-size: 13px;
    padding: 10px 25px 10px 25px;
    text-align: center;
    color: #fff;
    background: #E00F27;
    white-space: nowrap;
    margin: 0px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

#ctl00_ContentPlaceHolder1_hlToPlanSalle {
    color: #ffffff;
    background: #333333;
    margin-right: 10px;
}

#ctl00_ContentPlaceHolder1_lkChoixPlacement {
    color: #fff;
    background: #E7305E;
}

#ctl00_ContentPlaceHolder1_lkChoixPlacement:hover {
    color: #fff;
    background: #E7305E;
}

#ctl00_ContentPlaceHolder1_lkRetourCalendrier {
    margin-left: -15px;
}


/*#ctl00_ContentPlaceHolder1_lkAjouterPanier {
	margin-left:155px;
}

#ctl00_ContentPlaceHolder1_lkEtapeSuivante {
	margin-left:100px;
}
*/

#ctl00_ContentPlaceHolder1_imgForCalendar {
    cursor: pointer;
}

a.calendar-link {
    font-size: 1em;
    background-color: #E00F27;
    color: #fff;
    padding: 2 5px;
    font-weight: normal;
}

.calendar-monthname th a {
    display: inline-block;
    padding: 2px 7px 2px 7px;
    background: #E00F27;
    border-radius: 8px;
    font-size: 1.2em;
    font-weight: bold;
    color: #fff;
    text-align: center;
    text-shadow: 0px 2px 1px rgba( 0, 0, 0, 0.3);
    cursor: pointer;
    margin: 2px;
}

.calendar-day-session {
    text-align: center;
    border: 1px solid #ECECEC;
    padding: 2px;
    background: #fff;
    font-weight: bold;
}

.calendar-day-no-session {
    text-align: center;
    border: 1px solid #ECECEC;
    padding: 2px;
    background: #fff;
    color: #A6A6A6;
}

.calendar-header-day {
    text-align: center;
    font-size: 1em;
    text-transform: capitalize;
}

@media (max-width: 650px) {
    #ctl00_ContentPlaceHolder1_imgForCalendar {
        display: none;
    }
}

@media (min-width: 650px) {
    span#ctl00_ContentPlaceHolder1_lblChoixSeance {
        visibility: hidden;
    }
}

@media (min-width: 650px) {
    #ctl00_ContentPlaceHolder1_ddlChoixSeanceJava {
        visibility: hidden;
    }
}

#WctrlLoginConnectPA1_BtnLogin {
    margin-top: 15px;
}

.alert-warning {
    background-color: #2D2D2D;
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#0087e6', endColorstr='#0087e6', GradientType=0);
    color: #fff;
    background-image: none !important;
    border: none;
    box-shadow: none;
    border-radius: 0px;
    font-size: 12px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 400;
    text-shadow: none;
}

img,
#visa img {
    image-rendering: optimizeQuality;
    -ms-interpolation-mode: bicubic;
}

#visa img {
    width: 20%
}

th {
    color: #020202;
    background-color: #f2f2f2;
}


/* Responsive */

@media (max-width: 990px) {
    #ctl00_ContentPlaceHolder1_hlToPlanSalle {
        margin-left: 0px;
        margin-top: 10px;
        display: none !important;
    }
    #ctl00_ContentPlaceHolder1_lkRetourCalendrier {
        margin-left: 0px;
    }
    #ctl00_ContentPlaceHolder1_lkAjouterPanier {
        margin-left: 0px;
        margin-top: 15px;
    }
}


/******* FIN BOUTONS *********/


/************/


/*** MENU ***/


/************/

#menu {
    color: #ffffff;
    background: none;
    padding: 0px 0px;
    margin-top: 20px;
}

#menu a {
    color: #000;
}

a#linkupdateLogin_inMenu,
a#linkdisconnect {
    color: #000;
}

a#linkconnectLoginPA_inMenu {
    color: fff;
}

a#linkconnectLoginPA_inMenu:hover {
    color: #F6FF00;
    text-decoration: none;
}

#menu li {
    color: #000;
}

a#linkconnectLogin_inMenu {
    color: #000;
    font-weight: bold;
}

#ctl00_basketdiv {
    margin-top: 10px;
    margin-bottom: 10px;
    color: #000;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 14px;
    padding: 10px;
}

#ctl00_authendiv {
    margin-top: 10px;
    margin-bottom: 20px;
    color: #000;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 14px;
    padding: 10px;
}

li#menuID,
li#menuName {
    color: #000;
    font-size: 14px;
    font-weight: 700;
    margin-left: 0px;
}

li#menuMail {
    text-transform: none;
    color: #000;
    font-size: 14px;
    font-weight: 400;
    margin-left: 0px;
    margin-top: -10px;
}

#MenuLiens {
    padding-bottom: 10px !important;
    margin-bottom: 15px;
    margin-top: -5px;
    font-size: 14px;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    padding-left: 10px;
}

#MenuLogin {
    padding-bottom: 10px !important;
    margin-bottom: 0px;
    padding-top: 10px !important;
    margin-top: -5px;
    font-size: 14px;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

#MenuPanier {
    padding-bottom: 10px !important;
    margin-bottom: 0px;
    margin-top: -5px;
    font-size: 14px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

#MenuInfosLiens {
    padding-bottom: 10px !important;
    margin-bottom: 10px;
    margin-top: -5px;
    font-size: 14px;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    padding-left: 10px;
}

#MenuLiens li {
    text-align: right;
    text-decoration: none;
    list-style: none;
    margin: 2px 15px;
    padding: 3px 0px;
    line-height: 1;
}

#MenuLogin li {
    text-decoration: none;
    margin: 0px 10px;
    padding: 0px 0px;
    line-height: 1;
    list-style-position: inside;
}

li#menuName,
li#menuMail,
li#menuLinkCodePromo {
    list-style: none;
}

a.codePromoActive {
    color: yellow !important;
    text-decoration: none !important;
}

li#menuLinkCodePromo {
    padding: 0px;
    margin: 0px;
}

li#menuLinkProfil,
li#menuLinkLogout {
    list-style-position: inside;
}

li#menuLinkProfil {
    margin-top: 10px;
}

li#menuLinkCadeaux,
li#menuListeManif {
    text-decoration: none;
    margin: 2px 15px;
    padding: 3px 0px;
    line-height: 1;
    list-style-position: inside;
}

#MenuPanier li {
    text-decoration: none;
    margin: 0px 10px;
    padding: 0px 0px;
    line-height: 1;
}

#MenuInfosLiens li {
    text-decoration: none;
    list-style: none;
    margin: 2px 15px;
    padding: 3px 0px;
    line-height: 1;
}

#MenuInfosLiens a {
    color: #000;
}

a#linkRetourListeManif {
    color: #000;
    text-decoration: none;
    line-height: 1;
    font-weight: bold;
}

li#MenuPanier {
    color: #fff;
    padding-top: 20px;
    padding-bottom: 20px;
}

li#MenuPanierPlein {
    color: #fff;
    padding-top: 20px;
    padding-bottom: 0px;
}

#titleMenuLiens {
    padding-left: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 20px;
    font-size: 13px;
    font-weight: 400;
    color: #fff;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    background: #231F20;
}

#titleMenuLogin {
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 10px;
    font-size: 18px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    color: #000;
    border-top: 5px solid #000f1f;
}

#titleMenuPanier {
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 10px;
    font-size: 18px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    color: #000;
    border-top: 5px solid #000f1f;
}

#titleMenuCodePromo {
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 10px;
    font-size: 16px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    color: #000;
    border-top: 5px solid #000f1f;
}

#titleMenuInfos {
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 20px;
    font-size: 20px;
    font-weight: 400;
    color: #000;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    background: #aeafb0;
}

span#voirPanier {
    font-size: 14px;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    color: #fff;
}

a#voirPanier {
    font-size: 14px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    color: #000;
}

@media (max-width: 990px) {
    span.basketcontain {
        padding-left: 15px;
    }
}

li#menuPanierBillets {
    color: #000;
    font-size: 14px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 700;
    padding-bottom: 10px;
    padding-top: 15px;
    text-decoration: none;
    list-style: none;
    line-height: 1;
}

#ctl00_authendiv {
    background: #45b27f;
}

#ctl00_basketdiv {
    background: #e7305e;
}

.slicknav_menu {
    background: none;
    color: #fff !important;
}

.slicknav_nav .slicknav_row:hover {
    background: #fff;
    color: #2d2d2d;
}

.slicknav_nav a:hover {
    background: #fff;
    color: #2d2d2d;
    text-decoration: none;
}

.slicknav_nav a {
    padding: 5px 10px;
    margin: 2px 5px;
    text-decoration: none;
    color: #fff;
}

.slicknav_nav {
    font-size: 1em;
    color: #fff !important;
}

@media (max-width: 990px) {
    .forslick {
        margin-top: -40px;
        margin-left: 10px;
    }
}

.slicknav_btn {
    margin: 55px 5px 6px;
    text-decoration: none;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    background-color: #222222;
}


/*************************/


/*** PAGE LISTE MANIFS ***/


/*************************/


/* Filtres Manifs */

#TopListeManif {
    margin-bottom: 20px;
    padding-top: 10px;
    padding-left: 20px;
    margin-right: -20px;
    padding-right: 20px;
}

div#divfiltres {
    width: 100%;
    /*margin-left:20px;*/
    display: none;
}

.chosen-container {
    /*padding-right:30px;
	/*margin-top:10px;*/
}

#ctl00_ContentPlaceHolder1_ddlFiltreGroupe_chosen,
#ctl00_ContentPlaceHolder1_ddlFiltreCible_chosen,
#ctl00_ContentPlaceHolder1_ddlFiltreGenre_chosen,
#ctl00_ContentPlaceHolder1_ddlFiltreSousGenre_chosen {
    display: none;
}


/*#fListeManifsHaut {
	background:#fff;
	margin-left:-20px;
	margin-top:-20px;
	padding-left:20px;
	padding-top:20px;
	padding-bottom:7px;
	margin-bottom:20px;
	margin-right:-20px;
	padding-right:20px;
}*/

@media (min-width: 755px) and (max-width: 990px) {
    #fListeManifsHaut,
    #TopListeManif {
        margin-right: -40px;
    }
    #PlatformFooter {
        margin-right: 0px;
    }
}

#ctl00_ContentPlaceHolder1_lbFiltreNomSeance {
    color: #7fa400;
    font-size: 12px;
    margin: 5px 0;
    font-weight: 700;
    text-transform: uppercase;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

#ctl00_ContentPlaceHolder1_lbFiltre {
    display: inline !important;
    color: #7fa400;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    font-family: Verdana;
}

#ctl00_ContentPlaceHolder1_tblEventsList {
    width: 100%;
    background: #fff;
}

.tdMois {
    display: block;
    background: #012f6b;
    text-transform: uppercase;
    height: 30px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

tr.EnteteMois {
    font-size: 14px;
    font-weight: normal;
    text-transform: uppercase;
}

.tdLogo img {
    display: block;
    clear: both;
    float: left;
    padding-right: 15px;
    max-width: 200px;
    height: 90px;
    /*border: solid 1px #000;*/
    /*box-shadow: -3px 3px 5px rgba( 0, 0, 0, 0.5),
                0 -3px 0 rgba( 255, 255, 255, 0.4);*/
}

.tdLogo img:hover {
    opacity: 0.4;
    filter: alpha(opacity=40);
    /* For IE8 and earlier*/
}

td.tdEvent {
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 15px;
    color: #666;
}

.tdEventDate {
    color: #000;
    font-weight: bold;
}

.tdEventDate h4 {
    color: #012f6b;
    font-size: 1em;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    margin-top: 5px;
    margin-bottom: 0px;
}

.tdLieu h4 {
    color: #012f6b;
    font-size: 0.9em;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    margin-top: 10px;
    margin-bottom: 0px;
}

.tdEventDate .DateTime {
    text-transform: capitalize;
}

.tdEventName h2 {
    color: #45B27F;
    font-size: 20px;
    margin-left: 0px;
    font-weight: 700;
    margin-top: 0px;
    margin-bottom: 0px;
    text-transform: uppercase;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

.tdEventName h2:hover {
    text-decoration: underline;
}

.tdEventName h4 {
    color: #45B27F;
    font-size: 12px;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

.trForNomEtLogoImg {
    display: inline-block;
    margin-top: 10px;
    padding-right: 10px;
}

.tdMois h4 {
    color: #fff;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 1.3em;
    padding-left: 20px;
    margin: 0px;
}

div.tdBtBuy,
div.eventFull {
    display: inline-block;
    float: right;
    margin-top: -55px;
}

@media (max-width: 650px) {
    div.tdBtBuy,
    div.eventFull {
        margin-top: 0px;
    }
}

.tdBtBuy {
    padding: 0px;
}

div.tdEventDate {
    display: inline-block;
}

.trEvent td {
    padding-bottom: 10px;
}

.typeTheme {
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 400;
    font-size: 12px;
    color: #7d7d7d;
    margin-bottom: 5px;
}

.tooltip-arrow {
    display: none;
}

i.fa.fa-info-circle {
    color: #3A3A3A;
}

.sep_title {
    border-bottom: 3px solid #dadada;
    width: 50px;
    display: block;
    margin-top: 10px;
    margin-left: 150px;
    margin-bottom: 5px;
}


/*** END LISTE MANIFS ***/


/*************************/


/*** PAGE CHOIX SEANCE ***/


/*************************/

.trentete td,
.GrilleTarifDateSeance th {
    background: #fff;
    color: #000;
    font-size: 14px;
}

.table>tbody>tr>th {
    border: 1px solid #ddd;
    padding-bottom: 10px;
}

#divForImgPlan {
    display: block;
    /*margin-top:20px;*/
}

#pano {
    width: 100%;
    min-height: 360px !important;
    height: 360px;
    overflow: hidden;
}

#divForEventPlusDinfos {
    max-width: 400px;
    margin-bottom: 20px;
}

#eventInfosResume {
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 400;
    font-size: 12px;
    color: #2d2d2d;
    margin-top: 20px;
}

.map {
    margin: 5px auto;
}


/* #tableTotauxfChoixSeance td, #tableTotauxfChoixSeance th {
	text-align: center;
	background: #E7E7E7;
} */

table.tblpriceslist {
    width: 100%;
    margin-top: 30px;
    background: #fff;
    margin-bottom: 0px;
}

.table>thead>tr>td.active,
.table>tbody>tr>td.active,
.table>tfoot>tr>td.active,
.table>thead>tr>th.active,
.table>tbody>tr>th.active,
.table>tfoot>tr>th.active,
.table>thead>tr.active>td,
.table>tbody>tr.active>td,
.table>tfoot>tr.active>td,
.table>thead>tr.active>th,
.table>tbody>tr.active>th,
.table>tfoot>tr.active>th {
    background: none;
}


/* #tableTotauxfChoixSeance {
	width:100%;
	font-weight:bold;
	margin-bottom:30px;
} */

@media (min-width: 468px) {
    #divForImgPlan,
    #big {
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
    }
}

@media (max-width: 680px) {
    #divForImgPlan,
    #big {
        display: none !important;
    }
    #tableTotauxfChoixSeance {
        width: 100%;
    }
}

#ctl00_ContentPlaceHolder1_imgLogoManif {
    margin-bottom: 20px;
}

#ctl00_ContentPlaceHolder1_lblManif {
    color: #333333;
    font-size: 1.8em;
    margin-left: 0px;
    font-weight: 700;
    margin-top: 0px;
    margin-bottom: 0px;
}

.trForNomEtLogo {
    display: inline-block;
    vertical-align: top;
    margin-top: 5px;
    padding-left: 10px;
}

.tdDdlSeatsCount,
.tdPriceName,
.tdTotalAmount {
    padding-left: 20px !important;
    padding-bottom: 5px;
    padding-top: 10px;
    font-size: 12px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 400;
    color: #2d2d2d;
}

@media (max-width:450px) {
    .tdDdlSeatsCount,
    .tdPriceName,
    .tdTotalAmount {
        padding-left: 5px !important;
        text-align: center;
    }
}

@media (max-width:450px) {
    a#seeMyPlace {
        display: none !important;
    }
}

.tdseatdetail {
    font-size: 12px;
    padding-top: 0px;
    padding-bottom: 5px;
    font-weight: 400;
    color: #2d2d2d;
}

table.tableglobalechoixseance {
    max-width: 760px;
}

.input-sm,
select.input-sm {
    height: 30px;
    padding-bottom: 0px;
    padding-top: 0px;
}

#tableTotauxfChoixSeance {
    margin-top: 7px;
    max-width: 106.2%;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 12px;
    font-weight: 700;
    color: #2d2d2d;
    margin-bottom: 10px;
}

#tableTotauxfChoixSeance th {
    color: #2d2d2d;
    background: #e7e7e7;
}

th.totalPrix {
    width: 210px;
}

@media (min-width: 748px) {
    th.totalNbr {
        width: 433px !important;
    }
}


/*tr.GrilleTarifDateSeance {
	display:none;
}*/

@media (max-width: 470px) {
    #ctl00_ContentPlaceHolder1_divForGrilleTarifHtml {
        margin-left: -20px;
    }
}

#ctl00_ContentPlaceHolder1_CustomValidator1 {
    display: none;
}

@media (max-width: 680px) {
    .table {
        margin-bottom: 0px;
    }
}

#ctl00_ContentPlaceHolder1_divForGrilleTarifHtml {
    margin-top: 20px;
    margin-bottom: -10px;
}


/*** END CHOIX SEANCE ***/

.ui-dialog .ui-dialog-titlebar {
    font-size: 1.1em;
    font-weight: bold;
    padding: 5px;
    color: #fff;
    background: #3A3A3A;
}

.ui-corner-all {
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
}

span.categ {
    display: block;
    width: 50px;
    height: 20px;
}

.TableGrilleTarif {
    border-collapse: separate;
    border-spacing: 1px;
    width: auto;
    margin-left: 0px;
    margin-right: 0px;
    /* margin-top:0px;*/
    margin-bottom: 15px;
    font-size: 12px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    background-color: #fff;
}

tr#GrilleTarifName {
    background: #4E4E4E;
    font-weight: 700;
}

td.GrilleTarifName {
    color: #fff;
    border: 1px solid #e7e7e7;
}

td#GrilleTarifNameVide {
    background: #fff;
    border: none;
}

.GtCategName {
    white-space: nowrap;
    background: #222222;
    color: #fff;
    font-weight: 400;
    min-width: 100px;
}

@media (max-width: 470px) {
    .GtCategName {
        min-width: 0px;
    }
}

.GtEtageName {
    background: #3a3a3a;
    color: fff;
    font-weight: 700;
}

.GtEtageTarifs {
    background: #e7e7e7;
    border: 1px solid #e7e7e7;
}

#TableGrilleTarifs {
    font-size: 12px;
    text-align: center;
}

.colonneGrilleTarifCategName {
    border: 1px solid #e7e7e7;
    background-color: #DFDFDF;
}

.colonneGrilleTarifCategName:empty {
    background-color: #F1F1F1;
}

#ctl00_ContentPlaceHolder1_UpdatePanelChoixSeance {
    background: #fff;
    margin-left: -20px;
    margin-top: -20px;
    padding-left: 20px;
    padding-top: 5px;
    padding-bottom: 25px;
    margin-right: -20px;
    padding-right: 20px;
}


/* .GrilleTarifName{
	font-size:0.9em;
}

.GrilleTarifCategName{
	font-size:0.9em;
} */

.c1 {
    background-color: #cb5f5f;
}

.c2 {
    background-color: #f7f7a1;
}

.c3 {
    background-color: #7dad87;
}

.tdGtAmount {
    text-align: right;
}

tr.trentete.active th {
    padding-left: 18px;
}

#ctl00_ContentPlaceHolder1_lblTitreTotalNbr {
    padding-left: 15px;
}

#ctl00_ContentPlaceHolder1_lblTitreTotal {
    padding-right: 2px;
}


/*td.tdDdlSeatsCount{
	min-width:220px;
}*/

.form-horizontal .control-label {
    text-align: left;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 12px;
    font-weight: 700;
    color: #2d2d2d;
    white-space: nowrap;
}

#ctl00_ContentPlaceHolder1_lblChoixSeance,
#ctl00_ContentPlaceHolder1_lblEtage,
#ctl00_ContentPlaceHolder1_lblCateg {
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 12px;
    font-weight: 700;
    color: #2d2d2d;
    padding-right: 80px;
    white-space: nowrap;
}

#ctl00_ContentPlaceHolder1_ddlCategorieJava {
    margin-left: 30px;
}

#ctl00_ContentPlaceHolder1_ddlCategorieJava {
    margin-left: 30px;
}

#ctl00_ContentPlaceHolder1_ddlChoixSeanceJava {
    margin-left: 15px;
}

@media (max-width: 991px) {
    #ctl00_ContentPlaceHolder1_ddlCategorieJava,
    #ctl00_ContentPlaceHolder1_ddlChoixSeanceJava {
        margin-left: 0px;
    }
}

.bad {
    color: red;
}


/**********************/


/*** CHOIX SUR PLAN ***/


/**********************/

#divForAdress {
    display: none
}

body.bodyPopPlanSalle,
body#planSalle {
    color: #333;
    font-size: 14px;
    font-family: Verdana, Lucida, sans-serif;
    background: #eee;
    /* Old browsers */
}

.top {
    color: #666;
    width: 100%;
    left: 0px;
    overflow: visible;
    bottom: 100px;
}

.bottom {
    position: fixed;
    height: 0px;
    background: white;
    bottom: 0;
    width: 0%;
    background: none;
}

#emplacementPanier {
    margin: 0px 20px 0px auto;
    background-color: #ddd;
    width: 420px;
    position: relative;
    z-index: 10;
    padding: 5px;
    cursor: move;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12) !important;
}

div#emplacementPanier li {
    font-weight: 400;
    color: #2D2D2D;
    font-size: 12px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
}

#emplacementPanier li a.btn {
    margin-top: 5px;
    margin-left: 10px;
    margin-bottom: 5px;
}

#emplacementPanier li a {
    display: inline-block;
    -moz-border-radius: 2px;
    border-radius: 2px;
    font-size: 1em;
    padding: 5px 25px 5px 25px;
    text-align: center;
    color: #333333;
    background: none;
    white-space: nowrap;
    margin: 0px;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    border: 1px solid #333333;
}

#validation a {
    display: inline-block;
    -moz-border-radius: 0px;
    border-radius: 2px;
    font-size: 1em;
    padding: 5px 25px 5px 25px;
    text-align: center;
    color: #ffffff;
    background: #333333;
    white-space: nowrap;
    margin-top: 10px;
    font-weight: 400;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    border: none;
}

#emplacementPanier li a:hover {
    background: #E10D3A;
    color: #fff;
    border: 1px solid #E10D3A;
}

#validation a:hover {
    background: #0DE16B;
    color: #fff;
}

#divForAdress {
    text-align: center;
    margin: 10px;
}

#divForAdress br {
    display: none
}

#divPlanSalle {
    position: absolute;
    /*position: initial !important;*/
    display: table-cell;
    /*zoom:0.65;*/
}

.lbTitreManif {}


/* positionner le siege */

.textrang,
.poteau {
    margin-top: -200px;
    margin-left: 100px;
}

.seatimage.perspect {
    margin-top: -5px;
    margin-left: -8px;
}

.ps.seatimage.free {
    cursor: pointer;
    cursor: hand
}


/*.seatimage.perspect.free{
    width: 14px;
    height: 14px;
    display: inline-block;
    margin: 0 0 0 7px;
    border-radius: 2px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    cursor: pointer;
    padding: 0 !important;
    position: relative;
    margin: 32px 16px 0px -7px;

    background: url("https://www.themisweb.fr/images/default/seats/square_blue.png") no-repeat;
}*/

.seatimage.perspect.free {
    display: inline-block;
    cursor: pointer;
    padding: 0 !important;
    position: relative;
}

.seatimage {
    overflow: auto;
}

span.seatimage.legend {
    width: 15px;
    height: 15px;
    display: inline-block;
    margin: 0px 5px 0px 3px;
}

.seatimage.perspect {
    margin-top: -5px;
    margin-left: -1px;
    width: 8px;
    height: 8px;
    border: 1px solid #fff;
    border-radius: 10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    -khtml-border-radius: 10px;
    border-top-left-radius: 10px;
}

.seatimage.perspect.pris {
    margin-top: -17px;
    margin-left: -12px;
    width: 28px;
    height: 28px;
    border-radius: 0px;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    -khtml-border-radius: 0px;
    border-top-left-radius: 0px;
}

.seatimage.perspect.free,
.seatimage.free,
.seatimage.legend,
.seatimage.perspect.free,
.seatimage.free,
.seatimage.legend {
    /* background: url("https://www.themisweb.fr/images/default/seats/square_blue.png") no-repeat;*/
    background: #5CA4FF;
}

.seatimage.perspect.free.categNo1,
.seatimage.categNo1.free,
.seatimage.legend.categNo1 {
    /*background: url("https://www.themisweb.fr/images/billes/pastille_cyan.gif") no-repeat;*/
    background: rgb(0, 204, 197);
    /* Old browsers */
    background: -moz-linear-gradient(top, rgb(0, 204, 197) 0%, rgb(0, 156, 158) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgb(0, 204, 197)), color-stop(100%, rgb(0, 156, 158)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgb(0, 204, 197) 0%, rgb(0, 156, 158) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgb(0, 204, 197) 0%, rgb(0, 156, 158) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgb(0, 204, 197) 0%, rgb(0, 156, 158) 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, rgb(0, 204, 197) 0%, rgb(0, 156, 158) 100%);
    /* W3C */
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#00ccc5', endColorstr='#009c9e', GradientType=0);
    /* IE6-9 */
}

.seatimage.perspect.categNo2.free,
.seatimage.categNo2.free,
.seatimage.legend.categNo2 {
    /*background: url("https://www.themisweb.fr/images/billes/pastille_rose.gif") no-repeat;*/
    background: rgb(251, 131, 250);
    /* Old browsers */
    background: -moz-linear-gradient(top, rgba(251, 131, 250, 1) 0%, rgba(233, 60, 236, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(251, 131, 250, 1)), color-stop(100%, rgba(233, 60, 236, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(251, 131, 250, 1) 0%, rgba(233, 60, 236, 1) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(251, 131, 250, 1) 0%, rgba(233, 60, 236, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(251, 131, 250, 1) 0%, rgba(233, 60, 236, 1) 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, rgba(251, 131, 250, 1) 0%, rgba(233, 60, 236, 1) 100%);
    /* W3C */
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#fb83fa', endColorstr='#e93cec', GradientType=0);
    /* IE6-9 */
}

.seatimage.perspect.categNo3.free,
.seatimage.categNo3.free,
.seatimage.legend.categNo3 {
    /*background: url("https://www.themisweb.fr/images/billes/pastille_jaune.gif") no-repeat;*/
    background: rgb(252, 234, 187);
    /* Old browsers */
    background: -moz-linear-gradient(top, rgba(252, 234, 187, 1) 0%, rgba(252, 205, 77, 1) 50%, rgba(248, 181, 0, 1) 51%, rgba(251, 223, 147, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(252, 234, 187, 1)), color-stop(50%, rgba(252, 205, 77, 1)), color-stop(51%, rgba(248, 181, 0, 1)), color-stop(100%, rgba(251, 223, 147, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(252, 234, 187, 1) 0%, rgba(252, 205, 77, 1) 50%, rgba(248, 181, 0, 1) 51%, rgba(251, 223, 147, 1) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(252, 234, 187, 1) 0%, rgba(252, 205, 77, 1) 50%, rgba(248, 181, 0, 1) 51%, rgba(251, 223, 147, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(252, 234, 187, 1) 0%, rgba(252, 205, 77, 1) 50%, rgba(248, 181, 0, 1) 51%, rgba(251, 223, 147, 1) 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, rgba(252, 234, 187, 1) 0%, rgba(252, 205, 77, 1) 50%, rgba(248, 181, 0, 1) 51%, rgba(251, 223, 147, 1) 100%);
    /* W3C */
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#fceabb', endColorstr='#fbdf93', GradientType=0);
    /* IE6-9 */
}

.seatimage.perspect.categNo4.free,
.seatimage.categNo4.free,
.seatimage.legend.categNo4 {
    /* background: url("https://www.themisweb.fr/images/billes/pastille_bleue.gif") no-repeat;*/
    background: #1E74FF;
}

.seatimage.perspect.free:hover {
    /*background: url("https://www.themisweb.fr/images/billes/pastille_verte.gif") no-repeat;*/
    background: #00FF40;
}

.nofree.ps,
.seatimage.nofree {
    background: url("https://www.themisweb.fr/images/default/seats/square_grey_dark.png") no-repeat;
    opacity: 0.5;
}

.seatimage.ui-selected,
.pris {
    background: url(/files/0926/INDIV/images/poi.svg) no-repeat !important;
    width: 28px;
    height: 28px;
    margin-top: -20px;
    margin-left: -12px;
}

.seatimage.legend {
    border-radius: 10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    -khtml-border-radius: 10px;
    border-top-left-radius: 10px;
}



.shopping-cart {
    display: none
}

.texttexte {
    /*display:none;*/
    margin-top: -210px;
    margin-left: 110px;
    margin-bottom: 100px;
    font-weight: bold;
    font-size: 18px;
}

.tdOfTableOfLegendsForText {
    padding: 0px 10px;
    border: 0px;
    text-align: left;
    color: rgb(255, 255, 255);
    font-size: 16px;
}


/* tooltip seatview */

.ShowSeatView,
#SeatViewPicture {
    background-color: #000;
    color: #fff;
}


/*
.ui-dialog-titlebar{
background: rgb(152,17,11);
background: -moz-linear-gradient(top,  rgb(152,17,11) 0%, rgb(211,17,26) 100%);
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgb(152,17,11)), color-stop(100%,rgb(211,17,26)));
background: -webkit-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
background: -o-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
background: -ms-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
background: linear-gradient(to bottom,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#508fc5', endColorstr='#508fc5',GradientType=0 );
padding:5px;
font-size:1.1em;
font-weight:bold;
color:#ffffff;
}
*/

#divForComboxBox,
#lbHallName,
#lbHallNameInfo {
    display: none;
}


/*** END CHOIX SUR PAN ***/


/**************** LOGIN ****************/

.HeaderConnect {
    color: #333333;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 20px;
    text-transform: uppercase;
    font-weight: 700;
    text-align: left;
}

#WctrLoginCreation1_BtnCreate {
    margin-top: 20px;
    margin-bottom: 20px;
    text-decoration: none;
}

#WctrlLoginConnect1_LinkBtnPassForget {
    margin-top: 10px;
    margin-left: 15px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #333333;
    background: none;
    white-space: nowrap;
}

#WctrlLoginConnect1_LinkBtnPassForget:hover {
    margin-top: 10px;
    margin-left: 15px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #fff;
    white-space: nowrap;
    text-decoration: none;
}

#btModifierCmd {
    display: none;
}

#WctrlLoginConnect1_BtnLogin {
    margin-right: 0px;
    float: left;
    text-decoration: none;
}

#WctrLoginCreation1_lbluseraccount {
    font-style: italic;
    color: #2d2d2d;
}

.text-left {
    margin-bottom: 30px;
}

#WctrlLoginConnect1_oldBtnLogin,
#WctrlLoginConnect1_oldLinkBtnPassForget {
    display: none;
}

.tdForLibelleInfoComp.cnil {
    padding-top: 20px;
    font-size: 0.8em;
}

input#WctrLoginCreation1_cbIC12 {
    visibility: hidden
}


/***************/


/*** PANIER ***/


/***************/

a.btSupp {
    padding: 5px;
    border-radius: 0px;
    margin-left: 26px;
    color: #000;
}

a.btSupp:hover {
    color: #E00F27;
}

.trentete th {
    background-color: #DFDFDF!important;
    padding-left: 20px;
    padding-top: 8px;
    padding-bottom: 8px;
    padding-right: 20px;
    font-size: 12px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 700;
}

@media (max-width:450px) {
    .trentete th {
        padding-left: 5px;
        padding-right: 5px;
    }
}

tr.trentete:not(:first-child) {
    display: none;
}

td.tdseatdetail span.glyphicon.glyphicon-trash {
    visibility: hidden;
}

#ctl00_ContentPlaceHolder1_tblEntrees {
    background: none;
    margin-bottom: 10px;
    width: 103%;
}

@media (max-width:750px) {
    #ctl00_ContentPlaceHolder1_tblEntrees {
        width: 102%;
    }
}

td.tdmodeobtention {
    display: none;
}


/* table#ctl00_ContentPlaceHolder1_tblEntrees {
	width:100%;
	
	
} */

.cellmontantmommande {
    color: #2d2d2d;
}

.rowmontantmommande {
    color: #2d2d2d;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: bold;
    font-size: 1.1em;
    padding-top: 15px;
}

.rowfrais {
    color: #2d2d2d;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: bold;
    font-size: 12px;
    padding-top: 15px;
}

#TitlePanier {
    display: none;
}

#tableglobalepanier {
    width: auto;
    margin-top: 0px;
    padding-bottom: 0px;
    margin-bottom: 0px;
    padding-right: 20px;
}


/*
@media (mix-width: 700px) and (max-width: 990px) {
	#tableglobalepanier, #cgv, #retrait, #BoutonsActions, #fPanierSBas, #tdForChrono {
	margin-right: -40px !important;
	}
	
}*/


/* #ctl00_ContentPlaceHolder1_tblTotal {
    width:92%;
	margin: 0px 0px 20px 20px;
	height:40px;
	border-top: 1px solid #CECECE;
} */

td.seancedescription {
    font-weight: 700;
    font-size: 0.9em;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    color: #333333;
    padding-left: 20px;
    padding-top: 15px;
    text-transform: capitalize;
}

.tdseatdetail {
    padding-left: 20px;
}

.tdForChrono {
    text-align: center;
    padding: 15px;
}

#ctl00_ContentPlaceHolder1_lblChronoDebut,
#ctl00_ContentPlaceHolder1_lblChrono,
#ctl00_ContentPlaceHolder1_lblChronoDFin {
    /*background: rgb(152,17,11);*/
    background: #DFDFDF;
    /*background: -moz-linear-gradient(top,  rgb(152,17,11) 0%, rgb(211,17,26) 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgb(152,17,11)), color-stop(100%,rgb(211,17,26)));
	background: -webkit-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	background: -o-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	background: -ms-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	background: linear-gradient(to bottom,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#db0000', endColorstr='#db0000',GradientType=0 );*/
    color: #6B6B6B;
    margin-bottom: 5px;
}

#ctl00_ContentPlaceHolder1_lblChronoDebut {
    padding-left: 20px;
}

#ctl00_ContentPlaceHolder1_lblChronoDFin {
    padding-right: 20px;
}

#ctl00_ContentPlaceHolder1_lblChrono {
    font-weight: 700;
}

#Retrait,
#cgv {
    background: #fff;
    padding-right: 20px;
    padding-left: 20px;
    color: #2d2d2d;
    font-size: 12px;
    font-weight: 400;
}

#cgv {
    padding-bottom: 15px;
}

#Retrait {
    padding-top: 20px;
}

#ctl00_ContentPlaceHolder1_PanelConditions {
    text-align: left;
}

#Retrait div {
    text-align: center;
    display: inline;
}

#tdForChrono {
    text-align: center;
    padding: 3px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 12px;
    margin-left: -20px;
    margin-right: -20px;
    padding-right: 20px;
}

.tdForChrono {
    padding-bottom: 30px
}

#btAjouterPlaces,
#btIdentification,
#btPayer,
#fPanierSBas {
    background: none;
}

@media (min-width:350px) {
    #btIdentification {
        float: right;
    }
    #btAjouterPlaces {
        float: left;
    }
}

@media (min-width:436px) and (max-width:990px) {
    #btPayer {
        margin-left: 10px;
    }
}

@media (max-width:437px) {
    #btPayer {
        margin-top: 10px;
    }
}

#ctl00_ContentPlaceHolder1_lblChronoDebut:before {
    font-size: 12px;
}

.tableglobalechoixseance-trforchrono {
    text-align: center;
    background: #2d2d2d;
    padding: 3px;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 12px;
    margin-left: -20px;
    margin-right: -20px;
    padding-right: 20px;
}

td.cellmontantmommande:after {
    content: " €";
}

span#ctl00_ContentPlaceHolder1_lblObtentionBillet {
    font-weight: 700;
}

#fPanierSBas {
    margin-left: -20px;
    margin-right: -20px;
    padding-right: 20px;
    padding: 5px;
}


/*
#BoutonsActions {
	background:#fff;
	margin-left: -20px;
	margin-right: -20px;
	padding-right: 20px;
}
*/

span.trashPanier {
    background-image: url(/files/0466/INDIV/images/poubelle.png);
    background-repeat: no-repeat;
    padding-right: 20px;
    background-color: none;
}

.btSupp {
    background: none;
    cursor: hand;
    margin: 5px;
}

.btSupp:hover {
    background: none;
    cursor: hand;
}

@media (min-width:770px) {
    /* th#PanierPrixUnit, th#PanierQuantite, th#PanierTotal {
		min-width:100px;
	} */
    th#PanierSpectacle {
        min-width: 328px;
    }
}

div#BoutonsActions {
    /*padding-left:20px;*/
    padding-top: 20px;
}

@media (min-width:350px) {
    #ctl00_ContentPlaceHolder1_divIdentification {
        margin-left: 58px;
    }
}

#ctl00_ContentPlaceHolder1_tblTotal,
#ctl00_ContentPlaceHolder1_tblFrais {
    padding-left: 20px;
    margin-left: 20px;
    width: 98.5%;
    margin-top: 10px;
}

#ctl00_ContentPlaceHolder1_divtblEntrees {
    border-bottom: 1px solid #e7e7e7;
}

a#seeMyPlace {
    display: inline-block;
    -moz-border-radius: 0px;
    border-radius: 2px;
    font-size: 12px;
    padding: 5px 15px 5px 15px;
    text-align: center;
    color: #243342;
    background: #EBEBEB;
    white-space: nowrap;
    margin-top: 5px;
    font-weight: 700;
    font-family: Calibri, 'Helvetica Neue', Helvetica, sans-serif;
    border: none;
}


/*** END FPANIER ***/


/****************/


/*** PRODUITS ***/


/****************/

#Produits {
    display: none;
}

#ctl00_ContentPlaceHolder1_divForProduct {
    max-width: 535px;
    margin-bottom: 150px;
    background: #f4f4f4;
    color: #666666;
    border-radius: 8px;
    box-shadow: -2px 2px 8px rgba( 0, 0, 0, 0.2), 0 -2px 0 rgba( 255, 255, 255, 0.2);
    display: none;
}

.tblProduitsTitre {
    display: none;
}

.tblProduitsTrEntete th {
    /*background: rgb(152,17,11);*/
    background: #FF0000;
    /*background: -moz-linear-gradient(top,  rgb(152,17,11) 0%, rgb(211,17,26) 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgb(152,17,11)), color-stop(100%,rgb(211,17,26)));
	background: -webkit-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	background: -o-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	background: -ms-linear-gradient(top,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	background: linear-gradient(to bottom,  rgb(152,17,11) 0%,rgb(211,17,26) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#db0000', endColorstr='#db0000',GradientType=0 );*/
    padding-left: 15px;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-right: 5px;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
}

.tblProduits {
    /*width:500px;*/
}

.tblProduitsTrContent td {
    padding-left: 15px;
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 14px;
}

.tblProduitsTrContentTdProduit {
    font-weight: bold;
}

.tblProduitsTrEnteteTdImage {
    color: #FF0000;
}

.tblProduitsTrEnteteTdNbr,
.tblProduitsTrEnteteTdProduit,
.tblProduitsTrEnteteTdMontant {
    color: #ffffff;
}

.tblProduitsTotalProduitEuro {
    font-size: 1em;
    font-weight: bold;
    color: #000;
    text-align: center;
    background: #E2E2E2;
    margin-top: 10px;
}

td#lblTotalProduitEuro.tblProduitsTrContentTdMontant {
    padding-bottom: 10px;
    padding-top: 10px;
}

#lblTotalProduitEuro:before {
    content: "Montant total : ";
}

#ibAjouterPanierProduit {
    cursor: pointer;
    margin-top: 10px;
    padding: 10px;
    float: left;
}


/*** END PRODUITS ***/


/************************/


/*** FICHE INFO PERSO ***/


/************************/


/*** END FICHE INFO PERSO ***/


/**** HIDE HIDDENFILED ***/


/*#ctl00_ContentPlaceHolder1_lbChoixPlacement,*/


/*#ctl00_ContentPlaceHolder1_txtUrlToPanier,*/


/*#ctl00_ContentPlaceHolder1_txtListePlacesPrises,*/


/*#ctl00_ContentPlaceHolder1_txtDdlsChoix,*/


/*.trForNomEtLogo,*/


/*#btChoixSeances
{
	display: none;
}*/

a.seeConditionsDeVentes {
    color: #E00F27;
}

.tableLoginPATrConnexionLogin,
.tableLoginPATrConnexionPassWord,
.tableLoginPATrBtsConnexions {
    margin-left: 50px;
}

#ctl00_ContentPlaceHolder1_lkRetourCalendrier:before,
#imgBtBack:before {
    font-family: FontAwesome;
    content: "\f053";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

.ui-state-default.ui-corner-all:before {
    font-family: FontAwesome;
    content: "\f00d";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1.3em;
    padding-right: 10px;
}

#ctl00_ContentPlaceHolder1_hlToPlanSalle:before {
    font-family: FontAwesome;
    content: "\f21d";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#ctl00_ContentPlaceHolder1_lkChoixPlacement:before {
    font-family: FontAwesome;
    content: "\f074";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#ctl00_ContentPlaceHolder1_lkAjouterPanier:before {
    font-family: FontAwesome;
    content: "\f218";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#ctl00_ContentPlaceHolder1_linkdisplayLogin:before {
    font-family: FontAwesome;
    content: "\f090";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#ctl00_ContentPlaceHolder1_lkRetourManifs:before {
    font-family: FontAwesome;
    content: "\f067";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#seeMyPlace:before {
    font-family: FontAwesome;
    content: "\f06e";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#ctl00_ContentPlaceHolder1_lkEtapeSuivante:before {
    font-family: FontAwesome;
    content: "\f09d";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#linkconnectLoginPA_inMenu:before {
    font-family: FontAwesome;
    content: "\f02b";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 10px;
}

#titleMenuPanier:before {
    font-family: FontAwesome;
    content: "\f290";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 5px;
}

#titleMenuLogin:before {
    font-family: FontAwesome;
    content: "\f007";
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    text-decoration: none;
    font-size: 1em;
    padding-right: 5px;
}

#WzTtDiV {
    background-color: #fff;
}

#WzTiTl {
    background-color: #fff;
}

#WzBoDy {
    background-color: #fff !important;
}

.c10001 {
    width: 10px;
    height: 10px;
    background: #00B0AF;
}

.c10002 {
    background: #EE51F0;
}

.c10003 {
    background: #FAD161;
}

.c10004 {
    background: #1E74FF;
}

.c10005 {
    background: #d5380e;
}

.c10006 {
    background: #006f2c;
}