﻿
var formuleidselected = 0;

var arrChoix = [];

//tableau choix tarifs deja en session
var arrChoixTarifsDejaExistant;
$(document).ready(function () {
	
	forceConnection();
	forceProfil();
	
	//
	var objDejaChoisis = sessionStorage.getObj("objEventsChoice");	
	if(objDejaChoisis != null)
	{
		arrChoix = objDejaChoisis; 		
	}
	
	if (identiteIdCurrent > 0) {
        setTimeout(function () {
            var previousBasket = GetCurrentBasket(structureIdCurrent);
            if (previousBasket == null) {
                InvalidBasket();
                sessionStorage.setObj("previousBasket", null);
            }else if(previousBasket.panier.BasketId > 0 && localStorage.getItem('panierRecharger') == "false") { //&& thisbasket.customBasket.ReloadPage) {
                var modalText = ReadXmlTranslate("msg_alert_recover_existing_basket");
                ShowConfirmModal(ReadXmlTranslate("msg_title_recover_existing_basket"), modalText, ReadXmlTranslate("msg_response_recover_existing_basket_yes"), ReadXmlTranslate("msg_response_recover_existing_basket_no"), "btnGetBasket", "btnClearAllBasket");
	
				
				sessionStorage.setObj("previousBasket", previousBasket);
				BindButtonConfirmBasket();
            }
        }, 1000);
	}

    sessionStorage.setItem("structureIdCurrent", structureIdCurrent);
    sessionStorage.setItem("langCodeCurrent", langCodeCurrent);
	
	//if(previousBasket.panier.BasketId == 0)
		//si il y a deja un tableau de tarifs en session
		if (sessionStorage.getObj("objEventsChoice") != null) {
			//console.log('pas vide') 
			arrChoixTarifsDejaExistant = sessionStorage.getObj("objEventsChoice");
		}
			
		
		loadFormulasAndTarif();
		
		
		$('#btnValiderChoixTarifsTop, #btnValiderChoixTarifsBottom').on('click', function (e) {
			e.preventDefault();

                sessionStorage.setObj("objEventsChoice", arrChoix);

			//si on est pas identifié et que le forcelogin est désactivé
			if(identiteIdCurrent == 0 && settings.global.loadLoginFirstPage)
			{
				forceConnection();	
			}else{				
				url = urlajaxabov2 + "/Events/Index";
				$(location).attr("href", url);		
			}
			
		});

		GetEventsChoice();
		HideBtConnexionComm();

		ShowMiniBasket();
	
});

function forceConnection(){
	if(identiteIdCurrent == 0 && settings.global.loadLoginFirstPage)
	{
		$('#lnkconnect').trigger('click');		
	}
}


function forceProfil(){
	
	if(settings.global.loadProfilFirstPage)
	{
		$('.menugestionprofil').trigger('click');
	}
	
}

function HideBtConnexionComm()
{
	//cache le bouton de connexion lorsque l'on est connecté
	if (identiteIdCurrent!=undefined && identiteIdCurrent>0)
	{
		$('#comm_imnotconnected').hide();
	}
	
} 
//réattribut les valeurs dans les champs 
function setInput(){
	
	$.each(arrChoix, function(indx, itemchoix) {
		//liste des  tarifs de la formule en cours
		$.each(itemchoix.listTarifs, function(indx, itemtarif) {
			//attribut la valeur
			$('#collapse'+itemchoix.formulaid).find('input[data-priceid="'+itemtarif.tarifid+'"]').val(itemtarif.nb);
			//trigger change pour afficher le recapitulatif
			//$('#collapse'+itemchoix.formulaid).find('input[data-priceid="'+itemtarif.tarifid+'"]').change();
		});
		
	});
	
	$('input').change();
}

//charge les formules et tarifs 
function loadFormulasAndTarif() {
	   $.ajax({
                type: "POST",
                url: urlajaxabov2 + '/formulas/Load',
                data: {
                    structureId: structureIdCurrent,
					identiteId: identiteIdCurrent ,
					langCode: langCodeCurrent
                },
                success: function (data) {
                    //console.log(data);

                    var html = '';
                    if (data.length == 0) {   // aucune formule n'est remontée
                        html = ReadXmlTranslate("msg_neither_formula");
                        $('#nextsteptop').addClass('d-none');
                        $('#nextstepbottom').addClass('d-none');
                        $('#accordion').html(html);
                    } else {
                        var ariaExpanded = "false";
                        if (data.length == 1) {
                            // expand la premiere (et unique donc)
                            var ariaExpanded = "true"
                        }
						
						$.each(data, function(indx, itemformule){
							//récupère la contrainte de la formule
							var contrainte = getContrainte(itemformule.formulaId+':');
							
							var htmlContrainst = '<span data-id="' + itemformule.formulaId + '"  ><strong>' + itemformule.formulaName + '</strong></span> <span>';
							var nbcontrainte = contrainte[0].listConstraints.length;
							$.each(contrainte[0].listConstraints, function (indx, item) {
							    if (indx < nbcontrainte - 1)
							        htmlContrainst += '<small>' + item.name.trim() + '</small> & ';
							    else
							        htmlContrainst += '<small>' + item.name.trim() + '</small> ';

							    //htmlContrainst += '<span><small>'+contrainte[0].listConstraints[0].name.trim() +'</small></span>';
							});
							htmlContrainst += '</span>';

							html += ' <div class="card" data-formulename="' + itemformule.formulaName + '" data-nbmanifmax="'+itemformule.nb_manifMax+'" data-nbmanifmin="'+itemformule.nb_manifMin+'" data-formuleid="' + itemformule.formulaId + '"> <div class="card-header" id="heading'+itemformule.formulaId+'"> <h5 class="mb-0"> <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapse'+itemformule.formulaId+'" aria-expanded="false" aria-controls="collapse'+itemformule.formulaId+'">';
							html += '<div class="row no-gutters align-items-center"><div class="col-auto"><svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve"><g ><g><path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"/></g><g><g><path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"/></g></g></g><g class="verticalbar"><g><path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"/></g></g></svg></div>';							
							html += '<div class="col card-abotitle"> '+htmlContrainst+' </div> </div></button></h5> </div>';
							if (ariaExpanded != "true")
								html += '<div id="collapse' + itemformule.formulaId + '" class="collapse" aria-labelledby="heading' + itemformule.formulaId + '" data-parent="#accordion"><div class="card-body">';
							else {
								html += '<div id="collapse' + itemformule.formulaId + '" class="collapse show" aria-labelledby="heading' + itemformule.formulaId + '" data-parent="#accordion"><div class="card-body">';
							}
			
							
							$.each(itemformule.listPrices, function(indx, itemprice) {
								
								nmax = 6;
								if (itemprice.NbSeatMax != undefined && itemprice.NbSeatMax!=0)
									nmax= itemprice.NbSeatMax;
								
								html += '<div class="form-group row">';
								html += '		<div class="col col-md-auto">';
								html += '			<input id="form'+itemformule.formulaId+'tarif'+itemprice.PriceId+'" type="text" value="0" data-max="' + nmax +  '" name="form'+itemformule.formulaId+'tarif'+itemprice.PriceId+'" class="touchspin" data-pricename="'+itemprice.Price_name+'" data-priceid="' + itemprice.PriceId + '">';
								html += '		</div>';
								html += '<label for="form'+itemformule.formulaId+'tarif'+itemprice.PriceId+'" class="col-12 col-md control-label touchspin-label">'+itemprice.Price_name+'</label>';


								html += '	</div>';
							});
							
							html += '</div> </div> </div>';
																		

                        });

                        $('#accordion').html(html);
                        $("input.touchspin").TouchSpin();

                        showRecapitulatif();

                        if (sessionStorage.getObj("objEventsChoice") != null) {
                            arrChoix = sessionStorage.getObj("objEventsChoice");
                            //remets les valeurs de ce qui avait été choisi dans les input
                            setInput();
                        }
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(textStatus);
                }
            });
}





//affiche le recap des sélection en parcourant les input et collapse
function showRecapitulatif() {
	$('#aboresume').css('display', 'none');
	
	var timerIsShow = false;
	$('.step1 .touchspin').on('change', function (event) {
		var allselected = '';
		$('#aboresume').css('display', 'none');
		var aboResumeEmpty = false;
		$('#aboresume #cancelChoice').remove();
		
		//vide le tableau général à chaque ajout/suppression
		//arrChoix = [];
	
		$('.step1 .collapse').each( function (index, element) {
			
			//vide le tableau et l'objet pour chaque collapse
			var listTarifs = [];
			var objChoix = {};
			
			var thisAboIsEmpty=false;
			var thisCollapse = $(this).attr('id');
			var thisCollapseTitle = $('button[data-target="#'+thisCollapse+'"]')
			//console.log(thisCollapseTitle.find('.card-abotitle').html())
			allselected += '<div class="formulesselected">';
			
			var sumtarifs = 0;
			$('#'+thisCollapse+' .touchspin').each( function () {
				
				var inputId = $(this).attr('id');
				var inputVal = $(this).val()
				if(inputVal > 0) {
					//stock les données dans les objets
					var tarifid = $(this).data('priceid');
					var pricename = $(this).data('pricename');
					var formulename = $(this).closest('.card').data('formulename');
					var formuleid = $(this).closest('.card').data('formuleid');
					var nbmanifmax = $(this).closest('.card').data('nbmanifmax');
					var nbmanifmin = $(this).closest('.card').data('nbmanifmin');
					
																				  
					var objTarif = {};
					objTarif.tarifid = tarifid;
					objTarif.tarifName = pricename;
					
					//extraction de la formule deja stockée 
					var arrFormula = $.grep(arrChoix, function(x) { return x.formulaid == formuleid });
					
					if(arrFormula.length == 1)
					{
						//si il y a une formule, on extrait le tarif											
						var tarifOfFormule = $.grep(arrFormula[0].listTarifs, function (t) { return t.tarifid == tarifid })
						
						if(tarifOfFormule.length == 1)
						{
							if(inputVal > tarifOfFormule[0].nb)
							{
								tarifOfFormule[0].nb = parseInt(inputVal);
							}
							
							if(inputVal < tarifOfFormule[0].nb)
							{								
								tarifOfFormule[0].nb = parseInt(inputVal);
								//on regarde les listes abonnés et on mets la meme chose que les liste tarifs
								if(tarifOfFormule[0].listAbos.length > 0){
								
									tarifOfFormule[0].listAbos = $.grep(tarifOfFormule[0].listAbos, function(x) { return x.idx <= tarifOfFormule[0].nb });
								}
							}
							
							sumtarifs += parseInt(inputVal);
							
						}else{
							objTarif.nb = parseInt(inputVal);
							sumtarifs += parseInt(inputVal);
							arrFormula[0].listTarifs.push(objTarif);
						}
					
						arrFormula[0].sumtarifs =  sumtarifs;
					}else{
						objTarif.nb = inputVal;
					
						sumtarifs += parseInt(inputVal);
						//ajout des tarifs dans un tableau
						listTarifs.push(objTarif);
						
						objChoix.formulaid = formuleid;
						objChoix.formulaName = formulename;
						objChoix.listTarifs = listTarifs;
						objChoix.sumtarifs = sumtarifs;
						objChoix.nbmanifmax = nbmanifmax;
						objChoix.nbmanifmin = nbmanifmin;
					}
			
					
					aboResumeEmpty = true;
					if(!thisAboIsEmpty) {
						thisAboIsEmpty = true;
						allselected += '<div class="card-abotitle">'+thisCollapseTitle.find('.card-abotitle').html()+'</div>';
					}
					thisLabel = $('label[for="'+inputId+'"]').html();
					allselected += "<div><span class='value'>"+inputVal+" abonnement(s)</span> <span class='label'>"+thisLabel+"</span></div>";
				}else{
					//si il y a 0 dans input
					
					var tarifid = $(this).data('priceid');				
					var formuleid = $(this).closest('.card').data('formuleid');
					
					var arrFormula = $.grep(arrChoix, function(x) { return x.formulaid == formuleid });
					
					if(arrFormula.length == 1)
					{
						//si il y a une formule, on extrait le tarif											
						//var tarifOfFormule = $.grep(arrFormula[0].listTarifs, function (t) { return t.tarifid != tarifid })
						arrFormula[0].listTarifs = $.grep(arrFormula[0].listTarifs, function (t) { return t.tarifid != tarifid })
					}
					
				}
			});
			
			var formuleid = $(this).closest('.card').data('formuleid');
			var arrFormula = $.grep(arrChoix, function(x) { return x.formulaid == formuleid  });					
			
			//si aucun tarif alors on supprime la formule du tableau
			if(arrFormula.length > 0 && arrFormula[0].listTarifs.length == 0)
			{
				arrChoix = $.grep(arrChoix, function(x) { return x.formulaid != formuleid  });
			}
			//Si un tarif est suppérieur a 0 pour chaque collapse
			if(thisAboIsEmpty && arrFormula.length == 0)
				arrChoix.push(objChoix);
			else
				console.log(arrChoix.length);
			
			allselected += '</div>';
		})
		//console.log(arrChoix);
		
		if(aboResumeEmpty) {
			$('#aboresume').css('display', 'block');
		}
		$('#aboresumeContent').html(allselected);
		
		
		//nombre du tableau des tarifs 
		var lengthOfListTarif = $.grep(arrChoix, function(x) { 
			return x.listTarifs.length;
		});
	
		//on desactive le bouton vers la page suivante si les formules ont été supprimées
		if (arrChoix.length == 0 || lengthOfListTarif.length == 0)
		{
			//active les 2 boutons (haut et bas)
			$('#btnValiderChoixTarifsTop, #btnValiderChoixTarifsBottom').attr('disabled', true);
			
			//$('#timer').html('');
			$('#timer').addClass('d-none');
			getHeaderHeight();
			timerIsShow=false;
		}
		else
		{
		    $('#btnValiderChoixTarifsTop, #btnValiderChoixTarifsBottom').attr('disabled', false);

		    $('#aboresume h3').append(' <a id="cancelChoice"> (<i class="fa fa-trash fa-1x"></i>)</a>');
		    $('#cancelChoice').on('click', function (e) {


		        $('input[data-priceid]').val(0);
		        $('#btnValiderChoixTarifsTop, #btnValiderChoixTarifsBottom').attr('disabled', true);
		        showRecapitulatif();
		    });
		    

		
			//si le compteur n'a pas encore été affiché
			if(!timerIsShow)
			{				
				if(sessionStorage.getObj('previousBasket') == null)
				{
					startTimer(moment().format('YYYY-MM-DD HH:m:s'), settings.global.timerSelectDuration);
					timerIsShow = true;

					$('#actualtimer .minute_wrapper').prepend(ReadXmlTranslate("msg_before_select_timer") +" ");
					$('#actualtimer .second_wrapper').append(" " + ReadXmlTranslate("msg_after_select_timer"));

				}else{
					var previousBasket = sessionStorage.getObj('previousBasket');
					localStorage.setItem('isTimerBuy', true)
					startTimer(moment(previousBasket.customBasket.StrDateCreateBasket).format('YYYY-MM-DD HH:m:s'), settings.global.timerBuyDuration);
					timerIsShow = true;


					$('#actualtimer .minute_wrapper').prepend(ReadXmlTranslate("msg_before_buy_timer") + " ")
					$('#actualtimer .second_wrapper').append(" " +ReadXmlTranslate("msg_after_buy_timer"))

				}

			
			}
		}
	});
}

//Récupère les informations contrainte de la formule
//formulatarif ==> 1:
function getContrainte(formulatarif){
	var arrOfConstraintesOfFormula = [];
	 $.ajax({
		type: "POST",
		url: urlajaxabov2 + '/events/LoadConstraintes',
		async: false,
		data: {
			structureId: structureIdCurrent,
			identiteId: identiteIdCurrent,
			langCode: langCodeCurrent,
			formulaId: formulatarif
		},
		success: function (retour) {			
			arrOfConstraintesOfFormula.push(retour);
		},
		error: function (XMLHttpRequest, textStatus, errorThrown) {
			console.log(textStatus);
		},
		complete: function () {
			
		}
	});
	
	return arrOfConstraintesOfFormula;
}



