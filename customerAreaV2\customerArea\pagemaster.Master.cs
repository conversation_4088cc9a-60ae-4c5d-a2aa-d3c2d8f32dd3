﻿using customerArea.App_Code;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Web;
using System.Web.Configuration;
using System.Web.UI;
using System.Web.UI.WebControls;
using Themis.Libraries.Utilities.Crypto;
using ws_DTO.objets_liaisons;

namespace customerArea
{
    public partial class pagemaster : System.Web.UI.MasterPage
    {

        protected NameValueCollection QueryStrings;

        private static readonly ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        

        protected void Page_Load(object sender, EventArgs e)
        {

            //Ntipiws.PaiementSecuriseService tip = null; //=  Ntipiws.PaiementSecuriseService;
            //Ntipiws.creerPaiementSecuriseRequest1  req1= new Ntipiws.creerPaiementSecuriseRequest1();
            //Ntipiws.creerPaiementSecuriseRequest req = new Ntipiws.creerPaiementSecuriseRequest();
            //req1.arg0.
            //req1.arg0.exer = "2015";
            //tip.creerPaiementSecurise(req1);



            //tipiws.RecupererDetailClientRequest req2 = new tipiws.RecupererDetailClientRequest();

            //req2.numCli = "008426";

            //tp.recupererDetailClient(req2);
            //tipiws.PaiementSecuriseService

            // tp.creerPaiementSecurise(req);


            bool UserIsAuthentified = false;
            int IdStructure = 0;
            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
                IdStructure = int.Parse(System.Web.HttpContext.Current.Session["idstructure"].ToString());

            myhead.Attributes.Add("structureid", System.Web.HttpContext.Current.Session["idstructure"].ToString());

            int idIdentiteForTry;
            if (Session["SVarUserIdentityID"] != null && int.TryParse(Session["SVarUserIdentityID"].ToString(), out idIdentiteForTry))
            {
                UserIsAuthentified = true;
                myhead.Attributes.Add("myidentity", System.Web.HttpContext.Current.Session["SVarUserIdentityID"].ToString());

            }
            int resulEvent = 0;
            if (Request.QueryString["eventId"] != "" && Request.QueryString["eventId"] != null && int.TryParse(Request.QueryString["eventId"], out resulEvent))
            {
                System.Web.HttpContext.Current.Session["eventId"] = resulEvent;

                myhead.Attributes.Add("eventid", resulEvent.ToString());

            }

            if (Request.QueryString["codesite"] != "" && Request.QueryString["codesite"] != null)
            {
                System.Web.HttpContext.Current.Session["codesite"] = Request.QueryString["codesite"].ToString();
            }



            string cryptoKey = WebConfigurationManager.AppSettings["CryptoKey"].ToString();

            Sha1 sha1 = new Sha1(String.Format("{0,4:0000}", IdStructure) + "|" + resulEvent + cryptoKey);
            string myhash = sha1.getSha1();

            myhead.Attributes.Add("ctrlh", myhash.ToString());

            if (Session["DeviseCode"] != null && !string.IsNullOrEmpty(Session["DeviseCode"].ToString()))
            {
                myhead.Attributes.Add("deviseCode", Session["DeviseCode"].ToString());
            }

            if (Session["DeviseIso"] != null && !string.IsNullOrEmpty(Session["DeviseIso"].ToString()))
            {
                myhead.Attributes.Add("deviseIso", Session["DeviseIso"].ToString());
            }
            if (Session["SVarUserIsGuest"] != null && !string.IsNullOrEmpty(Session["SVarUserIsGuest"].ToString()))
            {
                myhead.Attributes.Add("isguest", Session["SVarUserIsGuest"].ToString());
            }

            //string CssPaths = System.Configuration.ConfigurationManager.AppSettings["CssPaths"].ToString();
            //CssPaths = CssPaths.Replace("[idstructure]", IdStructure.ToString("0000"));
            //int posLastPoint = CssPaths.LastIndexOf('/') + 1;
            //string CssPathRacine = CssPaths.Substring(0, posLastPoint);
            //Urls url = new Urls();
            //string urlCssGenerale = url.GetRelativePath(CssPathRacine, IdStructure.ToString("0000"), "css", resulEvent, true);

            ////string urlCssGenerale =  GetUrlCssGenerale();
            //if (urlCssGenerale != null)
            //    StyleLink.Href = urlCssGenerale;
            //else
            //    StyleLink.Visible = false;
            logger.Debug("Page Master Load");

            Ihm myIhm = new Ihm();

            myIhm.SetVisualCss(IdStructure, ref StyleLink, resulEvent, true);

            //string urlBanner = System.Configuration.ConfigurationManager.AppSettings["CssPaths"].ToString();
            myIhm.SetBanner(IdStructure.ToString("0000"), ref banner, resulEvent, true);




            /*    if (System.Web.HttpContext.Current.Request.QueryString.Count > 0 && System.Web.HttpContext.Current.Request.QueryString["iswidget"] != null
                   && System.Web.HttpContext.Current.Request.QueryString["iswidget"] == "1")
                {
                    //supprimer le header et footer
                    header.Visible = false;
                    footer.Visible = false;
                }*/

            string idpa = "0";
            if (Session["ProfilAcheteurId"] != null)
            {
                idpa = (string)Session["ProfilAcheteurId"];
            }


            string plateformCode = "";
            if (Request.QueryString["plateformCode"] != "" && Request.QueryString["plateformCode"] != null)
            {
                plateformCode = Request.QueryString["plateformCode"].ToString();
                System.Web.HttpContext.Current.Session["plateformCode"] = Request.QueryString["plateformCode"].ToString();
            }

            string lang = Initialisations.GetUserLanguage();



            // menu
            if (!UserIsAuthentified)
            {
                liUpdateprofil.Visible = false;
                liDisconnectprofil.Visible = false;
            }
            else
            {
             
                if (System.Web.HttpContext.Current.Session["SVarUserIsGuest"] != null && !string.IsNullOrEmpty(System.Web.HttpContext.Current.Session["SVarUserIsGuest"].ToString()) && Convert.ToBoolean(System.Web.HttpContext.Current.Session["SVarUserIsGuest"].ToString()) == false)
                {
                    string templateDisconnectprofil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateDisconnectProfil");
                    if (templateDisconnectprofil == "")
                        liDisconnectprofil.Visible = false;
                    LitdisconnectProfil.Text = templateDisconnectprofil;



                    List<CustomJsonSettings> listJsonSettingsToMerge = new List<CustomJsonSettings>
                        {
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = true },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsJSON", IsDefault = false },
                            new CustomJsonSettings() { KeyOfJsonPath = "physicalPathOfSettingsPlateformJSON", IsDefault = false }
                        };

                    //On récup les options du fichier appsettings.json
                    dynamic globalPlateform = utilitaires2010.Initialisations.GetMergeSettings(listJsonSettingsToMerge, IdStructure, resulEvent, int.Parse(idpa), plateformCode, lang);

                    //dynamic isProfilPageAuthorized = globalPlateform.global.pages.myprofil.Value;
                    //if (globalPlateform.customer != null && globalPlateform.customer.global != null && globalPlateform.customer.global.pages != null)
                    //{
                    //    isProfilPageAuthorized = globalPlateform.customer.global.pages.myprofil.Value;
                    //}

                    string templateUpdateprofil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateUpdateProfil", Initialisations.IsAuthorizedPage(globalPlateform, "myprofil"));
                    if (templateUpdateprofil == "")
                        liUpdateprofil.Visible = false;
                    else
                    {
                        string urlToUpdate = "";
                        urlToUpdate = Urls.getUrlPopUpdateIdentification(IdStructure, resulEvent.ToString(), true);
                        templateUpdateprofil = templateUpdateprofil.Replace("[linkupdate]", urlToUpdate);

                        LitupdateProfil.Text = templateUpdateprofil;
                    }

                  
                    string templateHistoprofil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateHistoriqueProfil", Initialisations.IsAuthorizedPage(globalPlateform, "myorders"));
                    if (templateHistoprofil == "")
                        liHistorique.Visible = false;
                    else
                    {
                        string urlPageHisto = Urls.getUrlPopHistoCustomer(IdStructure, resulEvent.ToString(), true);
                        templateHistoprofil = templateHistoprofil.Replace("[urlPageHisto]", urlPageHisto);
                        LitHistoProfil.Text = templateHistoprofil;
                    }


                    
                    string templateResaList = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateResaList", Initialisations.IsAuthorizedPage(globalPlateform, "myreservations"));
                    if (templateResaList == "")
                    {
                        liResalist.Visible = false;
                    }
                    else
                    {
                        string urlPageResaList = Urls.getUrlPopResaListCustomer(IdStructure, resulEvent.ToString(), true);
                        templateResaList = templateResaList.Replace("[urlPageResaList]", urlPageResaList);
                        LitResaList.Text = templateResaList;
                    }



                    string templateOrdersW = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateOrdersW", Initialisations.IsAuthorizedPage(globalPlateform, "myordersw"));
                    if (templateOrdersW == "")
                    {
                        liOrdersW.Visible = false;
                    }
                    else
                    {
                        string urlPageOrdersW = Urls.getUrlPopOrdersWCustomer(IdStructure, resulEvent.ToString(), true);
                        templateOrdersW = templateOrdersW.Replace("[urlPageOrdersW]", urlPageOrdersW);
                        liOrdersWPage.Text = templateOrdersW;
                    }



                    string templateTicketsW = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateTicketsW", Initialisations.IsAuthorizedPage(globalPlateform, "myticketsw"));
                    if (templateTicketsW == "")
                    {
                        liTickets.Visible = false;
                    }
                    else
                    {
                        string urlPageTicketsW = Urls.getUrlPopTicketsWCustomer(IdStructure, resulEvent.ToString(), true);
                        templateTicketsW = templateTicketsW.Replace("[urlPageTicketsW]", urlPageTicketsW);
                        liTicketsPage.Text = templateTicketsW;
                    }





                    string templateBankDetailProfil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateBankDetailsProfil", Initialisations.IsAuthorizedPage(globalPlateform, "mybankdetails"));
                    if (templateBankDetailProfil == "")
                        liBankDetail.Visible = false;
                    else
                    {
                        string urlBankDetails = Urls.getUrlBankDetails(IdStructure, resulEvent.ToString(), true);
                        templateBankDetailProfil = templateBankDetailProfil.Replace("[urlPageBankDetails]", urlBankDetails);

                        LiBankDetailProfil.Text = templateBankDetailProfil;
                    }

                    
                    string templateFans = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateFansProfil", Initialisations.IsAuthorizedPage(globalPlateform, "myreabofans"));
                    if (templateFans == "")
                        LiFansProfil.Visible = false;
                    else
                    {

                        string urlFansProfil = Urls.getUrlFans(IdStructure, resulEvent.ToString(), true);
                        //string urlFansProfil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateFansProfil");
                        templateFans = templateFans.Replace("[urlPageFans]", urlFansProfil);

                        LiFansProfil.Text = templateFans;
                    }


                    string templateTransaction = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateHistoTransationProfil");
                    if (templateTransaction == "")
                        liTransactions.Visible = false;
                    else
                    {

                        string urlTransactionProfil = Urls.getUrlHistoTransaction(IdStructure, resulEvent.ToString(), true);
                        //string urlFansProfil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateFansProfil");
                        templateTransaction = templateTransaction.Replace("[urlPageHistoTransaction]", urlTransactionProfil);

                        liTransactionsProfil.Text = templateTransaction;
                    }


                  
                    string templateWaitList = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateWaitList", Initialisations.IsAuthorizedPage(globalPlateform, "mywaitinglist"));
                    if (templateWaitList == "")
                        liWaitList.Visible = false;
                    else
                    {
                        string urlWaitList = Urls.getUrlWaitList(IdStructure, resulEvent.ToString(), true);
                        //string urlFansProfil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateFansProfil");
                        templateWaitList = templateWaitList.Replace("[urlPageWaitList]", urlWaitList);

                        liWaitListPage.Text = templateWaitList;
                    }


                    string templateConsumersList = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateLinkedConsumersList", Initialisations.IsAuthorizedPage(globalPlateform, "myconsumerslist"));
                    if (templateConsumersList == "")
                        liConsumersList.Visible = false;
                    else
                    {
                        string urlConsumersList = Urls.getUrlLinkedConsumersList(IdStructure, resulEvent.ToString(), true);
                        //string urlFansProfil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateFansProfil");
                        templateConsumersList = templateConsumersList.Replace("[urlPageLinkedConsumersList]", urlConsumersList);

                        liConsumersListPage.Text = templateConsumersList;
                    }


                    string templateAttachments = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateAttachments", Initialisations.IsAuthorizedPage(globalPlateform, "myattachments"));
                    if (templateAttachments == "")
                        liAttachments.Visible = false;
                    else
                    {
                        string urlAttachments = Urls.getUrlAttachments(IdStructure, resulEvent.ToString(), true);
                        //string urlFansProfil = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateFansProfil");
                        templateAttachments = templateAttachments.Replace("[urlPageAttachments]", urlAttachments);

                        liAttachmentsPage.Text = templateAttachments;
                    }


                    string templateAdhesions = myIhm.GetTemplate(resulEvent.ToString(), idpa, "templateAdhesions", Initialisations.IsAuthorizedPage(globalPlateform, "myadhesions"));
                    if (templateAdhesions == "")
                        liAdhesions.Visible = false;
                    else
                    {
                        string urlAdhesions = Urls.getUrlAdhesions(IdStructure, resulEvent.ToString(), true);
                        templateAdhesions = templateAdhesions.Replace("[urlPageAdhesions]", urlAdhesions);
                        liAdhesionsPage.Text = templateAdhesions;
                    }

              


                }

            }


            string hearderHtml = myIhm.GetTemplate(resulEvent.ToString(), idpa, "template_header");
            headerTemplate.InnerHtml = hearderHtml;

            string footerHtml = myIhm.GetTemplate(resulEvent.ToString(), idpa, "template_footer");
            footerTemplate.InnerHtml = footerHtml;



        
            //récuprère la config pour savoir ce qu'il aut afficher 

            //setting de la plateform de laquelle on viens qui est dans l'url (plateformCode)
            dynamic settingsPlateformData = utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsPlateformJSON", plateformCode, IdStructure, resulEvent, int.Parse(idpa), lang, false);

            // Correction temporaire pour l'App ID Facebook
            if (settingsPlateformData != null && settingsPlateformData.login != null && settingsPlateformData.login.facebook != null)
            {
                settingsPlateformData.login.facebook.applicationId = "1103870934574590";
            }

            settingsPlateform.InnerText = JsonConvert.SerializeObject(settingsPlateformData);

            dynamic settingsCustomerData = utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode, IdStructure, resulEvent, int.Parse(idpa), lang, false);

            // Correction temporaire pour l'App ID Facebook
            if (settingsCustomerData != null && settingsCustomerData.login != null && settingsCustomerData.login.facebook != null)
            {
                settingsCustomerData.login.facebook.applicationId = "1103870934574590";
            }

            settingsCustomer.InnerText = JsonConvert.SerializeObject(settingsCustomerData);

            dynamic settingsDefaultCustomerData = utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode, IdStructure, resulEvent, int.Parse(idpa), lang, true);

            // Correction temporaire pour l'App ID Facebook
            if (settingsDefaultCustomerData != null && settingsDefaultCustomerData.login != null && settingsDefaultCustomerData.login.facebook != null)
            {
                settingsDefaultCustomerData.login.facebook.applicationId = "1103870934574590";
            }

            settingsDefaultCustomer.InnerText = JsonConvert.SerializeObject(settingsDefaultCustomerData);

            

            /*
            string lang = "";
            if (Session["SVarLangue"] != null)
                lang = Session["SVarLangue"].ToString();


            //setting de la plateform de laquelle on viens qui est dans l'url (plateformCode)
            settingsPlateform.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsPlateformJSON", plateformCode, IdStructure, resulEvent, int.Parse(idpa), lang, false));


            //récuprère la config pour savoir ce qu'il aut afficher 
            settingsCustomer.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode,  IdStructure, resulEvent, int.Parse(idpa), lang, false));
            settingsDefaultCustomer.InnerText = JsonConvert.SerializeObject(utilitaires2010.Initialisations.LoadSettingsOfCustomer("physicalPathOfSettingsJSON", plateformCode, IdStructure, resulEvent, int.Parse(idpa), lang, true));

            */
        }



    
    }
}