﻿
var nb_dispo_high = 200, nb_dispo_medium = 50, nb_dispo_low = 49;

// dispo en pourcent
var nb_dispo_highPC = 50, nb_dispo_mediumPC = 20, nb_dispo_lowPC = 20;

var deviseCode = "€";
var AllGrilleTarif = [];
var isRecheckInput = false;
var strFormules = "";

$(document).ready(function () {
	
	//$('.pagetitle').text(ReadXmlTranslate("title_page_events"));
	
	//todo timer buy ou timerselect???
	startTimer();
	$('#actualtimer .minute_wrapper').prepend(ReadXmlTranslate("msg_before_select_timer") +" ");
	$('#actualtimer .second_wrapper').append(" " + ReadXmlTranslate("msg_after_select_timer"));
	
	if (structureIdCurrent != null) {
		
		var arrFormules = [];
		
	/*	var isClickedBtnValidAllForm = localStorage.getItem('isClickedBtnValidAllForm');
		var objSessionEventChoice = sessionStorage.getObj("objEventsChoice");
		if(settings.events.showMessageGetPreviousChoices && objSessionEventChoice != null && (isClickedBtnValidAllForm != null && isClickedBtnValidAllForm == "false"))
		{
			//si le paramètre permet d'afficher un message pour savoir si on veut récupérer les choix précédents
			var modalText = 'Voulez-vous récupérer les choix précédemment sélectionnés ? Si vous refusez, une redirection sera effectuée vers la 1ère page'
			ShowConfirmModal('Récupérer les choix', modalText,  'Récupérer', 'Ne pas récupérer', 'getPreviousChoices', 'btnRemoveSession');
			 
			BindButtonModal();
			
			//todo mettre en fonction le IF
			//si on click sur le bouton de la modal ou si l'option n'est pas activé
			
		}*/
		
		objChoices = sessionStorage.getObj("objEventsChoice");
		
		$.each(objChoices, function (i, val) {
			var tarif = "";
			$.each(val.listTarifs, function (t, tar) {
				tarif = tarif + ":" + tar.tarifid + tarif;
			});
			arrFormules.push(val.formulaid + tarif);
			
			//si c'est le dernier
			if(i == objChoices.length-1)
				strFormules += val.formulaid;
			else
				strFormules += val.formulaid +':';
		});
			
		LoadEvents(arrFormules);
		
				
		$('#validAllForm').on('click', function(e) {
			e.preventDefault();
			
			//internaute pas connecté
			if(identiteIdCurrent == 0)
			{
				//tempAlert("Veuillez vous identifier");
				ShowConfirmModal("", ReadXmlTranslate("msg_before_identification"), "", "", "", "", false, false, "static");
				$('#lnkconnect').trigger('click');
				sessionStorage.setItem('isClickedBtnValidAllForm', true);
			}else{
				
				//si afficher le message est actif dans le paramètrage
				if(settings.events.showMessageBankCard){
					
					  var modalText = ReadXmlTranslate("msg_alert_warning_bank_card");
                    ShowConfirmModal(ReadXmlTranslate('title_alert_warning_bank_card'), modalText, ReadXmlTranslate('msg_alert_warning_bank_card_yes'), ReadXmlTranslate('msg_alert_warning_bank_card_no'), 'btnConfirmBankCard', 'btnCancelBankCard');

					$('#btnConfirmBankCard').on('click', function(e) {
						bIsFloagAndShowMessage = true;
						FlagsSeats();
						
					});
				
				}else{
					
					FlagsSeats();
				}
			}

		});
			
		
	}else{
			//session perdue
	}
		
	
		//lorsque l'on ferme la modal
		$('#modalGeneric').on('hidden.bs.modal', function (e) {	  
			console.log(e);	  
			ShowSeatsChoicesHA();
			// do something...
		});	
	

});

function unFlag(eventid, sessionid)
{
//alert("unflag " + eventid + " " + sessionid)

    $.ajax({
        type: "POST",
        url: urlajaxabov2 + '/events/unFlag',
        data: {
            structureId: structureIdCurrent,
            identiteId: identiteIdCurrent,
            eventId: eventid,
            sessionId: sessionid
        },
        success: function (retour) {
            //alert(retour);					

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
//            alert(textStatus);
        },
        complete: function () {
            //functioncomplete();
        }
    });
}

function FlagsSeats(){
	
	$('#modalGeneric').modal('hide');
	//CreateBasket();				
	var ChoiceHA = [];
	if(localStorage.getObj("arrPanierHA") != null)				
		ChoiceHA = localStorage.getObj("arrPanierHA");
						
	$.ajax({
		type: "POST",
		url: urlajaxabov2 + '/events/Flag',
		data : {
			structureId: structureIdCurrent,
			identiteId: identiteIdCurrent,
			objChoices: objChoices,
			objChoicesHA: ChoiceHA
		},
		success: function (retour) {
			//alert(retour);					
			if (retour.messageError.length == 0) {
				localStorage.setObj("arrPanier", null);
				localStorage.setObj("arrPanier", retour); 
				url = urlajaxabov2 + "/Summary";
				$(location).attr("href", url);							 
			}
			else
			{				
				var msgT = ReadXmlTranslate("msg_error_plus_place_categ");						
				$.each(retour.messageError, function (idx, msg) {
					msgT += msg.EventName + " " + msg.SessionDescription + ""
				});
												
				//ne s'affiche pas car il est de type hidden a cause de l'alert showMessageBankCard qui s'est fermée précédemment
				if(settings.events.showMessageBankCard){
					alert(msgT);
				}else{
					ShowConfirmModal("", msgT,  "", "", "", "", false, false, "static");
				}
				
				
			}
		},
		error: function (XMLHttpRequest, textStatus, errorThrown) {
			alert(textStatus);
		},
		complete: function () {
			//functioncomplete();
		}
	});
}


//mets a jour l'objet en temps réel
function UpdateObjectTempsReel(formuleId, eventIdSeleted, sessionIdSeleted, categIdSeleted, tarifId, unitTTCAmount, thisForm, action) {
	
	var tarif = $.grep($.grep(objChoices, function(x) { return x.formulaid == formuleId })[0].listTarifs, function(x) { return x.tarifid == tarifId });
	var aboidx = thisForm.closest('.card').data('aboidx');
	var contrainstid = thisForm.find('.aboItems').data('contrainstid');
		
	thisForm.find('select[name^="abocategorie_'+formuleId+'_'+tarifId+'"]').selectpicker('refresh');
		
	var isplacementlibre = thisForm.find('select[name^="abocategorie_'+formuleId+'_'+tarifId+'"]').attr('data-isplacementlibre');
	var ischoixplacesurplan = thisForm.find('select[name^="abocategorie_'+formuleId+'_'+tarifId+'"]').attr('data-ischoixplacesurplan');
	var isvoirplacement = thisForm.find('select[name^="abocategorie_'+formuleId+'_'+tarifId+'"]').attr('data-isvoirplacement');
	
	console.log(' isplacementlibre :' +isplacementlibre  +' ischoixplacesurplan :' + ischoixplacesurplan+ ' isvoirplacement : ' + isvoirplacement)
	
	switch(action) {
		case "add" : 
			if(tarif[0].listAbos == undefined)
					tarif[0].listAbos = [];
			
			//var eventisexist = $.grep(tarif[0].listAbos, function(x) { return x.eventid == eventIdSeleted });
			var eventisexist = $.grep(tarif[0].listAbos, function(x) { return x.idx == aboidx });
			if(eventisexist.length > 0)
			{
				//var hasEventsinListAbo = $.grep(tarif[0].listAbos, function(x) { return x.idx == aboidx });

				//if(hasEventsinListAbo.length  > 0) 
				//{
					var objEvent = CreateEventObject(eventIdSeleted, sessionIdSeleted, categIdSeleted, unitTTCAmount, contrainstid, isplacementlibre, ischoixplacesurplan, isvoirplacement);				
					$.grep(tarif[0].listAbos, function(x) { return x.idx == aboidx })[0].listEvents.push(objEvent);
				//}else{
					
							
				//}
				
				//objreturn = tarif[0].listAbos;
			}else{
				
				var objAbo = {};
					objAbo.formulaid = formuleId;
					objAbo.tarifid = tarifId;
					objAbo.idx = aboidx;	
					objAbo.listEvents = [];
				
					var objEvent = CreateEventObject(eventIdSeleted, sessionIdSeleted, categIdSeleted, unitTTCAmount, contrainstid, isplacementlibre, ischoixplacesurplan, isvoirplacement);
					objAbo.listEvents.push(objEvent);			
					tarif[0].listAbos.push(objAbo);		
			}
		break;
		
		
		case "remove": 
			var tarif = $.grep($.grep(objChoices, function(x) { return x.formulaid == formuleId })[0].listTarifs, function(x) { return x.tarifid == tarifId });
			
			if(tarif.length > 0)
			{
				//récupère aboidx pour avoir ses manifs
				var arrAboIdx = $.grep(tarif[0].listAbos, function(x) { return x.idx == aboidx });
				
				if(arrAboIdx.length > 0)
				{
					//arrAboIdx[0].listEvents = 
					//tarif[0].listAbos
					
					arrAboIdx[0].listEvents = $.grep(arrAboIdx[0].listEvents, function(x) { return x.eventid != eventIdSeleted })
				}
			}
		break;
		
	}
	
	sessionStorage.setObj("objEventsChoice", objChoices);
	
}

//retourne un objet event qui sera dans le tableau listAbos ==> objChoices
function CreateEventObject(eventIdSeleted, sessionIdSeleted, categIdSeleted, unitTTCAmount, contrainstid, isplacementlibre, ischoixplacesurplan, isvoirplacement)
{
	var objEvent = {};				
	objEvent.eventid = eventIdSeleted;
	objEvent.categId = categIdSeleted;				
	objEvent.sessionid = sessionIdSeleted;
	objEvent.unitTTCamount = unitTTCAmount;
	objEvent.constraintId = contrainstid;
	
	objEvent.isplacementlibre = parseInt(isplacementlibre);
	objEvent.ischoixplacesurplan = parseInt(ischoixplacesurplan);
	objEvent.isvoirplacement = parseInt(isvoirplacement);

	var thisEvent = $.grep(events, function (x) { return x.EventId == eventIdSeleted } );
	if(thisEvent.length > 0 ) 
		objEvent.EventName = thisEvent[0].EventName;
	
	var arrSession =  Enumerable.From(Enumerable.From(events).Where(function (x) { return x.EventId == eventIdSeleted }).ToArray()[0].ListSessions).Where(function (x) { return x.SessionId == sessionIdSeleted }).ToArray()[0];
	if (arrSession != undefined)
	{
		objEvent.sessionDescription = arrSession.sSessionStartDate;
		objEvent.lieuName = arrSession.lieuName;					
	}				
	
	var thisCateg =  $.grep(arrSession.listCoupleTarifCateg, function(x) {
		return x.categId == categIdSeleted  
	});

	if(thisCateg != null && thisCateg.length==1)
	{
		objEvent.categName = thisCateg[0].categName
	}
	return objEvent;
}


var abonnements = [];
var events = [];

// ****************************************************************************************
// ***************************************** appels formules + contraintes sont finis -> creation table
// ****************************************************************************************
function ajaxIsComplete() {
    var arrFormules = [];

    var arrFormuleTarif = [];
    var firstformulaid = 0;

    $.each(arrOfEventsOfFormula, function (i, objForm) { // each formule
        $.each(objForm.listEvents, function (ii, objEvent) { // 
            oevent = {};
            oevent["EventId"] = objEvent.EventId;
            oevent["EventName"] = objEvent.EventName;
            oevent["ListSessions"] = objEvent.ListSessionsOfFormula;

            //oevent["tarifid"] = tarif.tarifid;
            if (Enumerable.From(events).Where(function (x) { return x.EventId == objEvent.EventId }).ToArray().length == 0) {
                events.push(oevent)
            }
			else
			{
                // ajout couple Categ/Tarif
                var thisoEventExistant = Enumerable.From(events).Where(function (x) { return x.EventId == objEvent.EventId }).ToArray()[0];

                $.each(objEvent.ListSessionsOfFormula, function (iii, objSession) {
                    thisoSessionExistant = Enumerable.From(thisoEventExistant.ListSessions).Where(function (x) { return x.SessionId == objSession.SessionId }).ToArray()[0];
                    

                    
                    if (thisoSessionExistant != undefined) {
                        $.each(objSession.listCoupleTarifCateg, function (i, objTarifCateg) {
                            thisoSessionExistant.listCoupleTarifCateg.push(objTarifCateg)
                        });
                        //}
                        //else{
                        //	thisoSessionExistant.listCoupleTarifCateg.push(objSession.listCoupleTarifCateg[0])
                        //}
                    }
                    else {
                        // cette séance n'existe pas encore
                        thisoEventExistant.ListSessions.push(objSession);
                    }
                    console.log("thisoSessionExistant");

                });
               // console.log("thisoEvent");
            }  
        });
    });

	
	var $ulNavTabs = $('<!-- tabs nav START --> <ul class="nav nav-pills nav-fill" id="myTab" role="tablist">   </ul>  <!-- tabs nav END -->');
	var $tabContainer = $('<div class="tab-content" id="myTabContent">    </div>');
    var $tabContent = $('<div class="tab-content">    </div>');

	//génère le bouton + et - en SVG
	var svgButton = '<svg class="plusoumoins img-fluid" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 16.4 16.3" style="enable-background:new 0 0 16.4 16.3;" xml:space="preserve"><g ><g><path class="st0" d="M13.4,9.2H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h10.3c0.6,0,1,0.4,1,1S13.9,9.2,13.4,9.2z"/></g><g><g><path class="st0" d="M9.5,16.3c-0.2,0-0.4-0.2-0.5-0.4c0-0.3,0.1-0.5,0.4-0.6c3.4-0.6,5.9-3.6,5.9-7.1c0-4-3.2-7.2-7.2-7.2 S1,4.2,1,8.2c0,3.5,2.5,6.5,5.9,7.1c0.3,0,0.5,0.3,0.4,0.6c0,0.3-0.3,0.5-0.6,0.4C2.8,15.6,0,12.2,0,8.2C0,3.7,3.7,0,8.2,0 c4.5,0,8.2,3.7,8.2,8.2C16.4,12.2,13.5,15.6,9.5,16.3C9.6,16.2,9.6,16.3,9.5,16.3z"/></g></g></g><g class="verticalbar"><g><path class="st0" d="M8.2,14.4c-0.6,0-1-0.4-1-1V3c0-0.6,0.4-1,1-1s1,0.4,1,1v10.3C9.2,13.9,8.7,14.4,8.2,14.4z"/></g></g></svg>';
				
		var cpt = 1;
		$.each(objChoices, function (i, formule) { // each formule choisie
			arrFormules.push(formule.formulaid);
			var thisFormulaId = formule.formulaid;
			
			$contentTab = "";
			if (i == 0) {
				$($ulNavTabs).append(' <li class="nav-item"> <a class="nav-link active" id="abo-tab' + formule.formulaid + '" data-toggle="tab" href="#tababo' + formule.formulaid + '" role="tab" aria-controls="home" aria-selected="true">' + formule.formulaName + '</a></li>');                      
			   $contentTab = $('<div class="tab-pane fade show active" id="tababo' + formule.formulaid + '" role="tabpanel" aria-labelledby="abo-tab">    </div>');          
			}
			else {
				 $($ulNavTabs).append(' <li class="nav-item"> <a class="nav-link" id="abo-tab' + formule.formulaid + '" data-toggle="tab" href="#tababo' + formule.formulaid + '" role="tab" aria-controls="home" aria-selected="true">' + formule.formulaName + '</a></li>');                       
				$contentTab = $('<div class="tab-pane fade" id="tababo' + formule.formulaid + '" role="tabpanel" aria-labelledby="abo-tab">    </div>');           
			}
			//ajout les ul li (NAVBAR) au body
			$('#content').append($ulNavTabs);
			
			//START CONTENU
			$colOfContent = $('<div class="col"> </div>');
			
				//START INTRO
				var imgpath='Content/img';		
				$divIntro = $('<div class="tab-intro">	</div>');			
				$aboTitle = $('<div class="abotitle"> </div>');
				
				 //récupère la liste des contraintes
				var thisConstraintes = Enumerable.From(arrOfConstraintesOfFormula).Where(function (x) {
					return x.formulaId == formule.formulaid;
				}).ToArray();

				var txtConstraintes = "<span><strong>"+formule.formulaName+"</strong> </span><span><small>";
				
				var templatetextConstrainte = ReadXmlTranslate("lbl_explication_constrainte");
				
				var constrainstInitialised = false;
				if (thisConstraintes.length > 0) {
					$.each(thisConstraintes[0].listConstraints, function (constridx, oconstrainte) {
						//si on est jamais passé dans la boucle on ne mets pas &
						
						var thisTextExplicConstrainte = templatetextConstrainte.replace("[nmin]", oconstrainte.nb_manifMin);
						thisTextExplicConstrainte = thisTextExplicConstrainte.replace("[nmax]", oconstrainte.nb_manifMax);
						
						if(!constrainstInitialised)						
							txtConstraintes +=  oconstrainte.name + ' ' + thisTextExplicConstrainte;
						else
							txtConstraintes += ' & ' +  oconstrainte.name+' ' + thisTextExplicConstrainte;
						
						constrainstInitialised = true;
					});
				}
				txtConstraintes += '</small></span>';			
				$divContrainst = txtConstraintes;			
				$aboTitle.append($divContrainst);
				
				//ajoute le contenu INTRO
				$divIntro.append($aboTitle);
				
				var dispoFaible = ReadXmlTranslate("lbl_dispo_tres_faible");
				var dispoMoyenne = ReadXmlTranslate("lbl_dispo_moyenne");
				var dispoElevee = ReadXmlTranslate("lbl_dispo_elevee");

				var $row = '<div class="row"><div class="col-auto"><img src="'+urlajaxabov2+'/Content/img/dispo-red.png" class="align-baseline">' + dispoFaible + '</div>' + '<div class="col-auto"><img src="'+urlajaxabov2+'/Content/img/dispo-orange.png" class="align-baseline">' + dispoMoyenne + '</div><div class="col-auto"><img src="'+urlajaxabov2+'/Content/img/dispo-green.png" class="align-baseline">' + dispoElevee + '</div></div>';
				$divIntro.append($row);
				$colOfContent.append($divIntro);
				//END INTRO
			
			
			//START ACCORDION Loop abonné
			$accordion = $('<div id="accordion"> </div>');
			
			var ntt = 0;
			var card = '';
			
			$.each(formule.listTarifs, function (ii, tarif) { // **** each tarif dans la formule
				var thisTarifId = tarif.tarifid;
				for (var i = 0; i < tarif.nb; i++) { // **** each abo dans le tarif
				
					var thisConstraintes = Enumerable.From(arrOfConstraintesOfFormula).Where(function (x) {
						return x.formulaId == formule.formulaid
					}).ToArray();
				
					//calcul de la somme min/max des contraintes individuelles egales a la contrainte générale
					var sumMin = 0, sumMax = 0; 
					$.each(thisConstraintes[0].listConstraints, function(indx, item) { 
						sumMin += item.nb_manifMin; 
						sumMax += item.nb_manifMax 
					})

					var classcontraintegeneralevisible = "";
					//contrainte générale == somme contraintes individuelles // on cache le message generale
					if(sumMin == formule.nbmanifmin && (sumMax == formule.nbmanifmax || formule.nbmanifmax == 99))
					{
						classcontraintegeneralevisible = "d-none";
					}else{
						classcontraintegeneralevisible = "";
					}
					
				
					card += '<div class="card" data-formuleid="'+formule.formulaid+'" data-tarifid="'+thisTarifId+'" data-aboidx="'+cpt+'" data-nbmanifmax="'+formule.nbmanifmax+'" data-nbmanifmin="'+formule.nbmanifmin+'">';
					if(cpt == 1)
					{	
						card += '<div class="card-header" id="heading-'+formule.formulaid+'-'+thisTarifId+'-'+cpt+'"><h5 class="mb-0"><button class="btn btn-link" data-toggle="collapse" data-target="#abo-'+formule.formulaid+'-'+thisTarifId+'-colappse'+cpt+'" aria-expanded="true" aria-controls="collapse'+thisTarifId+'"> '+svgButton+'<strong>'+ReadXmlTranslate("lbl_abonne_number").replace('[NUMBER]', cpt) +'</strong> ('+tarif.tarifName+')  <span class="badge badge-secondary nbevent"></span> <span class="generale-contrainte '+classcontraintegeneralevisible+'">'+ReadXmlTranslate("lbl_contrainte_generale").replace('[MIN]', formule.nbmanifmin).replace('[MAX]', formule.nbmanifmax)+'</span></button></h5></div>';
						card += '<div id="abo-'+formule.formulaid+'-'+thisTarifId+'-colappse'+cpt+'" class="collapse multi-collapse show" aria-labelledby="heading-'+formule.formulaid+'-'+thisTarifId+'-'+cpt+'"><div class="card-body">';
					} else
					{
						card += '<div class="card-header" id="heading-'+formule.formulaid+'-'+thisTarifId+'-'+cpt+'"><h5 class="mb-0"><button class="btn btn-link collapsed" data-toggle="collapse" data-target="#abo-'+formule.formulaid+'-'+thisTarifId+'-colappse'+cpt+'" aria-expanded="false" aria-controls="collapse'+thisTarifId+'"> '+svgButton+'<strong>' + ReadXmlTranslate("lbl_abonne_number").replace('[NUMBER]', cpt) +'</strong> ('+tarif.tarifName+')  <span class="badge badge-secondary nbevent"></span> <span class="generale-contrainte '+classcontraintegeneralevisible+'">'+ReadXmlTranslate("lbl_contrainte_generale").replace('[MIN]', formule.nbmanifmin).replace('[MAX]', formule.nbmanifmax)+'</span> </button> </h5>    </div>';
						card += '<div id="abo-'+formule.formulaid+'-'+thisTarifId+'-colappse'+cpt+'" class="collapse multi-collapse" aria-labelledby="heading-'+formule.formulaid+'-'+thisTarifId+'-'+cpt+'" ><div class="card-body">';
					}
				
					if(thisConstraintes.length > 0)
					{
						//pour chaque contrainte on génere une form de select
						$.each(thisConstraintes[0].listConstraints, function(indx, item) {
							//si min == 0 on ajoute la classe valid
							
							var thisEventsOfFormula = Enumerable.From(arrOfEventsOfFormula).Where(function (x) {
								return x.formulaId == formule.formulaid;
							}).ToArray();
							
							//récupère les manifestations de cette contrainte
							var queryEventList = $.grep(thisEventsOfFormula[0].listEvents, function(itemevent) {															
								return $.grep(itemevent.listManagementRuleId, function (itemRule) {
									return itemRule == item.groupeId;
								}).length == 1;
								//return itemevent.managementRuleId == item.groupeId;
							});
							
							//regarde si la manifestation n'est pas obligatoire
							var eventismandatory = $.grep(thisEventsOfFormula[0].listEvents, function (x) {
								return x.isMandatory == true
							});

							//ajout un data-eventmandatory=true lorsqu'il y a au moins un evenement obligatoire
							var datahaseventmandatory = false;
							if (eventismandatory.length == 1)
							{
								datahaseventmandatory = true;
							}
							console.log("min=" + item.nb_manifMin + ", max=" + item.nb_manifMax + ", cnt=" + queryEventList.length);				
							var isContrainteFermee = (item.nb_manifMin==item.nb_manifMax && item.nb_manifMax==queryEventList.length)								
							console.log("isContrainteFermee=" + isContrainteFermee);							
													
							
							var contrainteclass="abogroup";
							if(item.nb_manifMin == 0)								
								contrainteclass+=" valid";
							if(isContrainteFermee)								
								contrainteclass+=" ferme";
							
							var templatetextConstrainte = ReadXmlTranslate("lbl_explication_constrainte");
							var txtThisExplic = templatetextConstrainte.replace("[nmin]", item.nb_manifMin);
							txtThisExplic = txtThisExplic.replace("[nmax]", item.nb_manifMax);
							
							card += '<span class="' + contrainteclass + '" data-eventmandatory="'+datahaseventmandatory+'" data-iscontraintefermee="'+isContrainteFermee+'" data-min="'+item.nb_manifMin+'" data-max="'+item.nb_manifMax+'" data-contrainstid="'+item.groupeId+'">'+item.name+' <small>' + txtThisExplic + '</small></span>';							
													
							card += '<form class="mt-2 needs-validation" data-iscontraintefermee="'+isContrainteFermee+'" data-groupid="'+item.groupeId+'" name="cat'+item.groupeId+'" novalidate><div class="form-row select-row">';
							card += '<div class="col-12 col-md-2 col-lg mb-2 divabospectacles"><label class="sr-only" for="abospectacles_'+formule.formulaid+'_'+thisTarifId+'">liste des spectacles</label>';

								//si le min de la contrainte est supérieur a 0 on ajout required
								var required = '';
								if(item.nb_manifMin > 0)								
									required = 'required';
								
								card += '<select class="form-control selectpicker" id="abospectacles_'+formule.formulaid+'_'+thisTarifId+'" data-formuleid="'+formule.formulaid+'" data-tarifid="'+thisTarifId+'" " name="abospectacles" '+required+' data-validation="required" data-live-search="true">';
								card += '<option value="" selected>'+ReadXmlTranslate("select_spectacle")+'</option>';
								if (queryEventList.length > 0) {
									
									$.each(queryEventList, function(indx, itmevent){
										
										var nbDispoSession = 0;
										var dispoSession = $.grep(itmevent.ListSessionsOfFormula, function (y) {
											return y.dispo > 0;
										});

										if (dispoSession.length > 0)
											nbDispoSession = dispoSession[0].dispo;

										
										if(nbDispoSession == 0)
										{
											//si on affiche le message manifs indispo
											if(settings.events.showMessageEventsIndispo)
											{
												card += '<option value="'+itmevent.EventId+'" data-eventid="'+itmevent.EventId+'" data-nbdispo="'+nbDispoSession+'" disabled>'+itmevent.EventName+' '+settings.events.messageEventsIndispo+'</option>';																
											}else{
												card += '<option value="'+itmevent.EventId+'" data-eventid="'+itmevent.EventId+'" data-nbdispo="'+nbDispoSession+'" disabled>'+itmevent.EventName+'</option>';																
											}
											
										}else
											card += '<option value="'+itmevent.EventId+'" data-eventid="'+itmevent.EventId+'" data-nbdispo="'+nbDispoSession+'" data-ismandatory="' + itmevent.isMandatory + '">'+itmevent.EventName+' </option>';																

										/*
										if(nbDispoSession == 0)
										{
											if(settings.events.disabledIndispo)
												card += '<option value="'+itmevent.EventId+'" data-eventid="'+itmevent.EventId+'" data-nbdispo="'+nbDispoSession+'" disabled>'+itmevent.EventName+' '+settings.events.messageIndispo+' ('+nbDispoSession+')</option>';																
											else 
												card += '<option value="'+itmevent.EventId+'" data-eventid="'+itmevent.EventId+'" data-nbdispo="'+nbDispoSession+'" >'+itmevent.EventName+' '+settings.events.messageIndispo+' ('+nbDispoSession+')</option>';																
										}
										else
											card += '<option value="'+itmevent.EventId+'" data-eventid="'+itmevent.EventId+'" data-nbdispo="'+nbDispoSession+'">'+itmevent.EventName+' ('+nbDispoSession+')</option>';																
									
										*/
									});
									}
								card += '</select>';
					
								card +='</div>'; 
						
								//START select date
								card += '<div class="col-12 col-md-2 col-lg mb-2 divabodate"><label class="sr-only" for="abodate_'+formule.formulaid+'_'+thisTarifId+'">Date</label>';
										card += '<select class="form-control selectpicker" id="abodate_'+formule.formulaid+'_'+thisTarifId+'" name="abodate_'+formule.formulaid+'_'+thisTarifId+'" disabled '+required+'>';
										card += '<option value="" selected>'+ReadXmlTranslate("select_date") +'</option>';
										card += '</select>';			
								card +='</div>'; 
								//END select date							
								
								//START select zone
								card += '<div class="col-12 col-md-2 col-lg mb-2 d-none divabozone"><label class="sr-only" for="abozone_'+formule.formulaid+'_'+thisTarifId+'">Zone</label>';
										card += '<select class="form-control selectpicker" id="abozone_'+formule.formulaid+'_'+thisTarifId+'" name="abozone_'+formule.formulaid+'_'+thisTarifId+'"  disabled '+required+'>';
										card += '<option value="" selected>'+ReadXmlTranslate("select_zone") +'</option>';
										card += '</select>';			
								card +='</div>'; 
								//END select zone
								
								//START select etage
								card += '<div class="col-12 col-md-2 col-lg mb-2 d-none divaboetage"><label class="sr-only" for="aboetage_'+formule.formulaid+'_'+thisTarifId+'">Étage</label>';
										card += '<select class="form-control selectpicker" id="aboetage_'+formule.formulaid+'_'+thisTarifId+'" name="aboetage_'+formule.formulaid+'_'+thisTarifId+'" disabled '+required+'>';
										card += '<option value="" selected>'+ReadXmlTranslate("select_etage") +'</option>';
										card += '</select>';			
								card +='</div>'; 
								//END select etage
								
								//START select section
								card += '<div class="col-12 col-md-2 col-lg mb-2 d-none divabosection"><label class="sr-only" for="abosection_'+formule.formulaid+'_'+thisTarifId+'">Section</label>';
										card += '<select class="form-control selectpicker" id="abosection_'+formule.formulaid+'_'+thisTarifId+'" name="abosection_'+formule.formulaid+'_'+thisTarifId+'" disabled '+required+'>';
										card += '<option value="" selected>' + ReadXmlTranslate("select_section") + '</option>';
										card += '</select>';			
								card +='</div>'; 
								//END select section
								
								//START select catégorie
								card += '<div class="col-12 col-md-2 col-lg mb-2 divabocategorie"><label class="sr-only" for="abocategorie_'+formule.formulaid+'_'+thisTarifId+'">Catégorie</label>';						
										card += '<select class="form-control selectpicker" id="abocategorie_'+formule.formulaid+'_'+thisTarifId+'" name="abocategorie_'+formule.formulaid+'_'+thisTarifId+'" disabled '+required+'>';
										card += '<option value="" selected>' +ReadXmlTranslate("select_categorie") + '</option>';
										card += '</select>';			
								card +='</div>'; 
								//END select catégorie
								
								//ajoute les boutons 
								card += '<div class="col-12 col-md-auto mb-2"><button type="submit" class="col-12 col-sm btn btn-secondary " name="addAbo">' +ReadXmlTranslate("btn_add_one_abonne") + '</button></div>';
								card += '<div class="col-12 col-md-auto mb-2 align-self-center text-center">'+ReadXmlTranslate("lbl_or")+'</div>';

								//si il y a 1 seule formule 									
								if (formule.listTarifs.length == 1) {
									//on regarde si il y a qu'un seul abonné alors on désactive le bouton d'ajout sur tous les abonnés
									if (formule.listTarifs[0].nb == 1) {
										card += '<div class="col-12 col-md-auto mb-2"><button type="submit" class="col-12 col-sm btn btn-secondary btn-secondary-light" disabled name="addAboAll">' +ReadXmlTranslate("btn_add_all_abonnes") + '</button></div>';
									} else {
										card += '<div class="col-12 col-md-auto mb-2"><button type="submit" class="col-12 col-sm btn btn-secondary btn-secondary-light" name="addAboAll">' +ReadXmlTranslate("btn_add_all_abonnes") + '</button></div>';
									}
								} else
									card += '<div class="col-12 col-md-auto mb-2"><button type="submit" class="col-12 col-sm btn btn-secondary btn-secondary-light" name="addAboAll">' +ReadXmlTranslate("btn_add_all_abonnes") + '</button></div>';

								card += '</div><!-- END <div class="form-row"> --> <div class="aboItems" data-contrainstid="'+item.groupeId+'"></div> </form>';
						});
					}
					
					card += '</div></div></div> <!-- END card BODY -->';
				
					cpt++;
					//END ACCORDION Loop abonné
				}
			});
				
			//	card += '';
			$accordion.append(card);
			
			$colOfContent.append($accordion);
			//END CONTENU
			
			
			//ajoute la div COL au tab-pane
			$contentTab.append($colOfContent);

			//ajoute la div tab-pane au tab-content
			$tabContainer.append($contentTab);
			
			$('#content').append($tabContainer);

			// init select picker
			$('.selectpicker').selectpicker({
				style: 'btn-default'
			});
	
	});
	
	
	var $divSpectacleExtras = $('<div id="spectacleExtras" class="row"></div>');
	$('#content').append($divSpectacleExtras);

	/********* VALIDATION FORM SELECT *********/
	var thisbutton ='';
	$('button[type="submit"]').on('click', function(){
		thisbutton = $(this).attr('name');
	});


	//$('button[name="addAbo"], button[name="addAboAll"] ').on('click', function(event){
		$('form').submit(function (event) {
			event.preventDefault();
    		event.stopPropagation();
			
			var forms = $('.needs-validation');
			 // var validation = $.each(forms, function(form) {
			var form = $(this).closest('form');
    	
    		if (form[0].checkValidity() === false) {
    			event.preventDefault();
    			event.stopPropagation();
    			switch(thisbutton) {
    				case 'addAbo':
    					//tempAlert(ReadXmlTranslate("msg_alert_warning_select_options"));
						ShowConfirmModal("", ReadXmlTranslate("msg_alert_warning_select_options"), "", "", "", "", false, false, "static");
    					break;
    				case 'addAboAll':
						ShowConfirmModal("", ReadXmlTranslate("msg_alert_warning_select_options"), "", "", "", "", false, false, "static");
    					//tempAlert(ReadXmlTranslate("msg_alert_warning_select_options"));
    					break;
    				case 'addExtraShow':
    					break;
    				default:
    			}
    			form[0].classList.add('was-validated');
    		}else{
    			switch(thisbutton) {
    				case 'addAbo':
    				addSpectacleForOne($(this));
    				break;
    				case 'addAboAll':
    				//console.log('addAboAll')
    				addSpectacleForAll($(this));
    				break;
    				case 'addExtraShow':
    				//console.log('addExtraShow')
    				addExtraSpectacle($(this));
    				break;
    				default:
    			}
    		}
    		//form[0].classList.add('was-validated');
    	
			
		});
	
		/********* FIN VALIDATION FORM SELECT *********/			
	//charge le tarif lorsque l'on change la manif
	$('select[name="abospectacles"]').on('change', function() {
		//console.log($(this));
		var formuleid = $(this).data('formuleid');
		var tarifid = $(this).data('tarifid');
        var eventid = $(this).val();
        var groupeId = $(this).closest('form').data('groupid');
		
		//charge la liste des séances
		var thisEvent = $.grep(events, function(x) {
		  return x.EventId == eventid;
		});
		
		if(thisEvent.length > 0 && eventid > 0)
		{
			//var lstSeances = thisEvent[0].ListSessions
			if($(this).closest('.card').find('select[name="abodate_'+formuleid+'_'+tarifid+'"]').length > 0)
			{				
				//**** REMPLI liste des dates ************/				
				//$selectSeance = $(this).closest('.card').find('select[name="abodate_'+formuleid+'_'+tarifid+'"]');
				$selectSeance = $(this).closest('.select-row').find('select[name="abodate_'+formuleid+'_'+tarifid+'"]');
				//$selectCateg = $(this).closest('.select-row').find('select[name="abocategorie_'+formuleid+'_'+tarifid+'"]');
								
				$selectSeance.empty()
				$selectSeance.append($("<option />").val('').text(ReadXmlTranslate('select_date')));
				
				//<option data-dispo="high" data-thumbnail="/abonext/Content/img/dispo-green.png" value="jeudi 25 janvier 2018 - 19:30">jeudi 25 janvier 2018 - 19:30</option>
                var lstSeancesByGroup = $.grep(thisEvent[0].ListSessions, function (x) {
                    return x.managementRuleId == groupeId;
                });


				$.each(lstSeancesByGroup, function(indx, itemsession){
					var dateThisSession  = convertToDate(itemsession.SessionStartDate);								
											
					var strdispo = "";
					var thumbnaildispo = "";
					
					if (itemsession.dispoTotal==0)
					{															
						if(itemsession.dispo >= nb_dispo_high)
						{
							strdispo = "high";
							//thumbnaildispo = urlajaxabov2+'/Content/img/dispo-green.png';
							thumbnaildispo = '<img src="'+urlajaxabov2+'/Content/img/dispo-green.png"  /> '+itemsession.sSessionStartDate+'';
						}else if(itemsession.dispo >= nb_dispo_medium )
						{
							strdispo = "medium";
							//thumbnaildispo = urlajaxabov2+'/Content/img/dispo-orange.png';
							thumbnaildispo = '<img src="'+urlajaxabov2+'/Content/img/dispo-orange.png"  /> '+itemsession.sSessionStartDate+'';
						}else if(itemsession.dispo <= nb_dispo_low )
						{
							strdispo = "low";
							//thumbnaildispo = urlajaxabov2+'/Content/img/dispo-red.png';
							thumbnaildispo = '<img src="'+urlajaxabov2+'/Content/img/dispo-red.png" /> '+itemsession.sSessionStartDate+'';
						}
					}
					else // en mode pourcentage
					{
						var dispopc = itemsession.dispo / itemsession.dispoTotal * 100;
						console.log("pc=" + itemsession.dispo + "/" +  itemsession.dispoTotal + "=" + dispopc);
						
						if(dispopc >= nb_dispo_highPC)
						{
							strdispo = "high";
							//thumbnaildispo = urlajaxabov2+'/Content/img/dispo-green.png';
							thumbnaildispo = '<img src="'+urlajaxabov2+'/Content/img/dispo-green.png"  /> '+itemsession.sSessionStartDate+'';
						}else if(dispopc >= nb_dispo_mediumPC )
						{
							strdispo = "medium";
							//thumbnaildispo = urlajaxabov2+'/Content/img/dispo-orange.png';
							thumbnaildispo = '<img src="'+urlajaxabov2+'/Content/img/dispo-orange.png"  /> '+itemsession.sSessionStartDate+'';
						}else 
						{
							strdispo = "low";
							//thumbnaildispo = urlajaxabov2+'/Content/img/dispo-red.png';
							thumbnaildispo = '<img src="'+urlajaxabov2+'/Content/img/dispo-red.png" /> '+itemsession.sSessionStartDate+'';
						}						
						
						
					}
						
					
					
					//$selectSeance.append($("<option />").val(itemsession.SessionId).text(itemsession.sSessionStartDate).attr('data-dispo', strdispo).attr('data-thumbnail', thumbnaildispo).attr('data-sessionid', itemsession.SessionId));
			
					// console.log($(this));
					
					
					
					//si la date est 01/01/2099
					if(dateThisSession.getFullYear() == 2099 && dateThisSession.getMonth()+1 == 1 && dateThisSession.getDate() == 1)
					{
						$selectSeance.append($("<option />").val(itemsession.SessionId).text(ReadXmlTranslate("lbl_date_libre")).attr('data-dispo', strdispo).attr('data-content', thumbnaildispo).attr('data-sessionid', itemsession.SessionId));				
					}else {						
						$selectSeance.append($("<option />").val(itemsession.SessionId).text(itemsession.sSessionStartDate).attr('data-dispo', strdispo).attr('data-content', thumbnaildispo).attr('data-sessionid', itemsession.SessionId));				
					}

				});
								
				$selectSeance.removeAttr('disabled');							
						
				//si il y a qu'une seance on selectionne
				if($(this).closest('.select-row').find('select[name="abodate_'+formuleid+'_'+tarifid+'"] option[value!=""]').length == 1)
					$(this).closest('.select-row').find('select[name="abodate_'+formuleid+'_'+tarifid+'"] option[value!=""]').attr('selected', true);
							
				//si il y a qu'une catégorie on selectionne
				//if($(this).closest('.select-row').find('select[name="abocategorie_'+formuleid+'_'+tarifid+'"] option[value!=""]').length == 1)
				//	$(this).closest('.select-row').find('select[name="abocategorie_'+formuleid+'_'+tarifid+'"] option[value!=""]').attr('selected', true);
				
				$selectSeance.selectpicker('refresh');
				
				$selectSeance.trigger('change');
				//$selectCateg.selectpicker('refresh');
			}
			
		}else{
			
			//sinon la manif est = a 0 donc on reinitialise les autres select
			//$('select[name^="abodate"]')
			console.log($(this));
			
			var thisForm = $(this).closest('form').get(0);
			thisForm.reset();
			
			$(thisForm).find('.selectpicker option[value=""]').attr('selected', true);			
			$(thisForm).find('.selectpicker').selectpicker('refresh');
		}
		
	});		
	
	
	$('select[name^="abodate"]').on('change', function() {
	
		/*	var formuleid = $(this).data('formuleid');
		var tarifid = $(this).data('tarifid');
		var eventid = $(this).val();
		*/
		var formuleid = $(this).closest('.card').find('select[name="abospectacles"]').data('formuleid');
		var tarifid = $(this).closest('.card').find('select[name="abospectacles"]').data('tarifid');
		//var eventid=$(this).closest('.card').find('select[name="abospectacles"] option:selected').val();
		var eventid= $(this).closest('form').find('select[name="abospectacles"] option:selected').val();
		
		$selectCateg = $(this).closest('.select-row').find('select[name="abocategorie_'+formuleid+'_'+tarifid+'"]');
		
		console.log($(this));
			
		var thisArrEvent = $.grep(events, function(x) {
		  return x.EventId == eventid;
		});
		
		var thisSessionSelected = $(this).val();
		if(thisArrEvent.length > 0 && thisSessionSelected != "")
		{
			$selectCateg.removeAttr('disabled');
					
			var thisArrCategOfSession = $.grep(thisArrEvent[0].ListSessions, function(c) {
			  return c.SessionId == thisSessionSelected;
			});
			
			if(thisArrCategOfSession.length>0)
			{

				if(thisArrCategOfSession[0].listCoupleTarifCateg.length > 0){
						
					// créer un tableau distinct sur la categid
					var arrayOutput = [];

					$.each(thisArrCategOfSession[0].listCoupleTarifCateg, function(index, event) {
						
						//map le tarif avec la categ
						if (event.tarifId == tarifid) {
							var events2 = $.grep(arrayOutput, function (e) {
								return event.categId == e.categId; //&& event.tarifId == e.tarifId;
							});

							if (events2.length === 0) {
							  arrayOutput.push(event);
							}
						}
					});

					
					var categIdToPreSelect=0;
					var categDisponibles = $.grep(arrayOutput, function (e) {return e.dispo>0;})
					if (categDisponibles.length==1)
					{
						categIdToPreSelect = categDisponibles[0].categId
					}
					$selectCateg.empty();
					$selectCateg.append($("<option />").val('').text(ReadXmlTranslate('select_categorie')));
					$.each(arrayOutput, function(indx, itemtarifcateg){
						
						if(itemtarifcateg.dispo == 0)
						{
							//si la categ est indispo et qu'un message est paramétré 
							if(settings.events.showMessageCategIndispo) {
								$selectCateg.append($("<option />").val(itemtarifcateg.categId).text(itemtarifcateg.categName.substr(0, itemtarifcateg.categName.indexOf('/*')) + ' ' + settings.events.messageCategIndispo)
											.attr('data-nbdispo', itemtarifcateg.dispo).prop('disabled', true))	
							}else {
								$selectCateg.append($("<option />").val(itemtarifcateg.categId).text(itemtarifcateg.categName.substr(0, itemtarifcateg.categName.indexOf('/*'))).attr('data-nbdispo', itemtarifcateg.dispo).prop('disabled', true))					
							}
							$selectCateg.selectpicker('refresh');
							/*
							//si la catég est indispo et que 
							if(settings.events.disabledCategoriesIndispo && settings.events.showMessageCategIndispo) {			
								//Desactive + affiche le message
								$selectCateg.append($("<option />").val(itemtarifcateg.categId).text(itemtarifcateg.categName.substr(0, itemtarifcateg.categName.indexOf('/*')) + ' ' + settings.events.messageCategIndispo).attr('data-nbdispo', itemtarifcateg.dispo).prop('disabled', true))	
							} else if(settings.events.disabledCategoriesIndispo && !settings.events.showMessageCategIndispo) {
								//Desactive sans afficher le message
								$selectCateg.append($("<option />").val(itemtarifcateg.categId).text(itemtarifcateg.categName.substr(0, itemtarifcateg.categName.indexOf('/*'))).attr('data-nbdispo', itemtarifcateg.dispo).prop('disabled', true))	
							}else {
								$selectCateg.append($("<option />").val(itemtarifcateg.categId).text(itemtarifcateg.categName.substr(0, itemtarifcateg.categName.indexOf('/*'))).attr('data-nbdispo', itemtarifcateg.dispo).prop('disabled', true))					
							}
							*/
						}else{
							$selectCateg.append($("<option />").val(itemtarifcateg.categId).text(itemtarifcateg.categName).attr('data-nbdispo', itemtarifcateg.dispo))
										.attr('data-gpId', itemtarifcateg.gpId).attr('data-isplacementlibre', itemtarifcateg.isplacementlibre)
										//.attr('data-ischoixauto', itemtarifcateg.ischoixauto)
										.attr('data-ischoixplacesurplan', itemtarifcateg.ischoixplacesurplan)
										.attr('data-isvoirplacement', itemtarifcateg.isvoirplacement)			
						}
						
					});
					
					if (categIdToPreSelect!=0)
					{
						$selectCateg.find('option[value="' + categIdToPreSelect + '"]').prop('selected','selected');
					}
					
					//si il y a qu'une catégorie on selectionne
					if($(this).closest('.select-row').find('select[name="abocategorie_'+formuleid+'_'+tarifid+'"] option[value!=""]').length == 1)
						$(this).closest('.select-row').find('select[name="abocategorie_'+formuleid+'_'+tarifid+'"] option[value!=""]').attr('selected', true);
					
					$selectCateg.selectpicker('refresh');
					
				}
			}		
		}
	});
		
	/********* GESTION ABO FERMEE *********/
	
	$.each($('.abogroup[data-iscontraintefermee="true"], .abogroup[data-eventmandatory="true"]'), function(indx, itemabogroup) {
		
		var ismandatory = $(this).data('eventmandatory');
        //si c'est undefined ou false on met false sinon on met true
        var isContrainteFermee = ($(this).data('iscontraintefermee') != true) ? false : true;
		var thismanagementruleid = $(itemabogroup).data('contrainstid');		
		//récupère l'id de la contrainte
		var thisgroupeid = $(itemabogroup).data('contrainstid');
		
		console.log(itemabogroup);
		//Cache la poubelle et la ligne des selects
		var isToutAjoute = true;
		//pour chaque manif (se positionne sur le form qui a la contrainte récupérée 
		$.each($(this).closest('.card-body').find('form[data-iscontraintefermee="'+isContrainteFermee+'"][data-groupid="'+thisgroupeid+'"]').find('.divabospectacles select option'), function(indx, itemevent) {
			
			var isAjouterInAboItem = false;
			var thisevent = $.grep(events, function(x) { return x.EventId == $(itemevent).data('eventid') });
			
			if(thisevent.length > 0)
			{
				//si on est en contrainte fermée ou manifestation obligatoire alors on ajoute la ligne
                if ($(itemevent).data('ismandatory') || isContrainteFermee) {
					//si 1 seance et 1 catégorie alors on sélectionne
					var thisForm = $(this).closest('.card-body').find('form[data-iscontraintefermee="'+isContrainteFermee+'"][data-groupid="'+thisgroupeid+'"]');
					
					var thistarifid = $(itemevent).parent().data("tarifid")
					if(thisevent[0].ListSessions.length == 1)
					{									
						
						var thisformuleid = $(this).closest('.card').data('formuleid');
					
						//récupère la manifestation de cette formule 
						var thisEventsOfFormula = Enumerable.From(arrOfEventsOfFormula).Where(function (x) {
							return x.formulaId == thisformuleid;
						}).ToArray();
						
						
						var thiseventofmanagementrule = $.grep(thisEventsOfFormula[0].listEvents, function(x) {  
						  return x.managementRuleId == thismanagementruleid && x.EventId == thisevent[0].EventId;					
						});
						
							
						var categdispo = $.grep(thiseventofmanagementrule[0].ListSessionsOfFormula[0].listCoupleTarifCateg, function(x) {  
						  return x.dispo > 0 && x.tarifId == thistarifid;					
						});

						if(categdispo.length == 1)
						{
							GetEventsChoice();
							
							//Vérifie si le select n'est pas désactivé (==> déjà dans l'objet)
							if(!$(thisForm).find('select[name="abospectacles"] option[value="'+thisevent[0].EventId+'"]').hasAttr('disabled'))
							{
										
								$(thisForm).find('select[name="abospectacles"]').val(thisevent[0].EventId);
								$(thisForm).find('select[name="abospectacles"]').trigger('change');
								
								$(thisForm).find('select[name^="abodate_"]').val(thisevent[0].ListSessions[0].SessionId);
								$(thisForm).find('select[name^="abodate_"]').trigger('change');
								
								
								//$(thisForm).find('select[name="abocategorie_'+thisformuleId+'_'+thistarifId+'"]').val(thisevent[0].ListSessions[0].listCoupleTarifCateg[0].categId)
								$(thisForm).find('select[name^="abocategorie_"]').val(categdispo[0].categId)
								$(thisForm).find('select[name^="abocategorie_"]').trigger('change');
										
								
								//$(constructAddSpectable(thisForm, thisevent[0].unitTTCamount)).appendTo($(thisForm).find('.aboItems'));
								constructObjectEventChoice(thisForm, 'add')
									
									
								isAjouterInAboItem = true;	
									
							}
							
						}
						
					} //
					
					//si la manif n'a pas été ajoutée et que l'on est pas en mode manif obligatoire
                    if (!isAjouterInAboItem && !ismandatory) 
					{
						if(!$(thisForm).find('select[name="abospectacles"] option[value="'+thisevent[0].EventId+'"]').hasAttr('disabled'))
						{
							$(thisForm).find('select[name="abospectacles"] option[value="'+thisevent[0].EventId+'"]').attr('data-nextevent', true);
						
						//$(thisForm).find('select[name="abospectacles"]').val(thisevent[0].EventId);
						//$(thisForm).find('select[name="abospectacles"]').trigger('change');
								
						console.log('add class next');
						isToutAjoute = false;
							
						}
						
					}
				}
			}
		});

		//si toutes les lignes ont été ajoutées
		if (isToutAjoute)
        {
			//cache les select de la contrainte en cours (fermée)
			//on ne cache pas la ligne des select si on est en manifestation obligatoire
			if(!ismandatory)             
				$(this).closest('.card-body').find('form[data-iscontraintefermee="'+isContrainteFermee+'"][data-groupid="'+thisgroupeid+'"]').find('div.select-row').hide();
            
			$(this).closest('.card-body').find('form[data-iscontraintefermee="'+isContrainteFermee+'"][data-groupid="'+thisgroupeid+'"]').find('i.fa-trash').hide();
            //$(this).parent().find('i.fa-trash').hide();
			
        }
	});
	
	/*
	$.each($('form[data-isContrainteFermee="true"]'), function(indx, itemform) {
		
		console.log(itemform);
	
		if(!$(itemform).find('.divabospectacles select option[data-nextevent="true"]').hasAttr('disabled'))
		{
			var eventid = $(itemform).find('.divabospectacles select option[data-nextevent="true"]').data('eventid');
			
			$(itemform).find('select[name="abospectacles"]').val(eventid);
			$(itemform).find('select[name="abospectacles"]').trigger('change');
			
			console.log('select next');
			
		}
		
		//$(itemform).find('.divabospectacles select option[data-nextevent="true"]')
		
	
		
	});
	*/
	
	
	/********* FIN GESTION ABO FERMEE *********/
	LoadEventsHA(strFormules);
				
}

//convertit une date de ce format "/Date(1245398693390)/" en Date
function convertToDate(datedotnet)
{	
	var re = /-?\d+/; 
	var m = re.exec(datedotnet); 
	return new Date(parseInt(m[0]));
}

/******************* HORS ABO **********************/
// load events hors abo
var arrOfEventsHA = []; // tableau avec manifs où se trouve le liens (places supplémentaires)
var arrOfEventsHAnonFormule = []; //tableau avec les manifs qui sont dans l'onglet (HORS ABO)
function LoadEventsHA(strFormuls) {
		
    $.ajax({
        type: "POST",
        url: urlajaxabov2 + '/HorsAbo/LoadEvents',
		data: {
            structureId: structureIdCurrent,
            identiteId: identiteIdCurrent,
            langCode: langCodeCurrent,
            listformulaId: strFormuls //"1:2:3"
        },
        success: function (retour) {
			
			//on regarde si les manifestations sont dans les abonnements alors on ne les ajoute pas
			arrOfEventsHAnonFormule = []
			$.each(retour, function(indx, itemevt)  {	
				var arr = Enumerable.From(events).Where(function (x) { return x.EventId == itemevt.EventId }).ToArray();	
				if(arr.length == 0)	
					arrOfEventsHAnonFormule.push(itemevt);		
				
			});
			arrOfEventsHA = retour;	
			//si il y a des manifestations hors abo alors on affiche l'onglet
			if(arrOfEventsHAnonFormule.length > 0 )
			{
							
				//<!-- tabhorsabo -->
                $('#content #myTab ').append('<li class="nav-item"> <a class="nav-link" id="horsabo-tab" data-toggle="tab" href="#tabhorsabo" role="tab" aria-controls="profile" aria-selected="false">' + ReadXmlTranslate("lbl_hors_abos_tab") + '</a> </li>');
				$ulNavTabs = $('<div class="tab-pane fade" id="tabhorsabo" role="tabpanel" aria-labelledby="horsabo-tab"> </div>');
				
				//START CONTENU
				$colOfContent = $('<div class="col"> </div>');
				
				//START INTRO
				$divIntro = $('<div class="tab-intro">	</div>');
				$aboTitle = $('<div class="abotitle"> </div>');					
                $aboTitle.append('<strong>' + ReadXmlTranslate("lbl_hors_abos_title") + '</strong> ');
				
				$colOfContent.append($aboTitle);
				
                $divIntro.append(' <p>' + ReadXmlTranslate("lbl_hors_abos_intro") + '</p>');
				$colOfContent.append($divIntro);				
				$aboitems = $('<div class="aboItems"> </div>');	

				$.each(arrOfEventsHAnonFormule, function(indx, item){					
					$aboitem = '<div class="aboItem"> <div class="row">';
					$aboitem += '<div class="col-12 col-md  align-self-center"> <div class="item-abospectacles"> '+item.EventName+'</div> <span class="SpectacleExtrasHorsAbo" data-eventid="'+item.EventId+'"></span></div>';
                    $aboitem += ' <div class="col-12 col-md-4 col-lg-3"> <a href="" data-href="' + urlajaxabov2 + '/HorsAbo/partIndex/' + structureIdCurrent + '/0/todo/' + item.EventId + '/0/0/' + langCodeCurrent + '" class="col btn btn-secondary" data-target="#modalGeneric" data-toggle="modal" data-type="placesupp" data-ishorsabo="true"  data-typeHorsAbo="HA" data-title="">' + ReadXmlTranslate("lbl_choose_event") + '</a> </div>';
					$aboitem += ' </div> </div>';
										
					$aboitems.append($aboitem);
				});
				$colOfContent.append($aboitems);
				$ulNavTabs.append($colOfContent);
				
				$('#myTabContent').append($ulNavTabs);
			}
		},
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(textStatus);
        },
		complete: function () {		
			var objSessionEventChoice = sessionStorage.getObj("objEventsChoice");
			
			var isClickedBtnValidAllForm = sessionStorage.getItem('isClickedBtnValidAllForm');
			if(settings.events.showMessageGetPreviousChoices && objSessionEventChoice != null && (isClickedBtnValidAllForm != null && isClickedBtnValidAllForm == "false"))
			{

				//show modal  (title, message, textbuttonaccept, textbuttonrefuse, idbuttonaccept, idbuttonrefuse)
				 var modalText =  ReadXmlTranslate("msg_alert_warning_get_previous_selection")
                ShowConfirmModal(ReadXmlTranslate("msg_alert_warning_title_get_previous_selection"), modalText, ReadXmlTranslate("msg_alert_warning_get_previous_selection_yes"), ReadXmlTranslate("msg_alert_warning_get_previous_selection_no"), 'getPreviousChoices', 'btnRemoveSession');

				BindButtonModal();
				
			}else{
				
				//récupère les choix sélectionnés lors du refresh de la page
				GetEventsChoice();
				ShowSeatsChoicesHA();
				ShowMiniBasket();
				
				//si on est identifié et que l'on a déjà cliqué et le bouton valider
				if(sessionStorage.getObj("myIdentity") != null && (isClickedBtnValidAllForm != null && isClickedBtnValidAllForm) )
				{
					$('#validAllForm').trigger('click');						
				}	
			}
			everyFormArecomplete();
		}
    });
}

function BindButtonModal(){
	
	$('#getPreviousChoices').on('click', function(e){
		e.preventDefault();
		
		ShowSeatsChoicesHA();
		//récupère les choix sélectionnés lors du refresh de la page
		GetEventsChoice();
		ShowMiniBasket();
			
	});
	
	$('#btnRemoveSession').on('click', function(e){
		e.preventDefault();
		
		var firstPage = localStorage.getItem('firstPage').replace(/\"/g, "") ;
			
		CancelAndDeflagsSeats();

        InvalidBasket();
        localStorage.removeItem('arrPanier');
        localStorage.removeItem('end_time_buy');
        localStorage.removeItem('end_time_select');
        localStorage.removeItem('isTimerBuy');
        localStorage.removeItem('start_time_buy');
        localStorage.removeItem('start_time_select');
		//supprime les places hors abo
        localStorage.removeItem('arrPanierHA');

        sessionStorage.removeItem('arrChoixTarifs');
        sessionStorage.removeItem('objEventsChoice');
        sessionStorage.removeItem('isClickedBtnValidAllForm');
		window.location.href=  firstPage;
	});
				
}


/********** Charge et Affiche les montants **************/
function LoadAmounts(eventid, sessionid, tarifid) {
	 var listT = tarifid+":";
	return LoadAmountsOfEvent(eventid, sessionid, listT);
        //    var $trOfE = $("<tr data-eventid='" + oevent.EventId + "'></tr>");
}

function LoadAmountsOfEvent(eventid, psessionid, listTarifs) {
    
	if(listTarifs != undefined)
	{
		var arrRetour = "";
		$.ajax({
			type: "POST",
			url: urlajaxabov2 + '/Events/LoadAmounts',
			async: false,
			data: {
				structureId: structureIdCurrent,
				identiteId: identiteIdCurrent,
				sessionid : psessionid,
				langCode: langCodeCurrent,
				eventid: eventid,
				listTarifs: listTarifs
			},
			success: function (retour) {
				arrRetour = retour;
				//DisplayAmounts(retour[0].EventId, formuleid, retour);
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				alert(textStatus);
			},
			complete: function () {
				//functioncomplete();
			}
		});
		
		return arrRetour;
	}
}


/********** FIN Charge et Affiche les montants **************/
var nf_done = 0;
var nf_todo = 0;

var arrOfEventsOfFormula = [];
var arrOfConstraintesOfFormula = [];

// appels ajax 
function LoadEvents(arrFormules) {

    nf_todo = arrFormules.length * 2;
    nf_done = 0;

    $.each(arrFormules, function (i, val) {
        $.ajax({
            type: "POST",
            url: urlajaxabov2 + '/events/Load',
            data: {
                structureId: structureIdCurrent,
                identiteId: identiteIdCurrent,
                langCode: langCodeCurrent,
                formulaId: val,
                tarifsId: "0"
            },
            success: function (retour) {
                //alert('events of ' + val + "=" + retour);
                //retour.formulaId = val;
                arrOfEventsOfFormula.push(retour);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert(textStatus);
            },
            complete: function () {
                functioncomplete();
            }
        });

        $.ajax({
            type: "POST",
            url: urlajaxabov2 + '/events/LoadConstraintes',
            'data': {
                structureId: structureIdCurrent,
                identiteId: identiteIdCurrent,
                langCode: langCodeCurrent,
                formulaId: val
            },
            success: function (retour) {
                //retour.formulaId = val;
                arrOfConstraintesOfFormula.push(retour);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert(textStatus);
            },
            complete: function () {
                functioncomplete();
            }
        });
    });
}

// affiche le choix précédent sur les tarifs Hors Abo
function ShowSeatsChoicesHA() {

    $('.divplacesupplementaire_').remove();
    var arrBasket = localStorage.getObj("arrPanierHA");

   // $('.sptarifsHA, .spdetailstarifsHA, .suppHA').remove();


    if (arrBasket != undefined) {
        if (arrBasket.listEventsHA != undefined && arrBasket.listEventsHA.length > 0) {
			
				//récupère les dans 2 tableaux les hors abo et hors abo avec formule 
				var arrHorsAboNonFormule = [];
				var arrHorsAboAvecFormule = [];				
				$.each(arrBasket.listEventsHA, function (i, oevent) {
					var arr = Enumerable.From(arrOfEventsHAnonFormule).Where(function (x) { return x.EventId == oevent.eventid }).ToArray();	
					if(arr.length == 0)	
					{
						//PS : Places supplémentaire
						oevent.type="PS";
						arrHorsAboAvecFormule.push(oevent);	
					}
					else
					{
						oevent.type="HA";
						arrHorsAboNonFormule.push(oevent);
					}
				});
			
				/****** INSERT LES HORS ABO SANS FORMULES ************/
				var divSelection = ''; 
				var eventidprevious = 0;
				
				
				var nbTotalHA = 0;
				$.each(arrHorsAboNonFormule, function (i, oevent) {
					//si on change de manif alors on reinitialise
					if(eventidprevious != oevent.eventid)
						divSelection = '';
					
					divSelection += '<div class="aboExtraplaces" data-eventid="'+oevent.eventid+'" data-zoneid="'+oevent.zoneid+'" data-categid="'+oevent.categid+'" data-sectionid="'+oevent.sectionid+'" data-sessionid="'+oevent.sessionid+'" data-floorid="'+oevent.floorid+'">';
				   
					$.each(oevent.listTarifs, function (ii, oTarif) {
						
						if(sessionStorage.getObj('previousBasket') != null)
						{
							nb = oTarif.nb;
							gpId = oTarif.gpid;
							amountU = oTarif.unitTTCamount;
							amountT = parseInt(amountU) * parseInt(nb);
							
							//divSelection += '<div class="aboExtraplace" data-tarifid="'+oTarif.priceid+'" data-eventid="'+oevent.eventid+'"><span class="item-'+oTarif.priceid +' abotarif">'+nb+' x '+oTarif.pricename+'</span> ';
							//divSelection +=' <span class="text-right aboprice">' + (parseInt(amountT) / 100).toFixed(2) + ' '+deviseCode+'</span> <span class="text-right aboremove"   ><i class="fa fa-trash" aria-hidden="true"></i></span></div>';

							divSelection += '<div class="aboExtraplace" data-tarifid="'+oTarif.priceid+'" data-eventid="'+oevent.eventid+'" data-categid="'+oevent.categid+'" data-sessionid="'+oevent.sessionid+'"><span class="item-'+oTarif.priceid +' abotarif" data-nb="'+nb+'">'+nb+' x '+oTarif.pricename+'</span> ';
							divSelection +=' <span class="text-right aboprice">' + (parseInt(amountT) / 100).toFixed(2) + ' '+deviseCode+'</span> <span class="text-right aboremove"><i class="fa fa-trash" aria-hidden="true"></i></span>';
							
							divSelection += '<div class="horsaboinfos">' +oevent.sessionDescription+ ' '+oevent.floorName +' '+oevent.sectionName +' '  +oevent.categoryName + ' ' + oevent.lieuName+'</div></div>';
						
							nbTotalHA += nb;
						}else{
							
							nb = oTarif.nb;
							gpId = oTarif.gpid;
							amountU = oTarif.unitTTCAmount;
							amountT = parseInt(amountU) * parseInt(nb);
							
							//divSelection += '<div class="aboExtraplace" data-tarifid="'+oTarif.priceid+'" data-eventid="'+oevent.eventid+'"><span class="item-'+oTarif.priceid +' abotarif">'+nb+' x '+oTarif.pricename+'</span> ';
							//divSelection +=' <span class="text-right aboprice">' + (parseInt(amountT) / 100).toFixed(2) + ' '+deviseCode+'</span> <span class="text-right aboremove"   ><i class="fa fa-trash" aria-hidden="true"></i></span></div>';

							divSelection += '<div class="aboExtraplace" data-tarifid="'+oTarif.priceid+'" data-eventid="'+oevent.eventid+'" data-categid="'+oevent.categid+'" data-sessionid="'+oevent.sessionid+'"><span class="item-'+oTarif.priceid +' abotarif" data-nb="'+nb+'">'+nb+' x '+oTarif.pricename+'</span> ';
							divSelection +=' <span class="text-right aboprice">' + (parseInt(amountT) / 100).toFixed(2) + ' '+deviseCode+'</span> <span class="text-right aboremove"><i class="fa fa-trash" aria-hidden="true"></i></span>';
							
							divSelection += '<div class="horsaboinfos">' +oevent.sessionDescription+ ' '+oevent.floorName +' '+oevent.sectionName +' '  +oevent.categoryName + ' ' + oevent.lieuName+'</div></div>';
						
							nbTotalHA += nb;
						}
						
						
					});
					 divSelection += '</div>';
					 
					 
					 
					 
					 if($('#horsabo-tab .badge').length == 0)
					{
						$('#horsabo-tab').append(' <span class="badge badge-secondary nbevent">'+nbTotalHA+'</span>');
					}else{
						$('#horsabo-tab .badge').html(' <span class="badge badge-secondary nbevent">'+nbTotalHA+'</span>');
					}
							
							
					$('.SpectacleExtrasHorsAbo[data-eventid="'+oevent.eventid+'"]').html(divSelection);
					
					eventidprevious = oevent.eventid;
				});

			
				/****** INSERT LES HORS ABO AVEC FORMULES ************/
				var $h3 = $('<div class="col-12"><h3>' + ReadXmlTranslate("lbl_places_supplementaires") + '</h3></div>');
				var divSelection = ''; 
				var lstHorsAboFormule = arrHorsAboAvecFormule;
				$.each(arrHorsAboAvecFormule, function (i, oevent) {
					
					/*
					//compte le nombre total de même categ sur l'evenement
					var lstEventsOfCategs = $.grep(lstHorsAboFormule, function(x){ return x.eventid == oevent.eventid &&  x.categid == oevent.categid });

					//ajout du badge avec le nombre de spectacle extras
					var nbtotal = 0;
					//compte le nombre pour chaque categ 
					 $.each(lstEventsOfCategs, function(indx, item) {
						$.grep(item.listTarifs, function(x) {         
							 return  nbtotal+=x.nb;
						});
					})

					$('.aboItem[data-eventid="'+oevent.eventid+'"][data-categid="'+oevent.categid+'"]').find('.badgeExtras').html('<a href="#spectacleExtras" class="badge badge-info"> +'+nbtotal+'</a>');
					*/
					
					ShowBadgesNbPlacesSupplementaires(oevent.eventid, oevent.categid)
					//FIN ajout du badge avec le nombre de spectacle extras
			
				   //var eventname = $($('.aboItem[data-eventid="'+oevent.eventid+'"]')[0]).data('eventname');
				   //var eventname = oevent.eventName
				   divSelection += '<div class="aboExtraplaces col-12 col-sm-6 col-md-4" data-eventid="'+oevent.eventid+'" data-zoneid="'+oevent.zoneid+'" data-categid="'+oevent.categid+'" data-sectionid="'+oevent.sectionid+'" data-floorid="'+oevent.floorid+'">';
				   divSelection += '<span class="abotitle">'+oevent.eventName+'</span>';
					$.each(oevent.listTarifs, function (ii, oTarif) {
						if(sessionStorage.getObj('previousBasket') != null)
						{
							nb = oTarif.nb;
							gpId = oTarif.gpid;
							amountU = oTarif.unitTTCamount;
							amountT = parseInt(amountU) * parseInt(nb);
							
							divSelection += '<div class="aboExtraplace" data-tarifid="'+oTarif.priceid+'" data-eventid="'+oevent.eventid+'" data-categid="'+oevent.categid+'" data-sessionid="'+oevent.sessionid+'" ><span class="item-'+oTarif.priceid +' abotarif">'+nb+' x '+oTarif.pricename+'</span> ';
							divSelection +=' <span class="text-right aboprice">' + (parseInt(amountT) / 100).toFixed(2) + ' '+deviseCode+'</span> <span class="text-right aboremove"><i class="fa fa-trash" aria-hidden="true"></i></span>';
							divSelection += '<div class="horsaboinfos">' +oevent.sessionDescription+ '</div></div>';  //'+oevent.floorName +' '+oevent.sectionName +' '  +oevent.categoryName + ' ' + oevent.lieuName+'</div></div>';
						}else {
							nb = oTarif.nb;
							gpId = oTarif.gpid;
							amountU = oTarif.unitTTCAmount;
							amountT = parseInt(amountU) * parseInt(nb);
							
							divSelection += '<div class="aboExtraplace" data-tarifid="'+oTarif.priceid+'" data-eventid="'+oevent.eventid+'" data-categid="'+oevent.categid+'" data-sessionid="'+oevent.sessionid+'" ><span class="item-'+oTarif.priceid +' abotarif">'+nb+' x '+oTarif.pricename+'</span> ';
							divSelection +=' <span class="text-right aboprice">' + (parseInt(amountT) / 100).toFixed(2) + ' '+deviseCode+'</span> <span class="text-right aboremove"><i class="fa fa-trash" aria-hidden="true"></i></span>';
							//divSelection += '<div class="horsaboinfos">' +thissession[0].sSessionStartDate+ ' ' +oevent.categoryName + ' ' + oevent.lieuName+'</div>';
							divSelection += '<div class="horsaboinfos">' +oevent.sessionDescription+ ' '+oevent.floorName +' '+oevent.sectionName +' '  +oevent.categoryName + ' ' + oevent.lieuName+'</div></div>';
							//divSelection.append('<span class="item-'+$( this).attr("id")+'">'+nb+' '+oTarif.pricename+'</span>');
						}
					});
					 divSelection += '</div>';
					 
				});
				
				 $('#spectacleExtras').html(divSelection);
				 
				 //insert le titre que sur le premier
				$h3.insertBefore('#spectacleExtras .aboExtraplaces:first');
				 
				/* $.each(arrHorsAboAvecFormule, function (i, oevent) {
					
					ShowBadgesNbPlacesSupplementaires(oevent.eventid, oevent.categid)
					
				 });*/
				 
				 
				//si le titre était caché
				if($('#spectacleExtras').hasClass('d-none'))
					$('#spectacleExtras').removeClass('d-none')
			//}
			  
            $('span.aboremove').on("click", function (e) {
				console.log('remove');
				//todo supprimer que la ligne avec la categ tarif, zone, etage etc...
                var thiseventid, thissessionid = 0;
				
				//si on supprime directement les places supplémentaires
				if($(this).closest('.aboExtraplaces').data('eventid') != undefined)
				{
					thiseventid = $(this).closest('.aboExtraplaces').data('eventid');
					thissessionid = $(this).closest('.aboExtraplaces').find('.aboExtraplace[data-eventid="'+thiseventid+'"]').data('sessionid')
					unFlag(thiseventid, thissessionid);
					DeleteEventBasket($(this), thiseventid, true);
					
					
					var nb = $(this).closest('.aboExtraplace').find('.abotarif').data('nb');
					var actualNb= parseInt($($('#horsabo-tab .badge')[0]).text());
					 if($('#horsabo-tab .badge').length == 0)
					{
						
						$('#horsabo-tab').append(' <span class="badge badge-secondary nbevent">'+(actualNb - nb)+'</span>');
					}else{
						$('#horsabo-tab .badge').html(' <span class="badge badge-secondary nbevent">'+(actualNb - nb)+'</span>');
					}
					
				}else if($(this).closest('.aboItem').data('eventid')!= undefined) //si on supprime depuis la poubelle de l'abonné (aboItem)
				{
					thiseventid = $(this).closest('.aboItem').data('eventid');
					thissessionid = $(this).closest('.aboExtraplaces').find('.aboExtraplace[data-eventid="'+thiseventid+'"]').data('sessionid')
					unFlag(thiseventid, thissessionid);
					DeleteEventBasket($(this), thiseventid, false);				
					
				}
            });

        }
    }

	//afiche le mini panier
	ShowMiniBasket();
}


//supprime les lignes places supplémentaires
function DeleteEventBasket(thisobject, eventidtodelete, ishorsaboevent){
	var thistarifidtodelete = $(thisobject).closest('.aboExtraplace').data('tarifid')
	var thiscategidtodelete = $(thisobject).closest('.aboExtraplaces').data('categid')
	var thisfloorid = $(thisobject).closest('.aboExtraplaces').data('floorid');
	var thiszoneid = $(thisobject).closest('.aboExtraplaces').data('zoneid');
	var thissectionid = $(thisobject).closest('.aboExtraplaces').data('sectionid');
	

	var arrBasketHA = localStorage.getObj("arrPanierHA");
	
	var thisarrcateg = $.grep(arrBasketHA.listEventsHA, function(x){					
		return x.eventid == eventidtodelete && x.zoneid  == thiszoneid && x.floorid == thisfloorid && x.sectionid == thissectionid && x.categid == thiscategidtodelete;
	});
	
	if(thisarrcateg.length > 0 && thisarrcateg[0].listTarifs.length > 1)
	{
		//plusieurs tarifs		
		 thisarrcateg[0].listTarifs = $.grep(thisarrcateg[0].listTarifs, function(tar){ return tar.priceid != thistarifidtodelete });			
		 //supprime la ligne du tarif
		$(thisobject).closest('.aboExtraplace').remove();
	}else{
		//supprime la categ
		arrBasketHA.listEventsHA = $.grep(arrBasketHA.listEventsHA, function(x){					
			return x != thisarrcateg[0];
		});
		
		//supprime la ligne du tarif
		$(thisobject).closest('.aboExtraplaces[data-zoneid="'+thiszoneid+'"][data-sectionid="'+thissectionid+'"][data-floorid="'+thisfloorid+'"][data-categid="'+thiscategidtodelete+'"]').remove();
	}
	
	localStorage.setObj("arrPanierHA", arrBasketHA);			
	
	ShowBadgesNbPlacesSupplementaires(eventidtodelete, thiscategidtodelete);
	if(arrBasketHA.listEventsHA.length == 0)				
		$('#spectacleExtras').html('');
	
}


function ShowBadgesNbPlacesSupplementaires(eventid, thiscategid){
	
	var nbThistarif = 0;
	var arrBasketHA = localStorage.getObj("arrPanierHA");
	//sélectionne la manif pour avoir le nombre de tarif
	var arrTarifOfThisEvent = $.grep(arrBasketHA.listEventsHA, function(x){					
		return x.eventid == eventid && x.categid == thiscategid
	});
			
	$.each(arrTarifOfThisEvent, function (ii, oTarif) {		
		$.each(oTarif.listTarifs, function (ii, oTarif) {		
			nbThistarif += oTarif.nb;
		})		
	})
	
	if(nbThistarif ==0) //supprime les lignes et vide le html du badge
		$('.aboItem[data-eventid="'+eventid+'"][data-categid="'+thiscategid+'"]').find('.rowspectacleextra .badgeExtras').html('');	
	else  // affiche le nombre de places supplémentaires 
		$('.aboItem[data-eventid="'+eventid+'"][data-categid="'+thiscategid+'"]').find('.rowspectacleextra .badgeExtras').html('<a href="#spectacleExtras" class="badge badge-info "> +'+nbThistarif+'</a>');			
}


/******************* FIN HORS ABO **********************/
// check si tous les appels ont répondus
function functioncomplete() {
    nf_done++;
    if (nf_done == nf_todo) {
        ajaxIsComplete();
    }
}


